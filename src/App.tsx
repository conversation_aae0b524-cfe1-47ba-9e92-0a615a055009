import { BrowserRouter } from 'react-router-dom';

import AuthProvider from 'src/providers/AuthProvider';
import { ToastProvider } from 'src/providers/ToastProvider';

import Routes from './routes/Routes';
import ThemeProvider from './theme/ThemeProvider';

function App() {
  return (
    <BrowserRouter>
      <ThemeProvider>
        <ToastProvider>
          <AuthProvider>
            <Routes />
          </AuthProvider>
        </ToastProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
}

export default App;
