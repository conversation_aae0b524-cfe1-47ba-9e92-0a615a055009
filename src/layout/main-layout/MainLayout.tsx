import { Outlet } from 'react-router';

import { useResponsive } from 'src/hooks/use-responsive';

import SidebarNavigation from 'src/components/common/navigation/sidebar-navigation/SidebarNavigation';

import { MainLayoutContent, MainLayoutWrapper } from './MainLayout.style';

const MainLayout = () => {
  const isDesktop = useResponsive('up', 'md');
  return (
    <MainLayoutWrapper>
      <SidebarNavigation />

      <MainLayoutContent>
        {!isDesktop && <>Mobile view here</>}
        <Outlet />
      </MainLayoutContent>
    </MainLayoutWrapper>
  );
};

export default MainLayout;
