import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

export const AuthLayoutWrapper = styled('div')(({ theme }) => ({
  display: 'grid',
  height: '100vh',
  gridTemplateColumns: '1fr 1fr',
  overflow: 'hidden',
  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: '1fr',
  },
}));

export const AuthLayoutLeftView = styled('div')(({ theme }) => ({
  display: 'flex',
  height: '100%',
  backgroundColor: theme.palette.background.default,
  borderRight: `1px solid ${theme.palette.divider}`,
}));

export const AuthLayoutContentWrapper = styled('div')(() => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  width: 'min(100% - 2rem, 450px)',

  margin: 'auto',
}));

export const AuthLayoutCompanyLogo = styled('img')(({ theme }) => ({
  width: 'auto',
  marginBottom: theme.spacing(3),
  objectFit: 'contain',
  maxWidth: '150px',
}));

export const AuthLayoutLeftViewTitle = styled(Typography)(({ theme }) => ({
  fontWeight: theme.typography.fontWeightBold,
  marginBottom: theme.spacing(6),
  color: theme.palette.text.primary,
  width: '100%',
}));

export const AuthLayoutRightView = styled('div')(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '100%',
  backgroundColor: theme.palette.background.neutral,
  [theme.breakpoints.down('md')]: {
    display: 'none',
  },
}));

export const AuthLayoutRightViewBackground = styled('img')(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  objectFit: 'cover',
}));

export const AuthLayoutRightViewImage = styled('img')(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(0),
  right: theme.spacing(0),
  width: '85%',
  height: 'auto',
  zIndex: 1,
  filter: 'drop-shadow(0px 24px 48px rgba(0, 0, 0, 0.1))',
  transition: 'all 0.3s ease-in-out',
}));
