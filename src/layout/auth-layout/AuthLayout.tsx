import { Outlet } from 'react-router-dom';

import {
  AuthLayoutWrapper,
  AuthLayoutLeftView,
  AuthLayoutRightView,
  AuthLayoutCompanyLogo,
  AuthLayoutLeftViewTitle,
  AuthLayoutRightViewImage,
  AuthLayoutContentWrapper,
  AuthLayoutRightViewBackground,
} from './AuthLayout.style';

const authLayoutData = {
  companyLogo: '/assets/nexmove-logo.png',
  businessName: 'Business Management Tool',
  backgroundImageSrc: '/assets/auth-background.webp',
  mainImageSrc: '/assets/auth-main.webp',
  altData: 'auth-layout-illustration',
};

const AuthLayout = () => (
  <AuthLayoutWrapper>
    <AuthLayoutLeftView>
      <AuthLayoutContentWrapper>
        <AuthLayoutCompanyLogo src={authLayoutData.companyLogo} alt="company-logo" />
        <AuthLayoutLeftViewTitle variant="h5">
          {authLayoutData.businessName}
        </AuthLayoutLeftViewTitle>
        <Outlet />
      </AuthLayoutContentWrapper>
    </AuthLayoutLeftView>

    <AuthLayoutRightView>
      <AuthLayoutRightViewBackground src={authLayoutData.backgroundImageSrc} alt="background" />
      <AuthLayoutRightViewImage src={authLayoutData.mainImageSrc} alt={authLayoutData.altData} />
    </AuthLayoutRightView>
  </AuthLayoutWrapper>
);

export default AuthLayout;
