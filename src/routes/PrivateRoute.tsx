import type { ReactNode } from 'react';

import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { setupInterceptors } from 'src/utils/axios';

import { useAuth } from 'src/providers/AuthProvider';

import { paths } from './paths';

export interface ProtectedRouteProps {
  children: ReactNode;
}

const PrivateRoute = ({ children }: ProtectedRouteProps) => {
  const { token, logout } = useAuth();
  const location = useLocation();

  useEffect(() => {
    setupInterceptors(logout);
  }, [logout]);

  if (!token) {
    return <Navigate to={paths.auth.login} state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default PrivateRoute;
