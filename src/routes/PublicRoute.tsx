import type { ReactNode } from 'react';

import { Navigate, useLocation } from 'react-router-dom';

import { useAuth } from 'src/providers/AuthProvider';

import { paths } from './paths';

export interface PublicRouteProps {
  children: ReactNode;
}

const PublicRoute = ({ children }: PublicRouteProps) => {
  const { token } = useAuth();
  const location = useLocation();

  const from = location.state?.from?.pathname || paths.home;

  if (token) {
    return <Navigate to={from} />;
  }

  return <>{children}</>;
};

export default PublicRoute;
