import { Route, Navigate, Routes as Router } from 'react-router-dom';

import PublicRoute from 'src/routes/PublicRoute';
import PrivateRoute from 'src/routes/PrivateRoute';

import Layout from 'src/layout/Layout';
import TestPage from 'src/pages/TestPage';
import TrackingPage from 'src/pages/tracking-page';
import { Home } from 'src/pages/home-page/HomePage';
import MarketPlacePage from 'src/pages/market-place-page';
import AuthLayout from 'src/layout/auth-layout/AuthLayout';
import QuoteRequestPage from 'src/pages/quote-request-page';
import InstantQuotePage from 'src/pages/instant-quote-page';
import GalleryPage from 'src/pages/gallery-page/GalleryPage';
import PaymentRequestPage from 'src/pages/payment-request-page';
import { LoginPage } from 'src/pages/auth-page/login-page/LoginPage';
import NotFoundPage from 'src/pages/errors/not-found-page/NotFoundPage';
import TestimonialPage from 'src/pages/testimonial-page/TestimonialPage';
import JobPostingsPage from 'src/pages/job-posting-page/JobPostingsPage';
import { JobPositionPage } from 'src/pages/job-position-page/JobPositionPage';
import JobApplicationPage from 'src/pages/job-application-page/JobApplicationPage';
import ContactRequestPage from 'src/pages/contact-requests-page/ContactRequestPage';
import TrackingDetailsPage from 'src/pages/tracking-details-page/TrackingDetailsPage';
import JobPostingSearchPage from 'src/pages/job-posting-search-page/JobPostingSearchPage';
import { ResetPasswordPage } from 'src/pages/auth-page/reset-password-page/ResetPasswordPage';
import JobPostingsDetailsPage from 'src/pages/job-postings-details-page/JobPostingsDetailsPage';
import { ForgotPasswordPage } from 'src/pages/auth-page/forgot-password-page/ForgotPasswordPage';
import { CreatePasswordPage } from 'src/pages/auth-page/create-password-page/CreatePasswordPage';
import QuoteRequestDetailsPage from 'src/pages/quote-request-details-page/QuoteRequestDetailsPage';
import InstantQuoteDetailsPage from 'src/pages/instant-quote-details-page/InstantQuoteDetailsPage';
import { JobPositionDetailsPage } from 'src/pages/job-position-details-page/JobPositionDetailsPage';
import ContactRequestDetailsPage from 'src/pages/contact-request-details-page/ContactRequestDetailsPage';
import JobApplicationDetailsPage from 'src/pages/job-application-details-page/JobApplicationDetailsPage';

const Routes = () => (
  <Router>
    <Route
      path="/auth"
      element={
        <PublicRoute>
          <AuthLayout />
        </PublicRoute>
      }
    >
      <Route index element={<Navigate to="signin" />} />
      <Route path="signin" element={<LoginPage />} />
      <Route path="forgot-password" element={<ForgotPasswordPage />} />
      <Route path="reset-password" element={<ResetPasswordPage />} />
      <Route path="create-password" element={<CreatePasswordPage />} />
    </Route>

    <Route
      path="/"
      element={
        <PrivateRoute>
          <Layout />
        </PrivateRoute>
      }
    >
      <Route index element={<Home />} />
      <Route path="/home" element={<Home />} />
      <Route path="contact-requests" element={<ContactRequestPage />} />
      <Route path="contact-requests/:contactId" element={<ContactRequestDetailsPage />} />
      <Route path="quote-requests" element={<QuoteRequestPage />} />
      <Route path="quote-requests/:quoteId" element={<QuoteRequestDetailsPage />} />
      <Route path="instant-quotes" element={<InstantQuotePage />} />
      <Route path="instant-quotes/:instantQuoteId" element={<InstantQuoteDetailsPage />} />
      <Route path="careers/positions" element={<JobPositionPage />} />
      <Route path="careers/positions/:positionId" element={<JobPositionDetailsPage />} />
      <Route path="careers/postings" element={<JobPostingsPage />} />
      <Route path="careers/postings/new" element={<JobPostingSearchPage />} />
      <Route path="careers/postings/:postingId" element={<JobPostingsDetailsPage />} />
      <Route path="careers/applications" element={<JobApplicationPage />} />
      <Route path="careers/applications/:applicationId" element={<JobApplicationDetailsPage />} />
      <Route path="payment-requests" element={<PaymentRequestPage />} />
      <Route path="shipment-tracking/:shipmentId" element={<TrackingDetailsPage />} />
      <Route path="shipment-tracking" element={<TrackingPage />} />
      <Route path="market-place" element={<MarketPlacePage />} />
      <Route path="cms/gallery" element={<GalleryPage />} />
      <Route path="cms/testimonials" element={<TestimonialPage />} />
      <Route path="test" element={<TestPage />} />
    </Route>

    <Route path="*" element={<NotFoundPage />} />
  </Router>
);

export default Routes;
