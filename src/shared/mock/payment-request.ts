import type { ddValue } from 'src/shared/types/ddValue';
import type { PaymentRequest } from 'src/shared/services/payment-request/payment-request.type';

export const PaymentRequestStatus: ddValue[] = [
  {
    id: 1,
    name: 'Active',
    isDisabled: false,
  },
  {
    id: 2,
    name: 'Paid',
    isDisabled: false,
  },
  {
    id: 3,
    name: 'Declined',
    isDisabled: false,
  },
  {
    id: 4,
    name: 'Expired',
    isDisabled: false,
  },
];

export const generateMockPaymentRequests = (count: number): PaymentRequest[] =>
  Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    customerName: `Customer ${index + 1}`,
    date: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
    paymentId: `PAY-${String(index + 1).padStart(6, '0')}`,
    contactNumber: `98765432${String(index).padStart(2, '0')}`,
    city: `City ${index + 1}`,
    status: PaymentRequestStatus[index % PaymentRequestStatus.length],
    service: `Service ${index + 1}`,
    subCategory: `Sub Category ${index + 1}`,
    amount: 1000 + index * 100,
    destination: `Destination ${index + 1}`,
    invoice:
      index % 3 === 0
        ? {
            id: `inv-${index}`,
            fileName: `invoice-${index}.pdf`,
            fileUrl: `https://example.com/invoices/invoice-${index}.pdf`,
            uploadedAt: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
          }
        : undefined,
    action: {
      adminId: Math.random() < 0.5 ? null : 21,
      action: PaymentRequestStatus[0],
      remark: `Remark ${index + 1}`,
    },
  }));
