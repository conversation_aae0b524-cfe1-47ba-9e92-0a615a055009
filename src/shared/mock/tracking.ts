import type { ddValue } from 'src/shared/types/ddValue';
import type { PickUpOrDropOff, TrackingShipment } from 'src/shared/services/tracking/tracking.type';

export const TrackingServiceCategories: ddValue[] = [
  { id: 1, name: 'Premium Express', isDisabled: false },
  { id: 2, name: 'Air Express', isDisabled: false },
  { id: 3, name: 'Ground Express', isDisabled: false },
  { id: 4, name: 'Same Day Delivery', isDisabled: false },
];

export const TrackingStatus: ddValue[] = [
  { id: 1, name: 'In Transit', isDisabled: false },
  { id: 2, name: 'Delivered', isDisabled: false },
  { id: 3, name: 'Pending', isDisabled: false },
  { id: 4, name: 'Out for Delivery', isDisabled: false },
  { id: 5, name: 'Processing', isDisabled: false },
  { id: 6, name: 'Picked Up', isDisabled: false },
];

export const TrackingParticulars: ddValue[] = [
  { id: 1, name: 'Fragile', isDisabled: false },
  { id: 2, name: 'Documents', isDisabled: false },
  { id: 3, name: 'Electronics', isDisabled: false },
  { id: 4, name: 'Clothing', isDisabled: false },
  { id: 5, name: 'Books', isDisabled: false },
];

export const RiskChargesTypes: ddValue[] = [
  { id: 1, name: 'Standard', isDisabled: false },
  { id: 2, name: 'High Risk', isDisabled: false },
  { id: 3, name: 'Insurance Required', isDisabled: false },
];

export const ContentTypes: ddValue[] = [
  { id: 1, name: 'Package', isDisabled: false },
  { id: 2, name: 'Document', isDisabled: false },
  { id: 3, name: 'Box', isDisabled: false },
  { id: 4, name: 'Envelope', isDisabled: false },
];

export const DropOffLocationTypes: ddValue[] = [
  { id: 1, name: 'Home', isDisabled: false },
  { id: 2, name: 'Office', isDisabled: false },
  { id: 3, name: 'Depot', isDisabled: false },
  { id: 4, name: 'Collection Point', isDisabled: false },
];

const generateRandomPincode = (): string => Math.floor(100000 + Math.random() * 900000).toString();

const getRandomElement = <T>(array: T[]): T => array[Math.floor(Math.random() * array.length)];

const getRandomElements = <T>(array: T[], count: number = 1): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, array.length));
};

const firstNames = [
  'John',
  'Jane',
  'Michael',
  'Sarah',
  'David',
  'Emily',
  'Robert',
  'Lisa',
  'James',
  'Mary',
];
const lastNames = [
  'Smith',
  'Johnson',
  'Williams',
  'Brown',
  'Jones',
  'Garcia',
  'Miller',
  'Davis',
  'Rodriguez',
  'Martinez',
];
const cities = [
  'Mumbai',
  'Delhi',
  'Bangalore',
  'Chennai',
  'Kolkata',
  'Hyderabad',
  'Pune',
  'Ahmedabad',
  'Jaipur',
  'Surat',
];
const states = [
  'Maharashtra',
  'Delhi',
  'Karnataka',
  'Tamil Nadu',
  'West Bengal',
  'Telangana',
  'Gujarat',
  'Rajasthan',
];

export const generateMockTrackingData = (count: number): TrackingShipment[] =>
  Array.from({ length: count }, (_, index) => {
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    const customerName = `${firstName} ${lastName}`;
    const originCity = getRandomElement(cities);
    const destinationCity = getRandomElement(cities);
    const awbNumber = `AWB${String(Date.now() + index).slice(-8)}`;

    const createdDate = new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000));
    const updatedDate = new Date(
      createdDate.getTime() + Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)
    );

    return {
      id: index + 1,
      awbNumber,
      particulars: getRandomElements(TrackingParticulars, Math.floor(Math.random() * 2) + 1),
      status: [getRandomElement(TrackingStatus)],
      createdAt: createdDate.toISOString(),
      updatedAt: updatedDate.toISOString(),
      originLocation: originCity,
      destinationLocation: destinationCity,
      originPincode: generateRandomPincode(),
      destinationPincode: generateRandomPincode(),
      serviceCategory: [getRandomElement(TrackingServiceCategories)],
      isShipmentAtRisk: Math.random() > 0.8,
      riskChargesType: [getRandomElement(RiskChargesTypes)],
      contentType: [getRandomElement(ContentTypes)],
      contentDescription: `${getRandomElement(['Important', 'Fragile', 'Standard'])} ${getRandomElement(['documents', 'package', 'goods'])}`,
      weight: Math.floor(Math.random() * 50) + 1,
      weightUnit: 'kg',
      length: Math.floor(Math.random() * 100) + 10,
      width: Math.floor(Math.random() * 100) + 10,
      height: Math.floor(Math.random() * 50) + 5,
      dimensionsUnit: 'cm',
      shipmentValue: Math.floor(Math.random() * 50000) + 1000,
      customerName,
      customerEmailId: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
      customerPhoneNumber: `+91${Math.floor(********** + Math.random() * **********)}`,
      customerAddressLine1: `${Math.floor(Math.random() * 999) + 1} ${getRandomElement(['MG Road', 'Park Street', 'Main Street', 'Commercial Street'])}`,
      customerAddressLine2: `${getRandomElement(['Sector', 'Block', 'Area'])} ${Math.floor(Math.random() * 50) + 1}`,
      customerCity: destinationCity,
      customerState: getRandomElement(states),
      customerCountry: 'India',
      customerPincode: generateRandomPincode(),
      isPickupOrDropOff:
        Math.random() > 0.5 ? ('PICKUP' as PickUpOrDropOff) : ('DROP_OFF' as PickUpOrDropOff),
      pickupLocation: originCity,
      pickupAddressLine1: `${Math.floor(Math.random() * 999) + 1} ${getRandomElement(['Industrial Area', 'Business Park', 'Warehouse Complex'])}`,
      pickupAddressLine2: `${getRandomElement(['Unit', 'Building', 'Floor'])} ${Math.floor(Math.random() * 20) + 1}`,
      pickupCity: originCity,
      pickupState: getRandomElement(states),
      pickupCountry: 'India',
      pickupPincode: generateRandomPincode(),
      dropOffLocationType: [getRandomElement(DropOffLocationTypes)],
      shipmentImageUrl: `https://via.placeholder.com/300x200?text=Shipment+${index + 1}`,
      shipmentPrice: Math.floor(Math.random() * 5000) + 500,
    };
  });
