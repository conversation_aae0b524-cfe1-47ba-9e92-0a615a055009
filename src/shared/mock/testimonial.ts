import type { ddValue } from 'src/shared/types/ddValue';
import type { Testimonial } from 'src/shared/services/testimonial/testimonial.type';

export const TestimonialTypes: ddValue[] = [
  {
    id: 1,
    name: 'Customer',
    isDisabled: false,
  },
  {
    id: 2,
    name: 'Employee',
    isDisabled: false,
  },
];

export const generateMockTestimonials = (count: number): Testimonial[] =>
  Array.from({ length: count }, (_, index): Testimonial => {
    const type: ddValue = TestimonialTypes[index % TestimonialTypes.length];

    return {
      id: index + 1,
      name: `Client ${index + 1}`,
      company: `Company ${index + 1}`,
      rating: Math.floor(Math.random() * 3) + 3,
      text: `This is a testimonial from client ${index + 1}. The service was excellent and I would highly recommend it to others. The team was professional and responsive to all our needs.`,
      type,
    };
  });
