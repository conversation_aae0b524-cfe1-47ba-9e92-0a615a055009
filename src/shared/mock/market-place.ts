import type { ddValue } from 'src/shared/types/ddValue';
import type { MarketPlace } from 'src/shared/services/market-place/market-place.type';

export const MarketPlaceStatus: ddValue[] = [
  {
    id: 1,
    name: 'New',
    isDisabled: false,
  },
  {
    id: 2,
    name: 'Assigned',
    isDisabled: false,
  },
  {
    id: 3,
    name: 'Sales Closer',
    isDisabled: false,
  },
  {
    id: 4,
    name: 'Declined',
    isDisabled: false,
  },
];

export const generateMockMarketPlaces = (count: number): MarketPlace[] =>
  Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `Seller ${index + 1}`,
    contactNumber: `98765432${String(index).padStart(2, '0')}`,
    email: `seller${index + 1}@example.com`,
    category: `Category ${index + 1}`,
    location: `City ${index + 1}`,
    status: MarketPlaceStatus[index % MarketPlaceStatus.length],
    address: `Street ${index + 1}, Area ${index + 1}, City ${index + 1}`,
    title: `Item Title ${index + 1}`,
    expectingPrice: 1000 + index * 500,
    description: `Detailed description for item ${index + 1}`,
    message: `Additional message for item ${index + 1}`,
    images: [`https://example.com/image${index}_1.jpg`, `https://example.com/image${index}_2.jpg`],
    action: {
      adminId: Math.random() < 0.5 ? null : 21,
      action: MarketPlaceStatus[0],
      remark: `Remark ${index + 1}`,
    },
  }));
