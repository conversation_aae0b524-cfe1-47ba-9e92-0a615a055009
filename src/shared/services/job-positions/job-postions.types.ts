import type { ddValue } from 'src/shared/types/ddValue';

export type JobPosition = {
  id: number;
  departmentId: number;
  positionTitle: string;
  jobPositionTypeId: number;
  jobPositionStatusId: number;
  jobDescription: string;
  jobRequirements: string;
  jobCode: string;
  lastModified: string;
  createdDate: string;
  createdById: number;
  lastModifiedById: number;
  createdByUserName?: string | null;
  lastModifiedByUserName?: string | null;
  department?: ddValue | null;
  jobPositionType?: ddValue | null;
  jobPositionStatus?: ddValue | null;
};
