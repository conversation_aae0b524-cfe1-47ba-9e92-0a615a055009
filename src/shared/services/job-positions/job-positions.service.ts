import type { AxiosResponse } from 'axios';
import type { ddValue } from 'src/shared/types/ddValue';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';

import axios from 'src/utils/axios';

import type { JobPosition } from './job-postions.types';

export async function searchJobPositions(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<JobPosition>> {
  try {
    const response: AxiosResponse<SearchResponse<JobPosition>> = await axios.post(
      '/api/JobPositions/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to search Job positions: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobPositionById(id: number): Promise<JobPosition> {
  try {
    const response: AxiosResponse<JobPosition> = await axios.get(`/api/JobPositions/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to get job position by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobPositions(): Promise<JobPosition[]> {
  try {
    const response: AxiosResponse<JobPosition[]> = await axios.get(`/api/JobPositions`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to get job positions: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createJobPosition(postData: Partial<JobPosition>): Promise<JobPosition> {
  try {
    const response: AxiosResponse<JobPosition> = await axios.post(`/api/JobPositions`, postData);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to create job position: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateJobPosition(postData: Partial<JobPosition>): Promise<JobPosition> {
  try {
    const response: AxiosResponse<JobPosition> = await axios.put(
      `/api/JobPositions/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to update job position: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobPositionStatusOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(
      `/api/MasterData/jobPositionStatuses`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch job position status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobPositionTypeOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/jobPositionTypes`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch job position type options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getDepartmentOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/departments`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch department options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteJobPosition(id: number): Promise<void> {
  try {
    await axios.delete(`/api/JobPositions/${id}`);
  } catch (error) {
    throw new Error(
      `Failed to delete job position: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
