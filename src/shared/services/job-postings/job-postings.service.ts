import type { AxiosResponse } from 'axios';
import type { ddValue } from 'src/shared/types/ddValue';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';

import axios from 'src/utils/axios';

export async function searchJobPostings(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<JobPosting>> {
  try {
    const response: AxiosResponse<SearchResponse<JobPosting>> = await axios.post(
      '/api/JobPostings/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to search job postings: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobPostingById(id: number): Promise<JobPosting> {
  try {
    const response: AxiosResponse<JobPosting> = await axios.get(`/api/JobPostings/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to get job posting by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createJobPosting(data: Partial<JobPosting>): Promise<JobPosting> {
  try {
    const response: AxiosResponse<JobPosting> = await axios.post('/api/JobPostings', data);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to create job posting: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateJobPosting(postData: Partial<JobPosting>): Promise<JobPosting> {
  try {
    const response: AxiosResponse<JobPosting> = await axios.put(
      `/api/JobPostings/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to update job posting: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteJobPosting(id: number): Promise<void> {
  try {
    await axios.delete(`/api/JobPostings/${id}`);
  } catch (error) {
    throw new Error(
      `Failed to delete job posting: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobPostingsStatusOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(
      '/api/MasterData/JobPostingStatuses'
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch job posting status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getDepartmentOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get('/api/MasterData/Departments');
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch department options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
