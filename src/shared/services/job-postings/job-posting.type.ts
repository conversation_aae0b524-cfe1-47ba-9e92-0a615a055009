import type { ddValue } from 'src/shared/types/ddValue';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

export interface JobPosting {
  id: number;
  jobPositionId: number;
  jobPostingStatusId: number;
  jobPostingTitle: string;
  jobPostingDescription?: string;
  jobPostingRequirements?: string;
  minimumExperienceInYears: number;
  jobLocations: string[];
  lastModified: string;
  createdDate: string;
  createdById: number;
  lastModifiedById: number;
  shouldKeepOpenUntilStopped: boolean;
  createdByUserName?: string;
  lastModifiedByUserName?: string;
  schedulePostingStartDate?: string;
  schedulePostingEndTime?: string;
  jobPosition?: JobPosition | null;
  jobPostingStatus?: ddValue | null;
}
