import type { AxiosResponse } from 'axios';

import axios from 'src/utils/axios';

import type { AuditLogEntry, SearchLogCriteria } from './activity-logs.type';

export async function searchActivityLogs(
  searchCriteria: SearchLogCriteria
): Promise<AuditLogEntry[]> {
  try {
    const response: AxiosResponse<AuditLogEntry[]> = await axios.post(
      '/api/logs/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch activity logs: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
