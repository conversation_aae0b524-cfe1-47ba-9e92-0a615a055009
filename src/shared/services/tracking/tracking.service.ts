import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';

import {
  TrackingStatus,
  generateMockTrackingData,
  TrackingServiceCategories,
} from 'src/shared/mock/tracking';

import type { TrackingShipment } from './tracking.type';

const allTrackingData = generateMockTrackingData(50);

export async function searchTracking(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<TrackingShipment>> {
  try {
    let filteredTracking = allTrackingData.filter(
      (tracking) =>
        tracking.customerName.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        tracking.customerEmailId.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        tracking.customerPhoneNumber
          .toLowerCase()
          .includes(searchCriteria.searchKey.toLowerCase()) ||
        tracking.awbNumber.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        tracking.originPincode.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        tracking.destinationPincode.toLowerCase().includes(searchCriteria.searchKey.toLowerCase())
    );

    const { filters } = searchCriteria;
    filteredTracking = filteredTracking.filter((tracking) =>
      filters.every((filter) => {
        if (filter.field === 'status') {
          return tracking.status.some((status) => status.id === Number(filter.value));
        }
        if (filter.field === 'serviceCategory') {
          return tracking.serviceCategory.some((category) => category.id === Number(filter.value));
        }
        return true;
      })
    );

    const { pageNumber, pageSize } = searchCriteria.pagination;
    const start = (pageNumber - 1) * pageSize;
    const paginatedTracking = filteredTracking.slice(start, start + pageSize);

    const response: SearchResponse<TrackingShipment> = {
      count: filteredTracking.length,
      searchResult: paginatedTracking,
    };

    await new Promise((resolve) => setTimeout(resolve, 500));
    return response;
  } catch (error) {
    throw new Error(
      `Failed to search tracking data: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getTrackingById(id: number): Promise<TrackingShipment> {
  try {
    const tracking = allTrackingData.find((item) => item.id === id);
    if (!tracking) {
      throw new Error('Tracking data not found');
    }
    await new Promise((resolve) => setTimeout(resolve, 500));
    return tracking;
  } catch (error) {
    throw new Error(
      `Failed to get tracking data by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateTracking(
  id: number,
  updatedData: Partial<TrackingShipment>
): Promise<TrackingShipment> {
  try {
    const index = allTrackingData.findIndex((item) => item.id === id);
    if (index === -1) {
      throw new Error('Tracking data not found');
    }
    const updatedTracking = { ...allTrackingData[index], ...updatedData };
    allTrackingData[index] = updatedTracking;
    await new Promise((resolve) => setTimeout(resolve, 500));
    return updatedTracking;
  } catch (error) {
    throw new Error(
      `Failed to update tracking data: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createTracking(data: Partial<TrackingShipment>): Promise<TrackingShipment> {
  try {
    const newId = Math.max(...allTrackingData.map((item) => item.id)) + 1;
    const newTracking = {
      ...data,
      id: newId,
      status: [TrackingStatus[0]],
    } as TrackingShipment;

    (allTrackingData as Array<typeof newTracking>).push(newTracking);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return newTracking;
  } catch (error) {
    throw new Error(
      `Failed to create tracking data: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteTracking(id: number): Promise<void> {
  try {
    const index = allTrackingData.findIndex((item) => item.id === id);
    if (index === -1) {
      throw new Error('Tracking data not found');
    }
    allTrackingData.splice(index, 1);
    await new Promise((resolve) => setTimeout(resolve, 500));
  } catch (error) {
    throw new Error(
      `Failed to delete tracking data: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getTrackingStatusOptions() {
  await new Promise((resolve) => setTimeout(resolve, 300));
  return TrackingStatus;
}

export async function getTrackingServiceCategoryOptions() {
  await new Promise((resolve) => setTimeout(resolve, 300));
  return TrackingServiceCategories;
}
