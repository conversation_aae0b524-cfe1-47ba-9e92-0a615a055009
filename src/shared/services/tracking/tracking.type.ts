import type { ddValue } from 'src/shared/types/ddValue';

export enum PickUpOrDropOff {
  PICKUP = 'PICKUP',
  DROP_OFF = 'DROP_OFF',
}

export interface TrackingShipment {
  id: number;
  awbNumber: string;
  particulars: ddValue[];
  status: ddValue[];
  createdAt: string;
  updatedAt: string;
  originLocation: string;
  destinationLocation: string;
  originPincode: string;
  destinationPincode: string;
  serviceCategory: ddValue[];
  isShipmentAtRisk: boolean;
  riskChargesType: ddValue[];
  contentType: ddValue[];
  contentDescription: string;
  weight: number;
  weightUnit: string;
  length: number;
  width: number;
  height: number;
  dimensionsUnit: string;
  shipmentValue: number;
  customerName: string;
  customerEmailId: string;
  customerPhoneNumber: string;
  customerAddressLine1: string;
  customerAddressLine2: string;
  customerCity: string;
  customerState: string;
  customerCountry: string;
  customerPincode: string;
  isPickupOrDropOff: PickUpOrDropOff;
  pickupLocation: string;
  pickupAddressLine1: string;
  pickupAddressLine2: string;
  pickupCity: string;
  pickupState: string;
  pickupCountry: string;
  pickupPincode: string;
  dropOffLocationType: ddValue[];
  shipmentImageUrl: string;
  shipmentPrice: number;
}
