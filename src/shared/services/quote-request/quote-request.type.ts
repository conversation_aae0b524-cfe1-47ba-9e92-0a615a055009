import type { ddValue, ServiceDDValue, ServiceSubCategoryDDValue } from 'src/shared/types/ddValue';

import type { AdminUser } from '../admin/users/users.types';

export interface QuoteRequest {
  id: number;
  name: string;
  phone: string;
  emailId: string;
  service?: ServiceDDValue;
  serviceSubCategory?: ServiceSubCategoryDDValue;
  serviceId: number;
  serviceSubCategoryId: number;
  status?: ddValue | null;
  statusId: number;
  preferredContactMethod?: ddValue | null;
  preferredContactMethodId: number;
  origin: string;
  destination: string;
  distance?: string;
  message: string;
  adminActionRemark?: string;
  assignedAdmin?: AdminUser | null;
  assignedAdminId?: number;
  createdDate?: string;
}
