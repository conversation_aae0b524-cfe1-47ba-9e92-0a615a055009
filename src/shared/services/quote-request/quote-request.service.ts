import type { AxiosResponse } from 'axios';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';
import type { ddValue, ServiceDDValue, ServiceSubCategoryDDValue } from 'src/shared/types/ddValue';

import axios from 'src/utils/axios';

export async function searchQuoteRequests(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<QuoteRequest>> {
  try {
    const response: AxiosResponse<SearchResponse<QuoteRequest>> = await axios.post(
      '/api/quoterequests/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to search quote requests: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getQuoteRequestById(id: number): Promise<QuoteRequest> {
  try {
    const response: AxiosResponse<QuoteRequest> = await axios.get(`/api/quoterequests/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to get quote request by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateQuoteRequest(postData: Partial<QuoteRequest>): Promise<QuoteRequest> {
  try {
    const response: AxiosResponse<QuoteRequest> = await axios.put(
      `/api/quoterequests/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to update quote request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getQuoteRequestStatusOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(
      `/api/MasterData/QuoteRequestStatuses`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch quote request status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getQuoteRequestServiceOptions() {
  try {
    const response: AxiosResponse<ServiceDDValue[]> = await axios.get(
      '/api/masterdata/services?includeSubCategories=true'
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch quote request service options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getQuoteRequestSubCategories() {
  try {
    const response: AxiosResponse<ServiceSubCategoryDDValue[]> =
      await axios.get(`/api/quotesubcategories`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch quote request sub categories: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getPreferredContactMethods() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get('/api/masterdata/ContactMethods');
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch preferred contact methods: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createQuoteRequest(data: Partial<QuoteRequest>): Promise<QuoteRequest> {
  try {
    const response: AxiosResponse<QuoteRequest> = await axios.post('/api/quoterequests', data);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to create quote request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteQuoteRequest(id: number): Promise<void> {
  try {
    await axios.delete(`/api/quoterequests/${id}`);
  } catch (error) {
    throw new Error(
      `Failed to delete quote request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
