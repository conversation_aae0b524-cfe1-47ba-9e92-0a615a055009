import type { AxiosResponse } from 'axios';
import type { ddValue } from 'src/shared/types/ddValue';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';

import axios from 'src/utils/axios';

import type { JobApplication } from './job-applications.type';

export async function searchJobApplications(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<JobApplication>> {
  try {
    const response: AxiosResponse<SearchResponse<JobApplication>> = await axios.post(
      '/api/JobApplications/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to search Job applications: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobApplicationById(id: number): Promise<JobApplication> {
  try {
    const response: AxiosResponse<JobApplication> = await axios.get(`/api/JobApplications/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to get job application by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateJobApplication(
  postData: Partial<JobApplication>
): Promise<JobApplication> {
  try {
    const response: AxiosResponse<JobApplication> = await axios.put(
      `/api/JobApplications/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to update job application: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getJobApplicationStatusOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(
      `/api/MasterData/jobApplicationStatuses`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch job application status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getPreferredContactMethodOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/contactMethods`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch contact method options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteJobApplication(id: number): Promise<void> {
  try {
    await axios.delete(`/api/JobApplications/${id}`);
  } catch (error) {
    throw new Error(
      `Failed to delete job application: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
