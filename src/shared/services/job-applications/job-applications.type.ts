import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

export interface JobApplication {
  id: number;
  name: string;
  emailId: string;
  phone: string;
  addressLine1: string;
  addressLine2: string;
  zipcode: string;
  city: string;
  state: string;
  country: string;
  landmark?: string | null;
  totalRelevantExperienceInYears: number;
  previousEmployer?: string | null;
  previousJobPosition?: string | null;
  previousExploymentInYears?: string | null;
  preferredContactMethodId: number;
  jobPositionId: number;
  jobApplicationStatusId: number;
  reference: string;
  assignedAdminId: number;
  availableFromDate: string;
  isAuthorizedToWorkInIndia: boolean;
  requireVisaSponsorship: boolean;
  isTCAccepted: boolean;
  jobLocation: string;
  resumeDocumentUrl: string;
  lastModified: string;
  createdDate: string;
  adminActionRemark?: string | null;

  preferredContactMethod?: ddValue | null;
  jobPosition?: JobPosition | null;
  jobApplicationStatus?: ddValue | null;
  assignedAdmin?: AdminUser | null;
}
