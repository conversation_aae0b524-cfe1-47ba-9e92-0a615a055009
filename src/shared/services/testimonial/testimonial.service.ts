import type { ddValue } from 'src/shared/types/ddValue';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { Testimonial } from 'src/shared/services/testimonial/testimonial.type';

import { TestimonialTypes, generateMockTestimonials } from 'src/shared/mock/testimonial';

const allTestimonials = generateMockTestimonials(50);

export async function searchTestimonials(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<Testimonial>> {
  try {
    let filteredTestimonials = [...allTestimonials];

    filteredTestimonials = filteredTestimonials.filter(
      (testimonial) =>
        testimonial.name.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        testimonial.company.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        testimonial.text.toLowerCase().includes(searchCriteria.searchKey.toLowerCase())
    );

    const { filters } = searchCriteria;
    filteredTestimonials = filteredTestimonials.filter((testimonial) =>
      filters.every((filter) => {
        if (filter.field === 'type' && filter.value) {
          return testimonial.type.id === Number(filter.value);
        }
        return true;
      })
    );

    const { pageNumber, pageSize } = searchCriteria.pagination;
    const start = (pageNumber - 1) * pageSize;
    const paginatedTestimonials = filteredTestimonials.slice(start, start + pageSize);

    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      count: filteredTestimonials.length,
      searchResult: paginatedTestimonials,
    };
  } catch (error) {
    throw new Error(
      `Failed to search testimonials: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getTestimonialById(id: number): Promise<Testimonial> {
  try {
    const testimonial = allTestimonials.find((t) => t.id === id);
    if (!testimonial) {
      throw new Error('Testimonial not found');
    }
    await new Promise((resolve) => setTimeout(resolve, 500));
    return testimonial;
  } catch (error) {
    throw new Error(
      `Failed to get testimonial by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createTestimonial(data: Partial<Testimonial>): Promise<Testimonial> {
  try {
    const newId = Math.max(...allTestimonials.map((t) => t.id)) + 1;
    const newTestimonial = {
      ...data,
      id: newId,
    } as Testimonial;

    allTestimonials.push(newTestimonial);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return newTestimonial;
  } catch (error) {
    throw new Error(
      `Failed to create testimonial: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateTestimonial(id: number, data: Testimonial): Promise<Testimonial> {
  try {
    const index = allTestimonials.findIndex((t) => t.id === id);
    if (index === -1) {
      throw new Error('Testimonial not found');
    }

    allTestimonials[index] = { ...allTestimonials[index], ...data };
    await new Promise((resolve) => setTimeout(resolve, 500));
    return allTestimonials[index];
  } catch (error) {
    throw new Error(
      `Failed to update testimonial: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteTestimonial(id: number): Promise<void> {
  try {
    const index = allTestimonials.findIndex((t) => t.id === id);
    if (index === -1) {
      throw new Error('Testimonial not found');
    }

    allTestimonials.splice(index, 1);
    await new Promise((resolve) => setTimeout(resolve, 500));
  } catch (error) {
    throw new Error(
      `Failed to delete testimonial: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getTestimonialTypeOptions(): Promise<ddValue[]> {
  try {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return TestimonialTypes;
  } catch (error) {
    throw new Error(
      `Failed to get testimonial type options: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}
