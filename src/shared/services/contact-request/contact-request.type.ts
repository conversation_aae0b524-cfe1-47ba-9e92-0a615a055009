import type { ddValue } from 'src/shared/types/ddValue';

import type { AdminUser } from '../admin/users/users.types';

export interface ContactRequest {
  id: number;
  name: string;
  emailId: string;
  phone: string;
  preferredContactMethod?: ddValue | null;
  preferredContactMethodId: number;
  status?: ddValue | null;
  statusId: number;
  reference: string;
  subject: string;
  adminActionRemark: string;
  assignedAdmin?: AdminUser | null;
  assignedAdminId?: number;
  message: string;
  createdDate?: string;
}
