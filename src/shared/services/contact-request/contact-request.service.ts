import type { AxiosResponse } from 'axios';
import type { ddValue } from 'src/shared/types/ddValue';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { ContactRequest } from 'src/shared/services/contact-request/contact-request.type';

import axios from 'src/utils/axios';

export async function searchContactQuoteRequests(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<ContactRequest>> {
  try {
    const response: AxiosResponse<SearchResponse<ContactRequest>> = await axios.post(
      '/api/contactrequests/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch contact requests: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getContactRequestById(id: number): Promise<ContactRequest> {
  try {
    const response: AxiosResponse<ContactRequest> = await axios.get(`/api/contactrequests/${id}`);

    return response?.data;
  } catch (error) {
    throw new Error(
      `Failed to get contact request by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateContactRequest(
  postData: Partial<ContactRequest>
): Promise<ContactRequest> {
  try {
    const response: AxiosResponse<ContactRequest> = await axios.put(
      `/api/contactrequests/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to update contact request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getContactRequestStatusOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/contactrequeststatus`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch contact request status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export const getSubject = async (): Promise<ddValue[]> => {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get('/api/masterdata/subjects');
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch Subjects: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

export const getReference = async (): Promise<ddValue[]> => {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get('/api/masterdata/references');
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch References: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

export const getContactMethods = async (): Promise<ddValue[]> => {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get('/api/masterdata/ContactMethods');
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch Contact Methods: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

export async function getAssignedAdminOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get('/api/assignedadmins');
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch assigned admin options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
