import type { ddValue } from '../types/ddValue';

const mockDropOffLocations: ddValue[] = [
  {
    id: 1,
    name: 'Downtown Distribution Center - 123 Main St, Downtown',
    isDisabled: false,
  },
  {
    id: 2,
    name: 'North Side Warehouse - 456 Industrial Blvd, North Side',
    isDisabled: false,
  },
  {
    id: 3,
    name: 'Airport Logistics Hub - 789 Airport Rd, Near Terminal',
    isDisabled: false,
  },
  {
    id: 4,
    name: 'West Side Depot - 321 Commerce Dr, West District',
    isDisabled: false,
  },
  {
    id: 5,
    name: 'South Terminal - 654 Transport Ave, South End',
    isDisabled: false,
  },
  {
    id: 6,
    name: 'Central Pickup Point - 987 Center Plaza, City Center',
    isDisabled: false,
  },
];

export function getMockDropOffLocations(): Promise<ddValue[]> {
  return Promise.resolve(mockDropOffLocations);
}
