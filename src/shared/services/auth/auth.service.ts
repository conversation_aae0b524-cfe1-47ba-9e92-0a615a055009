/* eslint-disable @typescript-eslint/no-throw-literal */
import type { AxiosResponse } from 'axios';
import type { Errors } from 'src/shared/types/errorResonse';

import axios from 'src/utils/axios';

import { ErrorConstructor } from 'src/shared/types/errorResonse';

import type { CreatePasswordPostData } from './auth.types';
import type { SignInResponse } from '../admin/users/users.types';

interface SignInCredentials {
  email: string;
  password: string;
}

export async function signIn(credentials: SignInCredentials): Promise<SignInResponse | Errors> {
  try {
    const response: AxiosResponse<SignInResponse> = await axios.post('/api/authentication/token', {
      emailId: credentials.email,
      password: credentials.password,
    });
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function forgotPassword(email: string): Promise<string | Errors> {
  try {
    const response: AxiosResponse<string> = await axios.post(
      `/api/authentication/forgotPassword?emailID=${email}`
    );
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function createPassword(postData: CreatePasswordPostData): Promise<unknown | Errors> {
  try {
    const response: AxiosResponse<unknown> = await axios.post(
      '/api/authentication/resetPassword',
      postData
    );
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}
