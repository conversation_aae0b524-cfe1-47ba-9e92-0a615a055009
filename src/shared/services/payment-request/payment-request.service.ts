import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';

import { PaymentRequestStatus, generateMockPaymentRequests } from 'src/shared/mock/payment-request';

import type { PaymentRequest } from './payment-request.type';

const allPaymentRequests = generateMockPaymentRequests(50);

export async function searchPaymentRequests(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<PaymentRequest>> {
  try {
    let filteredRequests = allPaymentRequests.filter(
      (request) =>
        request.customerName.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        request.contactNumber.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        request.paymentId.toLowerCase().includes(searchCriteria.searchKey.toLowerCase())
    );

    const { filters } = searchCriteria;
    filteredRequests = filteredRequests.filter((request) =>
      filters.every((filter) => {
        if (filter.field === 'status') {
          return request.status.id === Number(filter.value);
        }
        return true;
      })
    );

    const { pageNumber, pageSize } = searchCriteria.pagination;
    const start = (pageNumber - 1) * pageSize;
    const paginatedRequests = filteredRequests.slice(start, start + pageSize);

    const response: SearchResponse<PaymentRequest> = {
      count: filteredRequests.length,
      searchResult: paginatedRequests,
    };

    await new Promise((resolve) => setTimeout(resolve, 500));
    return response;
  } catch (error) {
    throw new Error(
      `Failed to search payment requests: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getPaymentRequestById(id: number): Promise<PaymentRequest> {
  try {
    const request = allPaymentRequests.find((req) => req.id === id);
    if (!request) {
      throw new Error('Payment request not found');
    }
    await new Promise((resolve) => setTimeout(resolve, 500));
    return request;
  } catch (error) {
    throw new Error(
      `Failed to get payment request by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updatePaymentRequest(
  id: number,
  updatedData: Partial<PaymentRequest>
): Promise<PaymentRequest> {
  try {
    const index = allPaymentRequests.findIndex((req) => req.id === id);
    if (index === -1) {
      throw new Error('Payment request not found');
    }
    const updatedRequest = { ...allPaymentRequests[index], ...updatedData };
    allPaymentRequests[index] = updatedRequest;
    await new Promise((resolve) => setTimeout(resolve, 500));
    return updatedRequest;
  } catch (error) {
    throw new Error(
      `Failed to update payment request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createPaymentRequest(data: Partial<PaymentRequest>): Promise<PaymentRequest> {
  try {
    const newId = Math.max(...allPaymentRequests.map((req) => req.id)) + 1;
    const newRequest = {
      ...data,
      id: newId,
      status: PaymentRequestStatus[0],
      action: {
        adminId: 0,
        action: [{ id: 1, name: 'New' }],
        remark: '',
      },
    } as PaymentRequest;

    (allPaymentRequests as Array<typeof newRequest>).push(newRequest);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return newRequest;
  } catch (error) {
    throw new Error(
      `Failed to create payment request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deletePaymentRequest(id: number): Promise<void> {
  try {
    const index = allPaymentRequests.findIndex((req) => req.id === id);
    if (index === -1) {
      throw new Error('Payment request not found');
    }
    allPaymentRequests.splice(index, 1);
    await new Promise((resolve) => setTimeout(resolve, 500));
  } catch (error) {
    throw new Error(
      `Failed to delete payment request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getPaymentRequestStatusOptions() {
  try {
    const statusRequest = PaymentRequestStatus;
    await new Promise((resolve) => setTimeout(resolve, 500));
    return statusRequest;
  } catch (error) {
    throw new Error(
      `Failed to fetch payment request status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
