import type { PincodeDetails } from 'src/shared/types/pincode';

import { pincodeAxios } from './pincode.axios';

export const fetchPincodeDetails = async (pincode: string): Promise<PincodeDetails[]> => {
  try {
    const response = await pincodeAxios.get<PincodeDetails[]>(`/${pincode}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching pincode details:', error);
    throw error;
  }
};
