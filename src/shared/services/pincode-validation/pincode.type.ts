import type { ddValue } from 'src/shared/types/ddValue';

export interface PostOffice {
  Name: string;
  Description: string | null;
  BranchType: string;
  DeliveryStatus: string;
  Circle: string;
  District: string;
  Division: string;
  Region: string;
  Block: string;
  State: string;
  Country: string;
  Pincode: string;
}

export interface PincodeApiResponse {
  Message: string;
  Status: string;
  PostOffice: PostOffice[];
}

enum PickUpOrDropOff {
  PICKUP = 'Pickup',
  DROPOFF = 'Dropoff',
}

export interface TrackingShipment {
  id: number;
  awbNumber: string;
  particulars: ddValue[];
  status: ddValue[];
  createdAt: string;
  updatedAt: string;
  originLocation: string;
  destinationLocation: string;
  originPincode: string;
  destinationPincode: string;
  serviceCategory: ddValue[];
  isShipmentAtRisk: boolean;
  riskChargeType: ddValue[];
  contentType: ddValue[];
  contentDescription: string;
  weight: number;
  weightUnit: string;
  length: number;
  width: number;
  height: number;
  dimensionsUnit: string;
  shipmentValue: number;
  customerName: string;
  customerEmailId: string;
  customerPhoneNumber: string;
  customerAddressLine1: string;
  customerAddressLine2: string;
  customerCity: string;
  customerState: string;
  customerCountry: string;
  customerPincode: string;
  isPickupOrDropOff: PickUpOrDropOff;
  dropOffOutlets: ddValue[];
  pickupAddressLine1: string;
  pickupAddressLine2: string;
  pickupCity: string;
  pickupState: string;
  pickupCountry: string;
  pickupPincode: string;
  pickupLocation: string;
  dropLocation: string;
  dropAddressLine1: string;
  dropAddressLine2: string;
  dropCity: string;
  dropState: string;
  dropCountry: string;
  dropPincode: string;
  shipmentImageUrl: string;
  shipmentPrice: number;
}
