/* eslint-disable @typescript-eslint/no-throw-literal */
import type { AxiosResponse } from 'axios';
import type { ddValue } from 'src/shared/types/ddValue';
import type { Errors } from 'src/shared/types/errorResonse';

import axios from 'src/utils/axios';

import { ErrorConstructor } from 'src/shared/types/errorResonse';

import type { AdminUser } from './users.types';

export async function addUsers(postData: AdminUser): Promise<AdminUser | Errors> {
  try {
    console.log('Is you triggered?');
    const response: AxiosResponse<AdminUser> = await axios.post('/api/users/', postData);
    console.log('Is you triggered?', response);
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function getUsers(searchCriteria?: any): Promise<AdminUser[] | Errors> {
  try {
    const response: AxiosResponse<AdminUser[]> = await axios.get('/api/users', searchCriteria);
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function updateUser(postData: AdminUser): Promise<AdminUser | Errors> {
  try {
    const response: AxiosResponse<AdminUser> = await axios.put(
      `/api/users/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function deleteUser(id: number): Promise<AdminUser | Errors> {
  try {
    const response: AxiosResponse<AdminUser> = await axios.delete(`/api/users/${id}`);
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function getUserRoles(): Promise<ddValue[] | Errors> {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/masterdata/roles`);
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}

export async function getUserStatus(): Promise<ddValue[] | Errors> {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/masterdata/status`);
    return response.data;
  } catch (error) {
    throw new ErrorConstructor(error);
  }
}
