import type { ddValue } from 'src/shared/types/ddValue';

export interface AdminUser {
  id: number;
  firstName: string;
  lastName: string;
  fullName?: string;
  role?: ddValue;
  roleId: number;
  emailId: string;
  emailConfirmed?: boolean;
  phoneNumber?: string;
  phoneNumberConfirmed?: boolean;
  passwordHash?: string;
  passwordSalt?: string;
  lastLogin?: string;
  isDeleted: boolean;
  lastModified: string;
  statusId: number;
  status: ddValue;
  isExternalUser?: boolean;
  entityId?: number;
}

export interface SignInResponse {
  token: string;
  userName?: string;
  email?: string;
  role?: string;
}
