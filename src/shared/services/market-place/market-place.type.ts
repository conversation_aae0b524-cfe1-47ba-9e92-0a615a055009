import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminAction } from 'src/shared/services/admin-action/admin-action.type';

export interface MarketPlace {
  id: number;
  name: string;
  contactNumber: string;
  email: string;
  category: string;
  location: string;
  status: ddValue;
  address: string;
  title: string;
  expectingPrice: number;
  description: string;
  message: string;
  images: string[];
  action: AdminAction;
}
