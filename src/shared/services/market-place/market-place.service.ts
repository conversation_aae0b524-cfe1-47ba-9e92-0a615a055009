import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';

import { MarketPlaceStatus, generateMockMarketPlaces } from 'src/shared/mock/market-place';

import type { MarketPlace } from './market-place.type';

const allMarketPlaces = generateMockMarketPlaces(50);

export async function searchMarketPlaces(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<MarketPlace>> {
  try {
    let filteredRequests = allMarketPlaces.filter(
      (request) =>
        request.name.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        request.email.toLowerCase().includes(searchCriteria.searchKey.toLowerCase()) ||
        request.contactNumber.toLowerCase().includes(searchCriteria.searchKey.toLowerCase())
    );

    const { filters } = searchCriteria;
    filteredRequests = filteredRequests.filter((request) =>
      filters.every((filter) => {
        if (filter.field === 'status') {
          return request.status.id === Number(filter.value);
        }
        return true;
      })
    );

    const { pageNumber, pageSize } = searchCriteria.pagination;
    const start = (pageNumber - 1) * pageSize;
    const paginatedRequests = filteredRequests.slice(start, start + pageSize);

    const response: SearchResponse<MarketPlace> = {
      count: filteredRequests.length,
      searchResult: paginatedRequests,
    };

    await new Promise((resolve) => setTimeout(resolve, 500));
    return response;
  } catch (error) {
    throw new Error(
      `Failed to search market places: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getMarketPlaceById(id: number): Promise<MarketPlace> {
  try {
    const request = allMarketPlaces.find((req) => req.id === id);
    if (!request) {
      throw new Error('Market place not found');
    }
    await new Promise((resolve) => setTimeout(resolve, 500));
    return request;
  } catch (error) {
    throw new Error(
      `Failed to get market place by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateMarketPlace(
  id: number,
  updatedData: Partial<MarketPlace>
): Promise<MarketPlace> {
  try {
    const index = allMarketPlaces.findIndex((req) => req.id === id);
    if (index === -1) {
      throw new Error('Market place not found');
    }
    const updatedRequest = { ...allMarketPlaces[index], ...updatedData };
    allMarketPlaces[index] = updatedRequest;
    await new Promise((resolve) => setTimeout(resolve, 500));
    return updatedRequest;
  } catch (error) {
    throw new Error(
      `Failed to update market place: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function createMarketPlace(data: Partial<MarketPlace>): Promise<MarketPlace> {
  try {
    const newId = Math.max(...allMarketPlaces.map((req) => req.id)) + 1;
    const newRequest = {
      ...data,
      id: newId,
      status: MarketPlaceStatus[0],
      action: {
        adminId: 0,
        action: { id: 1, name: 'New' },
        remark: '',
      },
    } as MarketPlace;

    allMarketPlaces.push(newRequest);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return newRequest;
  } catch (error) {
    throw new Error(
      `Failed to create market place: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteMarketPlace(id: number): Promise<void> {
  try {
    const index = allMarketPlaces.findIndex((req) => req.id === id);
    if (index === -1) {
      throw new Error('Market place not found');
    }
    allMarketPlaces.splice(index, 1);
    await new Promise((resolve) => setTimeout(resolve, 500));
  } catch (error) {
    throw new Error(
      `Failed to delete market place: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getMarketPlaceStatusOptions() {
  try {
    const statusRequest = MarketPlaceStatus;
    await new Promise((resolve) => setTimeout(resolve, 500));
    return statusRequest;
  } catch (error) {
    throw new Error(
      `Failed to fetch market place status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
