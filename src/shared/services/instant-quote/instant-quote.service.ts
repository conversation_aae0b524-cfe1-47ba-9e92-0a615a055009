import type { AxiosResponse } from 'axios';
import type { SearchCriteria } from 'src/shared/types/searchCriteria';
import type { SearchResponse } from 'src/shared/types/searchResponse';
import type { ddValue, ServiceDDValue } from 'src/shared/types/ddValue';

import axios from 'src/utils/axios';

import type { InstantEstimation } from './instant-quote.type';

export async function searchInstantRequests(
  searchCriteria: SearchCriteria
): Promise<SearchResponse<InstantEstimation>> {
  try {
    const response: AxiosResponse<SearchResponse<InstantEstimation>> = await axios.post(
      '/api/InstantEstimations/search',
      searchCriteria
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to search instant requests: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getInstantRequestById(id: number): Promise<InstantEstimation> {
  try {
    const response: AxiosResponse<InstantEstimation> = await axios.get(
      `/api/InstantEstimations/${id}`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to get instant request by ID: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function updateInstantRequest(
  postData: Partial<InstantEstimation>
): Promise<InstantEstimation> {
  try {
    const response: AxiosResponse<InstantEstimation> = await axios.put(
      `/api/InstantEstimations/${postData?.id}`,
      postData
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to update instant request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getInstantRequestStatusOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(
      `/api/MasterData/QuoteRequestStatuses`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch instant request status options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function deleteInstantRequest(id: number): Promise<void> {
  try {
    await axios.delete(`/api/InstantEstimations/${id}`);
  } catch (error) {
    throw new Error(
      `Failed to delete instant request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getServiceOptions() {
  try {
    const response: AxiosResponse<ServiceDDValue[]> = await axios.get(
      '/api/masterdata/services?includeSubCategories=false'
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch quote request service options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getHouseTypeOptions(serviceId: string) {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(
      `/api/MasterData/householdcapacities/${serviceId}`
    );
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch house type options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getVehicleOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/vehicles`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch vehicle options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getPackageTypeOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/packagetypes`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch vehicle options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getTruckingTypeOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/TruckingTypes`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch trucking options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getGoodsTypeOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/GoodsTypes`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch trucking options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getDocumentTypeOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/DocumentTypes`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch document options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getRegionTypeOptions() {
  try {
    const response: AxiosResponse<ddValue[]> = await axios.get(`/api/MasterData/RegionTypes`);
    return response.data;
  } catch (error) {
    throw new Error(
      `Failed to fetch region options: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
