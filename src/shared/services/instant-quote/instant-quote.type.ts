import type { ddValue, ServiceDDValue } from 'src/shared/types/ddValue';

import type { AdminUser } from '../admin/users/users.types';

export type Vehicle = {
  id: number;
  name: string;
  length: number;
  width: number;
  height: number;
  cft: number;
  minimumCost: number;
  allowPartLoad: boolean;
  imageUrls: string[];
};

export type FieldConfig = {
  stepValue: number;
  maxValue: number;
};

export type FieldConfigs = {
  [key: string]: FieldConfig;
};

export enum EstimationType {
  Cost = 1,
  Time = 2,
  Information = 3,
}

export enum EstimationLineItemType {
  Primary = 1,
  Secondary = 2,
  Tertiary = 3,
  Information = 4,
}

export enum ValueType {
  Currency = 1,
  Decimal = 2,
  Integer = 3,
}

export type EstimationLineItem = {
  name: string;
  description?: string | null;
  value: number;
  textValue?: string | null;
  estimationType: EstimationType;
  lineItemType: EstimationLineItemType;
  isRecommendedValue: boolean;
  isEditable: boolean;
  requestPropertyName?: string | null;
  requestPropertyDataType?: string | null;
  showTextValue: boolean;
};

export type EstimationResponse = {
  estimations: Estimation[];
};

export type Estimation = {
  name: string;
  lineItems: EstimationLineItem[];
  insurance: number;
  totalCost: number;
  vehicle?: Vehicle | null;
};

export type InstantEstimation = {
  id: number;
  name: string;
  emailId: string;
  phone: string;
  serviceId: number;
  statusId: number;
  createdDate: string;
  requestDataJson: any;
  response: EstimationResponse;
  assignedAdminId?: number | null;
  assignedAdmin?: AdminUser | null;
  lastModified?: string;
  adminActionRemark?: string | null;
  service?: ServiceDDValue | null;
  status?: ddValue | null;
  isResponseGenerated?: boolean;
};
