import type { AxiosError } from 'axios';

export type DBErrorResponse = {
  [key: string]: string;
};

export type Errors = {
  errors: DBErrorResponse | string;
  status: number;
  code: string;
};

export class ErrorConstructor implements Errors {
  errors: DBErrorResponse | string;

  status: number;

  code: string;

  constructor(error: unknown) {
    const axiosError = error as AxiosError;

    this.errors = (axiosError.response?.data || 'Unknown error') as DBErrorResponse | string;
    this.status = axiosError.response?.status || 500;
    this.code = axiosError.code || 'UNKNOWN_ERROR';
  }
}
