export enum SortDirectionEnum {
  Ascending = 'Ascending',
  Descending = 'Descending',
}

export interface FilterOption {
  field?: string;
  value?: string;
}

export interface SortOption {
  field: string;
  direction: SortDirectionEnum;
}

export interface PaginationOptions {
  pageNumber: number;
  pageSize: number;
}

export interface SearchCriteria {
  searchKey: string;
  filters: FilterOption[];
  sortOptions: SortOption[];
  pagination: PaginationOptions;
}
