import type { ddValue } from './ddValue';

export interface LocationFormValues {
  shipmentType: number;
  pickupLocation: any;
  pickupAddressLine1: string;
  pickupAddressLine2: string;
  pickupCity: string;
  pickupState: string;
  pickupZipCode: string;
  pickupCountry: string;
  deliveryLocation: any;
  deliveryAddressLine1: string;
  deliveryAddressLine2: string;
  deliveryCity: string;
  deliveryState: string;
  deliveryZipCode: string;
  deliveryCountry: string;
  recipientName: string;
  recipientPhone: string;
  deliveryCountryType: string;
  deliveryType: string;
  selectedDropOffLocation: string;
}

export interface LocationFormProps {
  deliveryCountryTypeOptions?: ddValue[];
  deliveryTypeOptions?: ddValue[];
  dropOffOutletsOptions?: ddValue[];
  shipmentTypeOptions?: ddValue[];
}
