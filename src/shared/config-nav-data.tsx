import { paths } from 'src/routes/paths';

import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';

export type Children = {
  label: string;
  link: string;
};

export type ******** = {
  label: string;
  icon: JSX.Element;
  link?: string;
  children?: Children[];
};

const CHILD_CMS_DATA: Children[] = [
  {
    label: 'Gallery',
    link: paths.nevMovLinks.gallery,
  },
  {
    label: 'Testimonials',
    link: paths.nevMovLinks.testimonials,
  },
];

const CHILD_CAREERS_DATA: Children[] = [
  {
    label: 'Job Positions',
    link: paths.nevMovLinks.jobPositions,
  },
  {
    label: 'Job Postings',
    link: paths.nevMovLinks.jobPostings,
  },
  {
    label: 'Job Applications',
    link: paths.nevMovLinks.jobApplications,
  },
];

export const userLinks: ********[] = [
  {
    label: 'Home',
    icon: <Iconify icon={Icons.navigation.home} />,
    link: paths.home,
  },
  {
    label: 'profile',
    icon: <Iconify icon={Icons.navigation.profile} />,
    link: paths.profile,
  },
  {
    label: 'logout',
    icon: <Iconify icon={Icons.navigation.logout} />,
    link: paths.auth.login,
  },
];

const nexMovLinks: ********[] = [
  {
    label: 'Contact Requests',
    icon: <Iconify icon={Icons.nexMov.contactRequestIcon} />,
    link: paths.nevMovLinks.home,
  },
  {
    label: 'Quote Requests',
    icon: <Iconify icon={Icons.nexMov.quoteRequestIcon} />,
    link: paths.nevMovLinks.quoteRequest,
  },
  {
    label: 'Instant Quote',
    icon: <Iconify icon={Icons.nexMov.instantQuoteIcon} />,
    link: paths.nevMovLinks.instantRequest,
  },
  {
    label: 'Careers',
    icon: <Iconify icon={Icons.nexMov.careersIcon} />,
    children: CHILD_CAREERS_DATA,
  },
  {
    label: 'Payment Request',
    icon: <Iconify icon={Icons.nexMov.paymentRequestIcon} />,
    link: paths.nevMovLinks.paymentRequest,
  },
  {
    label: 'Tracking',
    icon: <Iconify icon={Icons.nexMov.trackingIcon} />,
    link: paths.nevMovLinks.shipmentTracking,
  },
  {
    label: 'CMS',
    icon: <Iconify icon={Icons.nexMov.cms} />,
    children: CHILD_CMS_DATA,
  },
  {
    label: 'Market Place',
    icon: <Iconify icon={Icons.nexMov.markePlaceIcon} />,
    link: paths.nevMovLinks.marketPlace,
  },
];

export const getNavigationLinks = (currentPath: string): ********[] => {
  if (currentPath.includes('/')) {
    return nexMovLinks;
  }

  return [];
};
