import { Box, Paper, styled, Typography } from '@mui/material';

export const DemoContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  maxWidth: '1200px',
  margin: '0 auto',
}));

export const DemoSection = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
}));

export const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  fontWeight: theme.typography.fontWeightSemiBold,
  color: theme.palette.primary.main,
}));

export const FormContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: theme.spacing(3),
  gridTemplateColumns: '1fr',
  [theme.breakpoints.up('md')]: {
    gridTemplateColumns: '1fr 1fr',
  },
}));

export const OutputContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.grey[200]}`,
  minHeight: '200px',
  fontFamily: 'monospace',
  fontSize: '0.875rem',
  whiteSpace: 'pre-wrap',
  overflow: 'auto',
}));

export const PreviewContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.grey[200]}`,
  minHeight: '200px',
  '& p': {
    margin: 0,
    '&:not(:last-child)': {
      marginBottom: theme.spacing(1),
    },
  },
  '& ul, & ol': {
    paddingLeft: theme.spacing(2),
    margin: 0,
    '&:not(:last-child)': {
      marginBottom: theme.spacing(1),
    },
  },
  '& li': {
    marginBottom: theme.spacing(0.5),
  },
  '& h1, & h2, & h3, & h4, & h5, & h6': {
    margin: 0,
    fontWeight: theme.typography.fontWeightSemiBold,
    '&:not(:last-child)': {
      marginBottom: theme.spacing(1),
    },
  },
  '& h1': { fontSize: '1.5rem' },
  '& h2': { fontSize: '1.25rem' },
  '& h3': { fontSize: '1.125rem' },
  '& blockquote': {
    borderLeft: `4px solid ${theme.palette.grey[300]}`,
    paddingLeft: theme.spacing(2),
    margin: 0,
    fontStyle: 'italic',
    '&:not(:last-child)': {
      marginBottom: theme.spacing(1),
    },
  },
  '& code': {
    backgroundColor: theme.palette.grey[100],
    padding: '2px 4px',
    borderRadius: '4px',
    fontSize: '0.8em',
    fontFamily: 'monospace',
  },
  '& pre': {
    backgroundColor: theme.palette.grey[100],
    padding: theme.spacing(1.5),
    borderRadius: theme.shape.borderRadius,
    overflow: 'auto',
    margin: 0,
    '&:not(:last-child)': {
      marginBottom: theme.spacing(1),
    },
    '& code': {
      backgroundColor: 'transparent',
      padding: 0,
    },
  },
  '& hr': {
    border: 'none',
    borderTop: `1px solid ${theme.palette.grey[300]}`,
    margin: `${theme.spacing(2)} 0`,
  },
}));
