import * as Yup from 'yup';
import { Form, Field, Formik } from 'formik';
import React, { useRef, useState } from 'react';

import { Box, Tab, Grid, Tabs, <PERSON>, <PERSON>ack, Button, Typography } from '@mui/material';

import PageTitle from 'src/components/common/page-title/PageTitle';
import {
  RichTextEditor,
  type RichTextEditorRef,
} from 'src/components/common/forms/rich-text-editor';

import {
  DemoSection,
  SectionTitle,
  DemoContainer,
  FormContainer,
  OutputContainer,
  PreviewContainer,
} from './RichTextEditorDemoPage.style';

interface FormValues {
  basicEditor: string;
  formikEditor: string;
  validatedEditor: string;
}

const validationSchema = Yup.object({
  validatedEditor: Yup.string()
    .required('This field is required')
    .min(10, 'Content must be at least 10 characters long'),
});

const RichTextEditorDemoPage: React.FC = () => {
  const [basicContent, setBasicContent] = useState(
    '<p>Welcome to the <strong>Rich Text Editor</strong> demo!</p>'
  );
  const [tabValue, setTabValue] = useState(0);
  const editorRef = useRef<RichTextEditorRef>(null);

  const initialValues: FormValues = {
    basicEditor: '<p>This is a <em>Formik-integrated</em> rich text editor.</p>',
    formikEditor: '<p>Another <strong>Formik</strong> example with different content.</p>',
    validatedEditor: '',
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleFormSubmit = (values: FormValues) => {
    console.log('Form submitted with values:', values);
    alert('Form submitted! Check the console for values.');
  };

  const handleEditorAction = (action: string) => {
    if (!editorRef.current) return;

    switch (action) {
      case 'focus':
        editorRef.current.focus();
        break;
      case 'blur':
        editorRef.current.blur();
        break;
      case 'getHTML':
        alert(`HTML Content: ${editorRef.current.getHTML()}`);
        break;
      case 'getText':
        alert(`Text Content: ${editorRef.current.getText()}`);
        break;
      case 'isEmpty':
        alert(`Is Empty: ${editorRef.current.isEmpty()}`);
        break;
      default:
        break;
    }
  };

  return (
    <DemoContainer>
      <PageTitle title="Rich Text Editor Demo" />

      {/* Basic Usage */}
      <DemoSection>
        <SectionTitle variant="h5">Basic Usage</SectionTitle>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          A simple rich text editor with basic formatting options.
        </Typography>

        <FormContainer>
          <Box>
            <RichTextEditor
              ref={editorRef}
              id="basic-editor"
              name="basicEditor"
              label="Basic Rich Text Editor"
              placeholder="Start typing your content here..."
              value={basicContent}
              handleChange={setBasicContent}
              required
            />

            <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
              <Button size="small" onClick={() => handleEditorAction('focus')}>
                Focus
              </Button>
              <Button size="small" onClick={() => handleEditorAction('blur')}>
                Blur
              </Button>
              <Button size="small" onClick={() => handleEditorAction('getHTML')}>
                Get HTML
              </Button>
              <Button size="small" onClick={() => handleEditorAction('getText')}>
                Get Text
              </Button>
              <Button size="small" onClick={() => handleEditorAction('isEmpty')}>
                Is Empty?
              </Button>
            </Stack>
          </Box>

          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Output Preview:
            </Typography>
            <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 1 }}>
              <Tab label="HTML" />
              <Tab label="Rendered" />
            </Tabs>

            {tabValue === 0 ? (
              <OutputContainer>{basicContent}</OutputContainer>
            ) : (
              <PreviewContainer dangerouslySetInnerHTML={{ __html: basicContent }} />
            )}
          </Box>
        </FormContainer>
      </DemoSection>

      {/* Formik Integration */}
      <DemoSection>
        <SectionTitle variant="h5">Formik Integration</SectionTitle>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Rich text editors integrated with Formik for form handling and validation.
        </Typography>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
        >
          {({ values, errors, touched, setFieldValue, setFieldTouched }) => (
            <Form>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Field name="formikEditor">
                    {() => (
                      <RichTextEditor
                        id="formik-editor"
                        name="formikEditor"
                        label="Formik Rich Text Editor"
                        placeholder="Enter your content..."
                        value={values.formikEditor}
                        handleChange={(value) => setFieldValue('formikEditor', value)}
                        handleBlur={() => setFieldTouched('formikEditor', true)}
                      />
                    )}
                  </Field>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Field name="validatedEditor">
                    {() => (
                      <RichTextEditor
                        id="validated-editor"
                        name="validatedEditor"
                        label="Validated Rich Text Editor"
                        placeholder="This field is required (min 10 characters)..."
                        value={values.validatedEditor}
                        error={touched.validatedEditor && !!errors.validatedEditor}
                        handleChange={(value) => setFieldValue('validatedEditor', value)}
                        handleBlur={() => setFieldTouched('validatedEditor', true)}
                        required
                      />
                    )}
                  </Field>
                </Grid>

                <Grid item xs={12}>
                  <Button type="submit" variant="contained" color="primary">
                    Submit Form
                  </Button>
                </Grid>
              </Grid>
            </Form>
          )}
        </Formik>
      </DemoSection>

      {/* States Demo */}
      <DemoSection>
        <SectionTitle variant="h5">Different States</SectionTitle>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Examples of the editor in different states.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <RichTextEditor
              id="disabled-editor"
              name="disabledEditor"
              label="Disabled Editor"
              value="<p>This editor is <strong>disabled</strong> and cannot be edited.</p>"
              handleChange={() => {}}
              disabled
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <RichTextEditor
              id="error-editor"
              name="errorEditor"
              label="Error State Editor"
              value=""
              handleChange={() => {}}
              error
              helperText="This field has an error"
              required
            />
          </Grid>
        </Grid>
      </DemoSection>

      {/* Features */}
      <DemoSection>
        <SectionTitle variant="h5">Features</SectionTitle>
        <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
          <Chip label="Bold/Italic/Underline" size="small" />
          <Chip label="Headings (H1-H3)" size="small" />
          <Chip label="Lists (Bullet/Numbered)" size="small" />
          <Chip label="Blockquotes" size="small" />
          <Chip label="Code Blocks" size="small" />
          <Chip label="Horizontal Rules" size="small" />
          <Chip label="Undo/Redo" size="small" />
          <Chip label="Formik Integration" size="small" />
          <Chip label="Validation Support" size="small" />
          <Chip label="MUI Theming" size="small" />
          <Chip label="TypeScript Support" size="small" />
        </Stack>
      </DemoSection>
    </DemoContainer>
  );
};

export default RichTextEditorDemoPage;
