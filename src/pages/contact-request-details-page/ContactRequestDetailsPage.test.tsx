import { render } from '@testing-library/react';

import ContactRequestDetailsPage from './ContactRequestDetailsPage';

jest.mock(
  'src/components/containers/contact-request-details-container/ContactRequestDetailsContainer',
  () => () => <div data-testid="contact-request-details-container" />
);

describe('ContactRequestDetailsPage', () => {
  it('renders the ContactRequestDetailsContainer', () => {
    const { getByTestId } = render(<ContactRequestDetailsPage />);
    expect(getByTestId('contact-request-details-container')).toBeInTheDocument();
  });
});
