import { render } from '@testing-library/react';

import ContactRequestPage from './ContactRequestPage';

jest.mock(
  'src/components/containers/contact-request-container/ContactRequestContainer',
  () => () => <div data-testid="contact-request-container" />
);

describe('ContactRequestPage', () => {
  it('renders the ContactRequestContainer', () => {
    const { getByTestId } = render(<ContactRequestPage />);
    expect(getByTestId('contact-request-container')).toBeInTheDocument();
  });
});
