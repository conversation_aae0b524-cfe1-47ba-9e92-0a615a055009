import { useSearchParams } from 'react-router-dom';

import ResetPasswordContainer from 'src/components/containers/reset-password-container/ResetPasswordContainer';

import { ResetPasswordPageWrapper } from './ResetPasswordPage.style';

export const ResetPasswordPage = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');

  return (
    <ResetPasswordPageWrapper>
      <ResetPasswordContainer token={token} />
    </ResetPasswordPageWrapper>
  );
};
