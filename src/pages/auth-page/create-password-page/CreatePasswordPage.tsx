import { useSearchParams } from 'react-router-dom';

import CreatePasswordContainer from 'src/components/containers/create-password-container/CreatePasswordContainer';

import { CreatePasswordPageWrapper } from './CreatePasswordPage.style';

export const CreatePasswordPage = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token')
    ? decodeURIComponent(searchParams.get('token')!).replace(/ /g, '+')
    : '';

  return (
    <CreatePasswordPageWrapper>
      <CreatePasswordContainer token={token} />
    </CreatePasswordPageWrapper>
  );
};
