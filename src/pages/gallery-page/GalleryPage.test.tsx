import { render } from '@testing-library/react';

import GalleryPage from './GalleryPage';

jest.mock('src/components/containers/gallery-container/GalleryContainer', () => () => (
  <div data-testid="gallery-container" />
));

describe('GalleryPage', () => {
  it('renders the GalleryContainer', () => {
    const { getByTestId } = render(<GalleryPage />);
    expect(getByTestId('gallery-container')).toBeInTheDocument();
  });
});
