import { render } from '@testing-library/react';

import TestimonialPage from './TestimonialPage';

jest.mock('src/components/containers/testimonial-container/TestimonialContainer', () => () => (
  <div data-testid="testimonial-container" />
));

describe('TestimonialPage', () => {
  it('renders the TestimonialContainer', () => {
    const { getByTestId } = render(<TestimonialPage />);
    expect(getByTestId('testimonial-container')).toBeInTheDocument();
  });
});
