import type { ReactNode } from 'react';
import type { Errors } from 'src/shared/types/errorResonse';
import type { SignInResponse } from 'src/shared/services/admin/users/users.types';

import axios from 'axios';
import { useMemo, useState, useEffect, useContext, createContext } from 'react';

import { signIn } from 'src/shared/services/auth/auth.service';

import { useToast } from './ToastProvider';

interface AdminUser {
  email: string;
  password: string;
}

interface AuthContextType {
  token: string | null;
  userName: string;
  email: string;
  role: string;
  loading: boolean;
  login: (credentials: AdminUser) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

const AuthProvider = ({ children }: AuthProviderProps) => {
  const [token, setToken_] = useState<string | null>(localStorage.getItem('token'));
  const [loading, setLoading_] = useState<boolean>(false);
  const [userName, setUserName_] = useState<string>(localStorage.getItem('userName') || '');
  const [email, setEmail_] = useState<string>('');
  const [role, setRole_] = useState<string>(localStorage.getItem('role') || '');
  const { showToast } = useToast();

  const setLoading = (load: boolean) => {
    setLoading_(load);
  };

  const setToken = (newToken: string | null) => {
    setToken_(newToken);
  };

  const setUserName = (newUserName: string) => {
    setUserName_(newUserName);
  };

  const setRole = (newRole: string) => {
    setRole_(newRole);
  };

  const setEmail = (newEmail: string) => {
    setEmail_(newEmail);
  };

  const login = async (credentials: AdminUser): Promise<void> => {
    try {
      setLoading(true);
      const response = await signIn(credentials);

      const {
        token: newToken,
        userName: newUserName,
        email: newEmail,
        role: newRole,
      } = response as SignInResponse;
      setToken(newToken);
      setUserName(newUserName ?? '');
      setEmail(newEmail ?? '');
      setRole(newRole ?? '');

      showToast('Successfully Logged in', 'success');
    } catch (error) {
      const errorResponse = error as Errors;

      const errorMessage =
        typeof errorResponse.errors === 'string'
          ? errorResponse.errors
          : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

      showToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setLoading(true);
    setToken(null);
    setUserName('');
    setEmail('');
    setRole('');
    setLoading(false);
    showToast(`You've been logged out`, 'info');
  };

  useEffect(() => {
    if (token) {
      axios.defaults.headers.common.Authorization = `Bearer ${token}`;
      localStorage.setItem('token', token);
    } else {
      delete axios.defaults.headers.common.Authorization;
      localStorage.removeItem('token');
    }
  }, [token]);

  useEffect(() => {
    if (userName) localStorage.setItem('userName', userName);
    else localStorage.removeItem('userName');
  }, [userName]);

  useEffect(() => {
    if (role) localStorage.setItem('role', role);
    else localStorage.removeItem('role');
  }, [role]);

  const contextValue = useMemo(
    () => ({
      token,
      userName,
      email,
      role,
      loading,
      login,
      logout,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [token, userName, email, role, loading]
  );

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
