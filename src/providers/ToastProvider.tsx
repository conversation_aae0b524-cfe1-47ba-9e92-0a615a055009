import type { ReactNode } from 'react';
import type { AlertColor } from '@mui/material';

import { useSnackbar, SnackbarProvider } from 'notistack';
import React, { useContext, useCallback, createContext } from 'react';

import { Alert } from '@mui/material';

interface ToastContextType {
  showToast: (message: string, severity?: AlertColor) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const showToast = useCallback(
    (message: string, severity: AlertColor = 'success') => {
      enqueueSnackbar(message, {
        variant: severity,
        anchorOrigin: { vertical: 'top', horizontal: 'right' },
        autoHideDuration: 6000,
        preventDuplicate: true,
        content: (key: any) => (
          <Alert onClose={() => closeSnackbar(key)} severity={severity} variant="standard">
            {message}
          </Alert>
        ),
      });
    },
    [enqueueSnackbar, closeSnackbar]
  );

  const contextValue = React.useMemo(() => ({ showToast }), [showToast]);

  return <ToastContext.Provider value={contextValue}>{children}</ToastContext.Provider>;
};

const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

const AppToastProvider: React.FC<ToastProviderProps> = ({ children }) => (
  <SnackbarProvider maxSnack={5} autoHideDuration={6000} preventDuplicate>
    <ToastProvider>{children}</ToastProvider>
  </SnackbarProvider>
);

export { useToast, AppToastProvider as ToastProvider };
