export const downloadFile = async (url: string): Promise<void> => {
  try {
    const response = await fetch(url);
    const blob = await response.blob();
    const fileName = url.split('/').pop() || 'download';

    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw new Error('Failed to download file');
  }
};

export const downloadMultipleFiles = async (urls: string[]): Promise<void> => {
  try {
    await Promise.all(urls.map((url) => downloadFile(url)));
  } catch (error) {
    console.error('Error downloading multiple files:', error);
    throw new Error('Failed to download one or more files');
  }
};
