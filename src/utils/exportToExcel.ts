import * as XLSX from 'xlsx';

function resolveNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((acc, key) => {
    if (acc && typeof acc === 'object') {
      return acc[key];
    }
    return undefined;
  }, obj);
}

export function exportToExcel<T>(
  data: T[],
  keysToInclude: string[],
  fileName: string = 'export.xlsx'
) {
  const filteredData = data.map((item) => {
    const filteredItem: Record<string, string> = {};
    keysToInclude.forEach((keyPath) => {
      const value = resolveNestedValue(item, keyPath);
      filteredItem[keyPath] = value !== undefined && value !== null ? String(value) : '';
    });
    return filteredItem;
  });

  const worksheet = XLSX.utils.json_to_sheet(filteredData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

  const excelBuffer = XLSX.write(workbook, {
    bookType: 'xlsx',
    type: 'array',
  });

  const blob = new Blob([excelBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
