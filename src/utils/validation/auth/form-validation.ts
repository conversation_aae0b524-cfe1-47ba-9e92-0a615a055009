export const validate = {
  email: (email: string) => {
    let isValidEmail = true;
    let emailErrorMsg = '';

    if (!email) {
      emailErrorMsg = 'Email is required';
      isValidEmail = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      emailErrorMsg = 'Email address is invalid';
      isValidEmail = false;
    }

    return { isValidEmail, emailErrorMsg };
  },

  password: (password: string) => {
    let passErrorMsg = '';
    let isValidPass = true;

    if (!password) {
      passErrorMsg = 'Password is required';
      isValidPass = false;
    } else if (password.length < 6) {
      passErrorMsg = 'Password must be at least 6 characters';
      isValidPass = false;
    }

    return { isValidPass, passErrorMsg };
  },

  isSamePassword: ({
    password,
    confirmPassword,
  }: {
    password: string;
    confirmPassword: string;
  }) => {
    let passErrorMsg = '';
    let confirmPassErrorMsg = '';
    let globalErrorMsg = '';
    let isValidPass = true;

    if (!password) {
      passErrorMsg = 'New password is required';
      isValidPass = false;
    }
    if (!confirmPassword) {
      confirmPassErrorMsg = 'Confirm password is required';
      isValidPass = false;
    }
    if (password && confirmPassword && password !== confirmPassword) {
      globalErrorMsg = 'Passwords do not match';
      isValidPass = false;
    }

    return { isValidPass, passErrorMsg, confirmPassErrorMsg, globalErrorMsg };
  },
};
