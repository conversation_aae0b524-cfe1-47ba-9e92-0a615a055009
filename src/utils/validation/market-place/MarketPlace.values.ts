import type { ddValue } from 'src/shared/types/ddValue';
import type { MarketPlace } from 'src/shared/services/market-place/market-place.type';

import { MarketPlaceStatus } from 'src/shared/mock/market-place';

export const initialMarketPlace = (data?: MarketPlace): MarketPlace => ({
  id: data?.id ?? 0,
  name: data?.name ?? '',
  contactNumber: data?.contactNumber ?? '',
  email: data?.email ?? '',
  category: data?.category ?? '',
  location: data?.location ?? '',
  status: data?.status ?? ({ id: 0, name: '', isDisabled: false } as ddValue),
  address: data?.address ?? '',
  title: data?.title ?? '',
  expectingPrice: data?.expectingPrice ?? 0,
  description: data?.description ?? '',
  message: data?.message ?? '',
  images: data?.images ?? [],
  action: data?.action ?? {
    adminId: null,
    action: MarketPlaceStatus[0],
    remark: '',
  },
});
