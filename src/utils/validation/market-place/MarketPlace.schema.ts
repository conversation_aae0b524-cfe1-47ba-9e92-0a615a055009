import type { ddValue } from 'src/shared/types/ddValue';

import * as Yup from 'yup';

const marketPlaceValidationSchema = () =>
  Yup.object().shape({
    id: Yup.number().required('ID is required'),

    name: Yup.string()
      .required('Name is required')
      .min(2, 'Name must be at least 2 characters')
      .max(50, 'Name must not exceed 50 characters'),

    contactNumber: Yup.string()
      .required('Contact number is required')
      .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit Indian mobile number'),

    email: Yup.string().required('Email is required').email('Invalid email format'),

    category: Yup.string()
      .required('Category is required')
      .min(2, 'Category must be at least 2 characters'),

    location: Yup.string()
      .required('Location is required')
      .min(2, 'Location must be at least 2 characters'),

    status: Yup.mixed<ddValue>()
      .required('Status is required')
      .test(
        'valid-status',
        'Invalid status',
        (value) => value && typeof value === 'object' && 'id' in value && 'name' in value
      ),

    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),

    title: Yup.string()
      .required('Title is required')
      .min(5, 'Title must be at least 5 characters')
      .max(100, 'Title must not exceed 100 characters'),

    expectingPrice: Yup.number()
      .required('Expecting price is required')
      .min(0, 'Price must be greater than or equal to 0')
      .test('is-decimal', 'Price must have at most 2 decimal places', (value) => {
        if (!value) return true;
        return /^\d+(\.\d{1,2})?$/.test(value.toString());
      }),

    description: Yup.string()
      .required('Description is required')
      .min(20, 'Description must be at least 20 characters'),

    message: Yup.string().optional().min(5, 'Message must be at least 5 characters'),

    images: Yup.array()
      .of(Yup.string().url('Must be a valid URL'))
      .min(1, 'At least one image is required'),

    action: Yup.object().shape({
      adminId: Yup.number().nullable(),
      action: Yup.object().shape({
        id: Yup.number().required('Action ID is required'),
        name: Yup.string().required('Action name is required'),
      }),
      remark: Yup.string().nullable(),
    }),
  });

export type MarketPlaceValidationType = Yup.InferType<
  ReturnType<typeof marketPlaceValidationSchema>
>;
export default marketPlaceValidationSchema;
