export interface ShipmentTrackingFormValues {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddressLine1: string;
  customerAddressLine2: string;
  customerCity: string;
  customerState: string;
  customerCountry: string;
  customerPincode: string;
  originPincode: string;
  destinationPincode: string;
  contentType: string;
  shipmentValue: string;
  serviceCategoryType: string;
  contentDescription: string;
  weight: string;
  weightUnit: string;
  length: string;
  width: string;
  height: string;
  originAddress: string;
  destinationAddress: string;
  shipmentType: string;
  pickupLocation: any;
  pickupAddressLine1: string;
  pickupAddressLine2: string;
  pickupCity: string;
  pickupState: string;
  pickupZipCode: string;
  pickupCountry: string;
  deliveryLocation: any;
  deliveryAddressLine1: string;
  deliveryAddressLine2: string;
  deliveryCity: string;
  deliveryState: string;
  deliveryZipCode: string;
  deliveryCountry: string;
  recipientName: string;
  recipientPhone: string;
  deliveryCountryType: string;
  deliveryType: string;
  shippingCost: string;
  tax: string;
  totalCost: string;
}

export const shipmentTrackingInitialValues: ShipmentTrackingFormValues = {
  customerName: '',
  customerEmail: '',
  customerPhone: '',
  customerAddressLine1: '',
  customerAddressLine2: '',
  customerCity: '',
  customerState: '',
  customerCountry: '',
  customerPincode: '',
  originPincode: '',
  destinationPincode: '',
  contentType: '',
  shipmentValue: '',
  contentDescription: '',
  weight: '',
  weightUnit: '',
  length: '',
  width: '',
  height: '',
  originAddress: '',
  destinationAddress: '',
  shipmentType: '',
  serviceCategoryType: '',
  pickupLocation: null,
  pickupAddressLine1: '',
  pickupAddressLine2: '',
  pickupCity: '',
  pickupState: '',
  pickupZipCode: '',
  pickupCountry: '',
  deliveryLocation: null,
  deliveryAddressLine1: '',
  deliveryAddressLine2: '',
  deliveryCity: '',
  deliveryState: '',
  deliveryZipCode: '',
  deliveryCountry: '',
  recipientName: '',
  recipientPhone: '',
  deliveryCountryType: '',
  deliveryType: '',
  shippingCost: '',
  tax: '',
  totalCost: '',
};
