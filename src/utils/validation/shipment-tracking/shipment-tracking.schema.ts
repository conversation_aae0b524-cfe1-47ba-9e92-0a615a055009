import * as Yup from 'yup';

export const customerInformationSchema = Yup.object().shape({
  customerName: Yup.string().required('Customer name is required'),
  customerEmail: Yup.string().email('Invalid email').required('Email is required'),
  customerPhone: Yup.string().required('Phone number is required'),
  customerAddressLine1: Yup.string().required('Address is required'),
  customerCity: Yup.string().required('City is required'),
  customerState: Yup.string().required('State is required'),
  customerCountry: Yup.string().required('Country is required'),
  customerPincode: Yup.string().required('Pincode is required'),
});

export const originDestinationSchema = Yup.object().shape({
  originPincode: Yup.string().required('Origin pincode is required'),
  destinationPincode: Yup.string().required('Destination pincode is required'),
});

export const packageDetailsSchema = Yup.object().shape({
  contentType: Yup.string().required('Content type is required'),
  shipmentValue: Yup.number().required('Shipment value is required'),
  contentDescription: Yup.string().required('Description is required'),
});

export const serviceOptionsSchema = Yup.object().shape({
  serviceCategoryType: Yup.string().required('Shipping service is required'),
});

export const pickupDeliverySchema = Yup.object().shape({
  shipmentType: Yup.string().required('Shipment type is required'),
  pickupLocation: Yup.mixed().required('Please search for a pickup location'),
  pickupAddressLine1: Yup.string().required('Pickup address is required'),
  pickupCity: Yup.string().required('Pickup city is required'),
  pickupState: Yup.string().required('Pickup state is required'),
  pickupZipCode: Yup.string().required('Pickup zip code is required'),
  pickupCountry: Yup.string().required('Pickup country is required'),
  deliveryLocation: Yup.mixed().required('Please search for a delivery location'),
  deliveryAddressLine1: Yup.string().required('Delivery address is required'),
  deliveryCity: Yup.string().required('Delivery city is required'),
  deliveryState: Yup.string().required('Delivery state is required'),
  deliveryZipCode: Yup.string().required('Delivery zip code is required'),
  deliveryCountry: Yup.string().required('Delivery country is required'),
  recipientName: Yup.string().required('Recipient name is required'),
});

export const paymentDetailsSchema = Yup.object().shape({
  shippingCost: Yup.number().required('Shipping cost is required'),
  tax: Yup.number().required('Tax is required'),
  totalCost: Yup.number().required('Total cost is required'),
});
