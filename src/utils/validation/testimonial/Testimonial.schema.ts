import * as Yup from 'yup';

export const testimonialSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  company: Yup.string().required('Company is required'),
  rating: Yup.number()
    .required('Rating is required')
    .min(1, 'Rating must be at least 1')
    .max(5, 'Rating cannot be more than 5'),
  text: Yup.string()
    .required('Testimonial text is required')
    .min(20, 'Testimonial text must be at least 20 characters'),
  type: Yup.object().shape({
    id: Yup.number().required('Testimonial type is required'),
    name: Yup.string().required('Testimonial type name is required'),
  }),
});
