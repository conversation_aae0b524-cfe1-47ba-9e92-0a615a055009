import type { Testimonial } from 'src/shared/services/testimonial/testimonial.type';

import { TestimonialTypes } from 'src/shared/mock/testimonial';

export const initialTestimonial = (data?: Testimonial): Testimonial => ({
  id: data?.id ?? 0,
  name: data?.name ?? '',
  company: data?.company ?? '',
  rating: data?.rating ?? 5,
  text: data?.text ?? '',
  type: data?.type ?? TestimonialTypes[0],
});
