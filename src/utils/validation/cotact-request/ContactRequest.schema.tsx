import * as Yup from 'yup';

export const contactRequestSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  emailId: Yup.string().required('Email is required').email('Invalid email format'),
  phone: Yup.string()
    .required('Phone number is required')
    .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit Indian mobile number'),
  preferredContactMethodId: Yup.string().required('Preferred contact method is required'),
  reference: Yup.string().required('Reference name is required'),
  subject: Yup.string().required('Subject name is required'),

  message: Yup.string().required('Message is required'),
  statusId: Yup.string().required('Status is required'),
  assignedAdminId: Yup.string().required('Assigned admin is required'),
  adminActionRemark: Yup.string().optional(),
});
