import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { ContactRequest } from 'src/shared/services/contact-request/contact-request.type';

export const initialContactRequest = (data?: ContactRequest): ContactRequest => ({
  id: data?.id ?? 0,
  name: data?.name ?? '',
  phone: data?.phone ?? '',
  preferredContactMethod: data?.preferredContactMethod ?? ({} as ddValue),
  emailId: data?.emailId ?? '',
  reference: data?.reference ?? '',
  subject: data?.subject ?? '',
  message: data?.message ?? '',
  status: data?.status ?? ({} as ddValue),
  assignedAdmin: data?.assignedAdmin ?? ({} as AdminUser),
  adminActionRemark: '',
  assignedAdminId: data?.assignedAdminId ?? 0,
  preferredContactMethodId: data?.preferredContactMethodId ?? 0,
  statusId: data?.statusId ?? 0,
});
