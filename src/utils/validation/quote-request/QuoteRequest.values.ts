import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';
import type { ddValue, ServiceDDValue, ServiceSubCategoryDDValue } from 'src/shared/types/ddValue';

export const initialQuoteRequest = (data?: QuoteRequest): QuoteRequest => ({
  id: data?.id ?? 0,
  name: data?.name ?? '',
  phone: data?.phone ?? '',
  emailId: data?.emailId ?? '',
  service: data?.service ?? ({} as ServiceDDValue),
  status: data?.status ?? ({} as ddValue),
  preferredContactMethod: data?.preferredContactMethod ?? ({} as ddValue),
  serviceSubCategory: data?.serviceSubCategory ?? ({} as ServiceSubCategoryDDValue),
  origin: data?.origin ?? '',
  destination: data?.destination ?? '',
  message: data?.message ?? '',
  assignedAdmin: data?.assignedAdmin ?? null,
  serviceSubCategoryId: data?.serviceSubCategoryId ?? 0,
  assignedAdminId: data?.assignedAdminId ?? 0,
  preferredContactMethodId: data?.preferredContactMethodId ?? 0,
  statusId: data?.statusId ?? 0,
  serviceId: data?.serviceId ?? 0,
  adminActionRemark: '',
});
