import type {
  InstantEstimation,
  EstimationResponse,
} from 'src/shared/services/instant-quote/instant-quote.type';

export const initialInstantEstimation = (data?: InstantEstimation): InstantEstimation => ({
  id: data?.id ?? 0,
  name: data?.name ?? '',
  phone: data?.phone ?? '',
  emailId: data?.emailId ?? '',
  serviceId: data?.serviceId ?? 0,
  createdDate: data?.createdDate ?? '',
  requestDataJson: data?.requestDataJson ?? undefined,
  response: data?.response ?? ({} as EstimationResponse),
  assignedAdminId: data?.assignedAdminId ?? 0,
  statusId: data?.statusId ?? 0,
});
