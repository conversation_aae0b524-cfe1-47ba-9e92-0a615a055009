import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

export const initialJobPosting = (data?: JobPosting): JobPosting => ({
  id: data?.id ?? 0,
  jobPositionId: data?.jobPositionId ?? 0,
  jobPostingStatusId: data?.jobPostingStatusId ?? 0,
  jobPostingTitle: (data?.jobPostingTitle || data?.jobPosition?.positionTitle) ?? '',
  jobPostingDescription: (data?.jobPostingDescription || data?.jobPosition?.jobDescription) ?? '',
  jobPostingRequirements:
    (data?.jobPostingRequirements || data?.jobPosition?.jobRequirements) ?? '',
  minimumExperienceInYears: data?.minimumExperienceInYears ?? 0,
  jobLocations: data?.jobLocations ?? [],
  lastModified: data?.lastModified ?? '',
  createdDate: data?.createdDate ?? '',
  createdById: data?.createdById ?? 0,
  lastModifiedById: data?.lastModifiedById ?? 0,
  shouldKeepOpenUntilStopped: data?.shouldKeepOpenUntilStopped ?? false,
  createdByUserName: data?.createdByUserName ?? '',
  lastModifiedByUserName: data?.lastModifiedByUserName ?? '',
  schedulePostingStartDate: data?.schedulePostingStartDate ?? '',
  schedulePostingEndTime: data?.schedulePostingEndTime ?? '',
  jobPosition: data?.jobPosition ?? ({} as JobPosition),
  jobPostingStatus: data?.jobPostingStatus ?? null,
});
