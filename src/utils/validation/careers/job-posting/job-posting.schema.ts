import * as Yup from 'yup';

export const JobPostingsSchema = Yup.object().shape({
  jobPositionId: Yup.number().required('Job Position is required'),
  jobPostingStatusId: Yup.number().required('Status is required'),
  jobPostingTitle: Yup.string().required('Position Title is required'),
  jobPostingDescription: Yup.string().required('Job Description is required'),
  jobPostingRequirements: Yup.string().required('Job Requirements are required'),
  minimumExperienceInYears: Yup.string()
    .matches(/^\d+$/, 'Minimum experience must be a number')
    .required('Minimum Experience is required'),
  jobLocations: Yup.array()
    .of(Yup.string().required('Location is required'))
    .min(1, 'At least one location is required'),
});
