import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

export const initialJobPosition = (data?: JobPosition): JobPosition => ({
  id: data?.id ?? 0,
  departmentId: data?.departmentId ?? 0,
  positionTitle: data?.positionTitle ?? '',
  jobPositionTypeId: data?.jobPositionTypeId ?? 0,
  jobPositionStatusId: data?.jobPositionStatusId ?? 0,
  jobDescription: data?.jobDescription ?? '',
  jobRequirements: data?.jobRequirements ?? '',
  jobCode: data?.jobCode ?? '',
  lastModified: data?.lastModified ?? '',
  createdDate: data?.createdDate ?? '',
  createdById: data?.createdById ?? 0,
  lastModifiedById: data?.lastModifiedById ?? 0,
});
