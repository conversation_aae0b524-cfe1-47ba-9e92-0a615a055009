import * as yup from 'yup';

export const jobPositionSchema = yup.object().shape({
  id: yup.number().required(),
  departmentId: yup.number().required('Department is required'),
  positionTitle: yup.string().required('Position title is required'),
  jobPositionTypeId: yup.number().required('Job position type is required'),
  jobPositionStatusId: yup.number().required('Job position status is required'),
  jobDescription: yup.string().required('Job description is required'),
  jobRequirements: yup.string().required('Job requirements are required'),
  jobCode: yup.string().required('Job code is required'),
});
