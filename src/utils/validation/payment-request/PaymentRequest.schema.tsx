import type { ddValue } from 'src/shared/types/ddValue';

import * as Yup from 'yup';

const paymentRequestValidationSchema = () =>
  Yup.object().shape({
    id: Yup.number().required('ID is required'),
    customerName: Yup.string()
      .required('Customer name is required')
      .min(2, 'Customer name must be at least 2 characters')
      .max(50, 'Customer name must not exceed 50 characters'),

    date: Yup.string()
      .required('Date is required')
      .matches(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/,
        'Invalid date format. Use ISO format'
      ),

    paymentId: Yup.string()
      .required('Payment ID is required')
      .min(2, 'Payment ID must be at least 2 characters'),

    contactNumber: Yup.string()
      .required('Contact number is required')
      .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit Indian mobile number'),

    city: Yup.string()
      .required('City is required')
      .min(2, 'City must be at least 2 characters')
      .max(50, 'City must not exceed 50 characters'),

    status: Yup.mixed<ddValue>()
      .required('Status is required')
      .test(
        'valid-status',
        'Invalid status',
        (value) => value && typeof value === 'object' && 'id' in value && 'name' in value
      ),

    service: Yup.string()
      .required('Service is required')
      .min(2, 'Service must be at least 2 characters'),

    subCategory: Yup.string()
      .required('Sub category is required')
      .min(2, 'Sub category must be at least 2 characters'),

    amount: Yup.number()
      .required('Amount is required')
      .min(0, 'Amount must be greater than or equal to 0')
      .test('is-decimal', 'Amount must have at most 2 decimal places', (value) => {
        if (!value) return true;
        return /^\d+(\.\d{1,2})?$/.test(value.toString());
      }),

    destination: Yup.string()
      .required('Destination is required')
      .min(2, 'Destination must be at least 2 characters'),
    invoice: Yup.object()
      .shape({
        id: Yup.string(),
        fileName: Yup.string(),
        fileUrl: Yup.string().url('Invalid file URL'),
        uploadedAt: Yup.string(),
      })
      .nullable(),
  });

export type PaymentRequestValidationType = Yup.InferType<
  ReturnType<typeof paymentRequestValidationSchema>
>;
export default paymentRequestValidationSchema;
