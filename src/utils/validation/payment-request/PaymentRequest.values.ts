import type { ddValue } from 'src/shared/types/ddValue';
import type { PaymentRequest } from 'src/shared/services/payment-request/payment-request.type';

export const initialPaymentRequest = (data?: PaymentRequest): PaymentRequest => ({
  id: data?.id ?? 0,
  customerName: data?.customerName ?? '',
  date: data?.date ?? new Date().toISOString(),
  paymentId: data?.paymentId ?? '',
  contactNumber: data?.contactNumber ?? '',
  city: data?.city ?? '',
  status: data?.status ?? ({ id: 0, name: '', isDisabled: false } as ddValue),
  service: data?.service ?? '',
  subCategory: data?.subCategory ?? '',
  amount: data?.amount ?? 0,
  destination: data?.destination ?? '',
});
