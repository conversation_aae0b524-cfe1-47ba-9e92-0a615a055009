export function formatDate(date: string | number | Date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

export const formatTime = (date: string | number | Date) =>
  new Date(date).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
  });

export const formatDateTime = (date: string | number | Date) => {
  const d = new Date(date);
  const month = d.toLocaleString('en-US', { month: 'short' });
  const day = d.getDate();
  const year = d.getFullYear();
  const hour = d.getHours() % 12 || 12;
  const minute = d.getMinutes().toString().padStart(2, '0');
  const period = d.getHours() >= 12 ? 'PM' : 'AM';

  return `${month} ${day}, ${year} • ${hour}:${minute} ${period}`;
};
