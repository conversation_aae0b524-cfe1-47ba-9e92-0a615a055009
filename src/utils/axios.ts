import type { AxiosError, AxiosResponse } from 'axios';

import axios from 'axios';

import { CONFIG } from 'src/config-global';

const axiosInstance = axios.create({ baseURL: CONFIG.site.serverUrl });

axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error) => Promise.reject((error.response && error.response.data) || 'Something went wrong!')
);

const setupInterceptors = (logout: () => void): void => {
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      console.log('ERROR', error.code);
      if (error.code === 'ERR_NETWORK') {
        localStorage.removeItem('token');
        logout();
      }
      return Promise.reject(error);
    }
  );
};

export { setupInterceptors };
export default axiosInstance;
