import type { Shadows } from '@mui/material/styles';

import type { ThemeColorScheme } from '../types';

export function shadows(colorScheme: ThemeColorScheme): Shadows {
  const shadowColor = colorScheme === 'light' ? 'rgba(0, 0, 0, 0.08)' : 'rgba(0, 0, 0, 0.15)';

  const softShadowColor = colorScheme === 'light' ? 'rgba(0, 0, 0, 0.04)' : 'rgba(0, 0, 0, 0.08)';

  return [
    'none',
    `0px 1px 1px -1px ${softShadowColor}, 0px 1px 1px 0px ${softShadowColor}, 0px 1px 2px 0px ${shadowColor}`,
    `0px 1px 1px -1px ${softShadowColor}, 0px 1px 2px 0px ${softShadowColor}, 0px 1px 3px 0px ${shadowColor}`,
    `0px 1px 2px -1px ${softShadowColor}, 0px 2px 2px 0px ${softShadowColor}, 0px 1px 4px 0px ${shadowColor}`,
    `0px 1px 2px -1px ${softShadowColor}, 0px 2px 3px 0px ${softShadowColor}, 0px 1px 5px 0px ${shadowColor}`,
    `0px 1px 3px -1px ${softShadowColor}, 0px 3px 4px 0px ${softShadowColor}, 0px 1px 7px 0px ${shadowColor}`,
    `0px 2px 3px -1px ${softShadowColor}, 0px 3px 5px 0px ${softShadowColor}, 0px 1px 9px 0px ${shadowColor}`,
    `0px 2px 3px -1px ${softShadowColor}, 0px 4px 5px 0px ${softShadowColor}, 0px 1px 10px 0px ${shadowColor}`,
    `0px 2px 4px -1px ${softShadowColor}, 0px 4px 6px 0px ${softShadowColor}, 0px 2px 10px 0px ${shadowColor}`,
    `0px 3px 4px -2px ${softShadowColor}, 0px 5px 7px 0px ${softShadowColor}, 0px 2px 11px 0px ${shadowColor}`,
    `0px 3px 4px -2px ${softShadowColor}, 0px 5px 8px 0px ${softShadowColor}, 0px 2px 12px 0px ${shadowColor}`,
    `0px 3px 5px -2px ${softShadowColor}, 0px 6px 8px 0px ${softShadowColor}, 0px 2px 13px 0px ${shadowColor}`,
    `0px 3px 5px -2px ${softShadowColor}, 0px 6px 9px 0px ${softShadowColor}, 0px 3px 14px 0px ${shadowColor}`,
    `0px 4px 5px -2px ${softShadowColor}, 0px 6px 10px 0px ${softShadowColor}, 0px 3px 15px 0px ${shadowColor}`,
    `0px 4px 5px -2px ${softShadowColor}, 0px 7px 10px 0px ${softShadowColor}, 0px 3px 16px 0px ${shadowColor}`,
    `0px 4px 6px -2px ${softShadowColor}, 0px 7px 10px 0px ${softShadowColor}, 0px 3px 16px 0px ${shadowColor}`,
    `0px 4px 6px -2px ${softShadowColor}, 0px 8px 11px 0px ${softShadowColor}, 0px 3px 17px 0px ${shadowColor}`,
    `0px 5px 6px -3px ${softShadowColor}, 0px 8px 12px 0px ${softShadowColor}, 0px 4px 17px 0px ${shadowColor}`,
    `0px 5px 7px -3px ${softShadowColor}, 0px 9px 12px 0px ${softShadowColor}, 0px 4px 18px 0px ${shadowColor}`,
    `0px 5px 7px -3px ${softShadowColor}, 0px 9px 13px 0px ${softShadowColor}, 0px 4px 18px 0px ${shadowColor}`,
    `0px 5px 8px -3px ${softShadowColor}, 0px 10px 14px 0px ${softShadowColor}, 0px 4px 19px 0px ${shadowColor}`,
    `0px 6px 8px -3px ${softShadowColor}, 0px 10px 14px 0px ${softShadowColor}, 0px 4px 19px 0px ${shadowColor}`,
    `0px 6px 8px -4px ${softShadowColor}, 0px 11px 15px 0px ${softShadowColor}, 0px 4px 20px 0px ${shadowColor}`,
    `0px 6px 9px -4px ${softShadowColor}, 0px 11px 16px 0px ${softShadowColor}, 0px 5px 20px 0px ${shadowColor}`,
    `0px 6px 9px -4px ${softShadowColor}, 0px 12px 17px 0px ${softShadowColor}, 0px 5px 21px 0px ${shadowColor}`,
  ];
}
