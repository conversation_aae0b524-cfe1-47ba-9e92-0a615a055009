import type { ColorSystemOptions } from '@mui/material/styles';

import COLORS from './colors.json';

export const { primary, secondary, info, success, warning, error, grey, common } = COLORS;

declare module '@mui/material/styles/createPalette' {
  interface CommonColors {
    white: string;
    black: string;
  }
  interface TypeText {
    disabled: string;
  }
  interface TypeBackground {
    neutral: string;
  }
  interface SimplePaletteColorOptions {
    lighter: string;
    light?: string;
    main: string;
    dark?: string;
    darker: string;
    contrastText?: string;
  }
  interface PaletteColor {
    lighter: string;
    light: string;
    main: string;
    dark: string;
    darker: string;
    contrastText: string;
  }
}

export type ColorType = 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error';

export const text = {
  light: { primary: grey[800], secondary: grey[600], disabled: grey[500] },
  dark: { primary: common.white, secondary: grey[500], disabled: grey[600] },
};

export const background = {
  light: { paper: common.white, default: grey[50], neutral: grey[200] },
  dark: { paper: grey[800], default: grey[900], neutral: grey[700] },
};

export const baseAction = {
  hover: grey[100],
  selected: grey[100],
  focus: grey[100],
  disabled: grey[300],
  disabledBackground: grey[300],
  hoverOpacity: 0.08,
  disabledOpacity: 0.48,
};

export const action = {
  light: { ...baseAction, active: grey[600] },
  dark: { ...baseAction, active: grey[500] },
};

export const basePalette = {
  primary,
  secondary,
  info,
  success,
  warning,
  error,
  grey,
  common,
  divider: 'rgba(0, 0, 0, 0.2)',
  action,
};

export const lightPalette = {
  ...basePalette,
  text: text.light,
  background: background.light,
  action: action.light,
};

export const darkPalette = {
  ...basePalette,
  text: text.dark,
  background: background.dark,
  action: action.dark,
};

export const colorSchemes: Partial<Record<'dark' | 'light', ColorSystemOptions>> = {
  light: { palette: lightPalette },
  dark: { palette: darkPalette },
};
