import type { Theme, CSSObject, Components, ComponentsVariants } from '@mui/material/styles';

import { alpha } from '@mui/material/styles';
import { loadingButtonClasses } from '@mui/lab/LoadingButton';
import { buttonClasses, type ButtonProps } from '@mui/material/Button';

declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    soft: true;
  }
}

const COLORS = ['primary', 'secondary', 'info', 'success', 'warning', 'error'] as const;

type ColorType = (typeof COLORS)[number];

function styleColors(ownerState: ButtonProps, styles: (val: ColorType) => CSSObject) {
  const outputStyle = COLORS.reduce((acc, color) => {
    if (!ownerState.disabled && ownerState.color === color) {
      acc = styles(color);
    }
    return acc;
  }, {});

  return outputStyle;
}

const MuiButtonBase: Components<Theme>['MuiButtonBase'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      fontFamily: theme.typography.fontFamily,
    }),
  },
};

const softVariant: Record<string, ComponentsVariants<Theme>['MuiButton']> = {
  colors: COLORS.map((color) => ({
    props: ({ ownerState }) =>
      !ownerState.disabled && ownerState.variant === 'soft' && ownerState.color === color,
    style: ({ theme }) => ({
      color: theme.palette[color].main,
      backgroundColor: theme.palette[color].lighter,
      '&:hover': {
        backgroundColor: alpha(theme.palette[color].main, 0.16),
      },
      [`&.${buttonClasses.disabled}`]: {
        color: theme.palette.grey[500],
        backgroundColor: theme.palette.grey[200],
      },
    }),
  })),
  base: [
    {
      props: ({ ownerState }) => ownerState.variant === 'soft',
      style: ({ theme }) => ({
        backgroundColor: theme.palette.grey[100],
        color: theme.palette.grey[800],
        '&:hover': {
          backgroundColor: theme.palette.grey[200],
        },
        [`&.${buttonClasses.disabled}`]: {
          backgroundColor: theme.palette.grey[200],
          color: theme.palette.grey[500],
        },
        [`& .${loadingButtonClasses.loadingIndicatorStart}`]: { left: 14 },
        [`& .${loadingButtonClasses.loadingIndicatorEnd}`]: { right: 14 },
        [`&.${buttonClasses.sizeSmall}`]: {
          [`& .${loadingButtonClasses.loadingIndicatorStart}`]: { left: 10 },
          [`& .${loadingButtonClasses.loadingIndicatorEnd}`]: { right: 10 },
        },
      }),
    },
  ],
};

const MuiButton: Components<Theme>['MuiButton'] = {
  defaultProps: {
    color: 'primary',
    disableElevation: true,
  },
  variants: [...[...softVariant.base!, ...softVariant.colors!]],
  styleOverrides: {
    root: ({ theme }) => ({
      borderRadius: theme.shape.borderRadius,
      textTransform: 'none',
      fontWeight: 600,
      transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {
        duration: theme.transitions.duration.short,
      }),
    }),
    contained: ({ theme, ownerState }) => {
      const styled = {
        colors: styleColors(ownerState, (color) => ({
          backgroundColor: theme.palette[color].main,
          color: theme.palette[color].contrastText,
          '&:hover': {
            backgroundColor: theme.palette[color].dark,
            boxShadow: 'none',
          },
          [`&.${buttonClasses.disabled}`]: {
            backgroundColor: theme.palette.grey[300],
            color: theme.palette.grey[900],
          },
        })),
        inheritColor: {
          ...(ownerState.color === 'inherit' &&
            !ownerState.disabled && {
              color: theme.palette.grey[800],
              backgroundColor: theme.palette.grey[800],
              '&:hover': {
                backgroundColor: theme.palette.grey[900],
                boxShadow: 'none',
              },
            }),
          ':disabled': {
            backgroundColor: theme.palette.grey[200],
            color: theme.palette.grey[500],
          },
        },
      };
      return { ...styled.inheritColor, ...styled.colors };
    },
    outlined: ({ theme, ownerState }) => {
      const styled = {
        colors: styleColors(ownerState, (color) => ({
          borderColor: theme.palette[color].main,
          color: theme.palette[color].main,
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: alpha(theme.palette[color].lighter, 0.5),
            borderColor: theme.palette[color].dark,
          },
          [`&.${buttonClasses.disabled}`]: {
            borderColor: theme.palette.grey[300],
            color: theme.palette.grey[900],
          },
        })),
        inheritColor: {
          ...(ownerState.color === 'inherit' &&
            !ownerState.disabled && {
              borderColor: theme.palette.grey[500],
              color: theme.palette.grey[800],
              '&:hover': {
                backgroundColor: theme.palette.grey[100],
                borderColor: theme.palette.grey[700],
              },
            }),
        },
        base: {
          borderWidth: 1,
          '&:hover': {
            borderWidth: 1,
          },
        },
      };
      return { ...styled.base, ...styled.inheritColor, ...styled.colors };
    },
    text: ({ ownerState, theme }) => {
      const styled = {
        colors: styleColors(ownerState, (color) => ({
          color: theme.palette[color].main,
          '&:hover': {
            backgroundColor: alpha(theme.palette[color].lighter, 0.5),
          },
          [`&.${buttonClasses.disabled}`]: {
            color: theme.palette.grey[500],
          },
        })),
        inheritColor: {
          ...(ownerState.color === 'inherit' &&
            !ownerState.disabled && {
              color: theme.palette.grey[800],
              '&:hover': {
                backgroundColor: theme.palette.grey[100],
              },
            }),
        },
      };
      return { ...styled.inheritColor, ...styled.colors };
    },
    sizeSmall: ({ ownerState }) => ({
      height: 30,
      fontSize: '0.8125rem',
      ...(ownerState.variant === 'text'
        ? { paddingLeft: '8px', paddingRight: '8px' }
        : { paddingLeft: '12px', paddingRight: '12px' }),
    }),
    sizeMedium: ({ ownerState }) => ({
      height: 36,
      fontSize: '0.875rem',
      ...(ownerState.variant === 'text'
        ? { paddingLeft: '10px', paddingRight: '10px' }
        : { paddingLeft: '16px', paddingRight: '16px' }),
    }),
    sizeLarge: ({ ownerState }) => ({
      height: 48,
      fontSize: '1rem',
      ...(ownerState.variant === 'text'
        ? { paddingLeft: '12px', paddingRight: '12px' }
        : { paddingLeft: '20px', paddingRight: '20px' }),
    }),
  },
};

export const button = { MuiButtonBase, MuiButton };
