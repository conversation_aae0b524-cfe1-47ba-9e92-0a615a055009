import type { Theme, Components } from '@mui/material/styles';

const MuiStack: Components<Theme>['MuiStack'] = {
  defaultProps: {
    useFlexGap: true,
    spacing: 2,
    direction: 'column',
  },
  styleOverrides: {
    root: ({ theme, ownerState }) => ({
      ...(ownerState.direction === 'column' && {
        gap: theme.spacing(2),
      }),
      ...(ownerState.direction === 'row' && {
        gap: theme.spacing(1),
        alignItems: 'center',
      }),
      [theme.breakpoints.down('sm')]: {
        ...(ownerState.direction === 'row' && {
          flexDirection: 'column',
          gap: theme.spacing(2),
        }),
      },
    }),
  },
};

export const stack = { MuiStack };
