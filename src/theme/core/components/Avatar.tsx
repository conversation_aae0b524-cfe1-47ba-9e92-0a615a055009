import { avatarGroupClasses } from '@mui/material/AvatarGroup';
import { alpha, type Theme, type Components } from '@mui/material/styles';

declare module '@mui/material/AvatarGroup' {
  interface AvatarGroupPropsVariantOverrides {
    compact: true;
  }
}

const MuiAvatar: Components<Theme>['MuiAvatar'] = {
  styleOverrides: {
    rounded: ({ theme }) => ({
      borderRadius: theme.shape.borderRadius * 1.5,
    }),

    colorDefault: ({ theme }) => ({
      color: theme.palette.primary.main,
      backgroundColor: alpha(theme.palette.primary.lighter, 0.7),
    }),
  },
};

const MuiAvatarGroup: Components<Theme>['MuiAvatarGroup'] = {
  defaultProps: {
    max: 4,
  },

  styleOverrides: {
    root: ({ ownerState }) => ({
      justifyContent: 'flex-end',
      ...(ownerState.variant === 'compact' && {
        width: 40,
        height: 40,
        position: 'relative',
        [`& .${avatarGroupClasses.avatar}`]: {
          margin: 0,
          width: 28,
          height: 28,
          position: 'absolute',
          '&:first-of-type': { left: 0, bottom: 0, zIndex: 9 },
          '&:last-of-type': { top: 0, right: 0 },
        },
      }),
    }),

    avatar: ({ theme }) => ({
      fontSize: 16,
      fontWeight: theme.typography.fontWeightSemiBold,
      '&:first-of-type': {
        fontSize: 12,
        color: theme.palette.primary.dark,
        backgroundColor: theme.palette.primary.lighter,
      },
    }),
  },
};

export const Avatar = { MuiAvatar, MuiAvatarGroup };
