import type { Theme, Components } from '@mui/material/styles';

import { alpha } from '@mui/material/styles';
import { inputBaseClasses } from '@mui/material/InputBase';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import { formHelperTextClasses } from '@mui/material/FormHelperText';

const MuiInputBase: Components<Theme>['MuiInputBase'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      fontFamily: theme.typography.fontFamily,
      fontSize: theme.typography.pxToRem(14),
      fontWeight: theme.typography.fontWeightRegular,
      lineHeight: theme.typography.pxToRem(21),
      borderRadius: theme.shape.borderRadius,
      transition: theme.transitions.create(['background-color', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      display: 'flex',
      alignItems: 'center',
      backgroundColor: theme.palette.background.paper,
      color: theme.palette.text.primary,
      [`&.${inputBaseClasses.disabled}`]: {
        backgroundColor: theme.palette.action.disabledBackground,
        color: theme.palette.text.disabled,
      },
    }),
    input: ({ theme }) => ({
      padding: theme.spacing(1.5, 1.75),
      '&::placeholder': {
        color: theme.palette.text.disabled,
        opacity: 1,
      },
    }),
  },
};

const MuiOutlinedInput: Components<Theme>['MuiOutlinedInput'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      borderRadius: theme.shape.borderRadius,
      backgroundColor: theme.palette.background.paper,
      '&:hover': {
        backgroundColor: alpha(theme.palette.action.hover, 0.1),
      },
      [`&.${outlinedInputClasses.disabled}`]: {
        backgroundColor: alpha(theme.palette.action.disabledBackground, 0.3),
      },
      [`&.${outlinedInputClasses.error}`]: {
        backgroundColor: alpha(theme.palette.error.light, 0.1),
      },
    }),
  },
};

const MuiFormHelperText: Components<Theme>['MuiFormHelperText'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      marginTop: theme.spacing(0.5),
      fontSize: theme.typography.pxToRem(12),
      color: theme.palette.text.secondary,
      [`&.${formHelperTextClasses.error}`]: {
        color: theme.palette.error.main,
      },
      [`&.${formHelperTextClasses.disabled}`]: {
        color: theme.palette.text.disabled,
      },
    }),
  },
};

export const textfield = {
  MuiInputBase,
  MuiOutlinedInput,
  MuiFormHelperText,
};
