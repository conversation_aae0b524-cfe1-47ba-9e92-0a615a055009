import type { Theme, Components } from '@mui/material/styles';

import { alpha } from '@mui/material/styles';
import { switchClasses } from '@mui/material/Switch';

const MuiSwitch: Components<Theme>['MuiSwitch'] = {
  styleOverrides: {
    root: { alignItems: 'center' },
    switchBase: ({ ownerState, theme }) => ({
      top: 'unset',
      transform: `translateX(${theme.spacing(0.75)})`,
      [`&.${switchClasses.checked}`]: {
        transform: `translateX(calc(100% - ${theme.spacing(2.25)}))`,
        [`& .${switchClasses.thumb}`]: {
          ...(ownerState.color === 'default' && {
            color: theme.palette.grey[800],
          }),
        },
        [`&+.${switchClasses.track}`]: {
          opacity: 1,
          ...(ownerState.color === 'default' && {
            backgroundColor: theme.palette.primary.main,
          }),
        },
      },
      [`&.${switchClasses.disabled}`]: {
        [`& .${switchClasses.thumb}`]: { opacity: 1 },
        [`&+.${switchClasses.track}`]: { opacity: 0.48 },
      },
    }),
    track: ({ theme }) => ({
      opacity: 1,
      borderRadius: theme.shape.borderRadius,
      backgroundColor: alpha(theme.palette.grey[400], 0.48),
      '&:hover': {
        backgroundColor: alpha(theme.palette.grey[500], 0.48),
      },
    }),
    thumb: ({ theme }) => ({
      color: theme.palette.common.white,
      '&.Mui-disabled': { color: theme.palette.grey[500] },
    }),
    sizeMedium: ({ theme }) => ({
      [`& .${switchClasses.track}`]: { height: theme.spacing(2.5), width: theme.spacing(4.5) },
      [`& .${switchClasses.thumb}`]: { width: theme.spacing(1.75), height: theme.spacing(1.75) },
    }),
    sizeSmall: ({ theme }) => ({
      [`& .${switchClasses.track}`]: { height: theme.spacing(2), width: theme.spacing(3.5) },
      [`& .${switchClasses.thumb}`]: { width: theme.spacing(1.25), height: theme.spacing(1.25) },
    }),
  },
};

export const switches = { MuiSwitch };
