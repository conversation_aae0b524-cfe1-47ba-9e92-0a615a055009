import type { ChipProps } from '@mui/material/Chip';
import type { SvgIconProps } from '@mui/material/SvgIcon';
import type { Theme, CSSObject, Components, ComponentsVariants } from '@mui/material/styles';

import SvgIcon from '@mui/material/SvgIcon';
import { alpha } from '@mui/material/styles';
import { chipClasses } from '@mui/material/Chip';

export const ChipDeleteIcon = (props: SvgIconProps) => (
  <SvgIcon {...props}>
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10M8.97 8.97a.75.75 0 0 1 1.06 0L12 10.94l1.97-1.97a.75.75 0 0 1 1.06 1.06L13.06 12l1.97 1.97a.75.75 0 0 1-1.06 1.06L12 13.06l-1.97 1.97a.75.75 0 0 1-1.06-1.06L10.94 12l-1.97-1.97a.75.75 0 0 1 0-1.06"
      clipRule="evenodd"
    />
  </SvgIcon>
);

declare module '@mui/material/Chip' {
  interface ChipPropsVariantOverrides {
    soft: true;
  }
}

const COLORS = ['primary', 'secondary', 'info', 'success', 'warning', 'error'] as const;

type ColorType = (typeof COLORS)[number];

function styleColors(ownerState: ChipProps, styles: (val: ColorType) => CSSObject) {
  const outputStyle = COLORS.reduce((acc, color) => {
    if (!ownerState.disabled && ownerState.color === color) {
      acc = styles(color);
    }
    return acc;
  }, {});

  return outputStyle;
}

const softVariant: Record<string, ComponentsVariants<Theme>['MuiChip']> = {
  colors: COLORS.map((color) => ({
    props: ({ ownerState }) =>
      !ownerState.disabled && ownerState.variant === 'soft' && ownerState.color === color,
    style: ({ theme }) => ({
      color: theme.palette[color].dark,
      backgroundColor: alpha(theme.palette[color].lighter, 0.5),
      '&:hover': { backgroundColor: alpha(theme.palette[color].main, 0.16) },
      [`& .${chipClasses.avatar}`]: {
        color: theme.palette[color].contrastText,
        backgroundColor: theme.palette[color].dark,
      },
    }),
  })),
  inheritColor: [
    {
      props: ({ ownerState }) => ownerState.variant === 'soft' && ownerState.color === 'default',
      style: ({ theme }) => ({
        color: theme.palette.grey[800],
        backgroundColor: theme.palette.grey[200],
        '&:hover': { backgroundColor: theme.palette.grey[200] },
        [`& .${chipClasses.avatar}`]: {
          color: theme.palette.grey[200],
          backgroundColor: theme.palette.grey[700],
        },
      }),
    },
  ],
};

const MuiChip: Components<Theme>['MuiChip'] = {
  defaultProps: { color: 'primary', deleteIcon: <ChipDeleteIcon /> },
  variants: [...[...softVariant.inheritColor!, ...softVariant.colors!]],
  styleOverrides: {
    root: ({ ownerState, theme }) => {
      const styled = {
        colors: styleColors(ownerState, (color) => ({
          [`& .${chipClasses.avatar}`]: {
            color: theme.palette[color].contrastText,
            backgroundColor: theme.palette[color].dark,
          },
        })),
        disabled: {
          [`&.${chipClasses.disabled}`]: {
            opacity: 1,
            color: theme.palette.grey[500],
            backgroundColor: theme.palette.grey[200],
            borderColor: theme.palette.grey[300],
            [`& .${chipClasses.avatar}`]: {
              color: theme.palette.grey[500],
              backgroundColor: theme.palette.grey[300],
            },
          },
        },
      };
      return {
        fontWeight: 600,
        transition: theme.transitions.create(['background-color', 'border-color'], {
          duration: theme.transitions.duration.short,
        }),
        ...styled.colors,
        ...styled.disabled,
      };
    },
    label: ({ theme }) => ({
      fontWeight: theme.typography.fontWeightMedium,
    }),
    icon: {
      color: 'currentColor',
    },
    deleteIcon: {
      opacity: 0.7,
      color: 'currentColor',
      '&:hover': {
        opacity: 1,
        color: 'currentColor',
      },
    },
    sizeMedium: {
      height: 32,
      fontSize: '0.875rem',
      borderRadius: 20,
    },
    sizeSmall: {
      height: 24,
      fontSize: '0.8125rem',
      borderRadius: 20,
    },
    filled: ({ ownerState, theme }) => {
      const styled = {
        colors: styleColors(ownerState, (color) => ({
          backgroundColor: theme.palette[color].main,
          color: theme.palette[color].contrastText,
          '&:hover': {
            backgroundColor: theme.palette[color].dark,
          },
        })),
        defaultColor: {
          ...(ownerState.color === 'default' &&
            !ownerState.disabled && {
              color: theme.palette.grey[100],
              backgroundColor: theme.palette.grey[800],
              '&:hover': {
                backgroundColor: theme.palette.grey[900],
              },
              [`& .${chipClasses.avatar}`]: {
                color: theme.palette.grey[800],
                backgroundColor: theme.palette.grey[100],
              },
            }),
        },
      };
      return { ...styled.colors, ...styled.defaultColor };
    },
    outlined: ({ ownerState, theme }) => {
      const styled = {
        colors: styleColors(ownerState, (color) => ({
          borderColor: theme.palette[color].main,
          color: theme.palette[color].main,
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: alpha(theme.palette[color].lighter, 0.5),
            borderColor: theme.palette[color].dark,
          },
        })),
        defaultColor: {
          ...(ownerState.color === 'default' &&
            !ownerState.disabled && {
              borderColor: theme.palette.grey[500],
              color: theme.palette.grey[800],
              backgroundColor: 'transparent',
              '&:hover': {
                backgroundColor: theme.palette.grey[100],
                borderColor: theme.palette.grey[700],
              },
              [`& .${chipClasses.avatar}`]: {
                color: theme.palette.grey[100],
                backgroundColor: theme.palette.grey[700],
              },
            }),
        },
      };
      return { ...styled.colors, ...styled.defaultColor };
    },
  },
};

export const chip = { MuiChip };
