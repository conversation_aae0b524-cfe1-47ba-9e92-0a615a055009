import { alpha, type Theme, type Components } from '@mui/material/styles';

const MuiPaper: Components<Theme>['MuiPaper'] = {
  defaultProps: { elevation: 0 },
  styleOverrides: {
    root: ({ theme }) => ({
      backgroundImage: 'none',
      backgroundColor: theme.palette.common.white,
      borderRadius: theme.shape.borderRadius,
    }),
    elevation: ({ theme }) => ({
      boxShadow: theme.shadows[1],
    }),
    outlined: ({ theme }) => ({
      border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`,
    }),
  },
};

export const paper = { MuiPaper };
