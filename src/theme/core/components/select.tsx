import type { SvgIconProps } from '@mui/material/SvgIcon';
import type { Theme, Components } from '@mui/material/styles';

import SvgIcon from '@mui/material/SvgIcon';
import { alpha } from '@mui/material/styles';
import { inputLabelClasses } from '@mui/material/InputLabel';

export const ArrowDownIcon = (props: SvgIconProps) => (
  <SvgIcon
    {...props}
    sx={{
      color: (theme) => theme.palette.grey[500],
    }}
  >
    <path
      fill="currentColor"
      d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15a1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16"
    />
  </SvgIcon>
);

const MuiInputLabel: Components<Theme>['MuiInputLabel'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      ...theme.typography.body2,
      position: 'absolute',
      transform: 'translate(14px, 8px) scale(1)',
      color: theme.palette.grey[600],
      transition: theme.transitions.create(['transform', 'font-size', 'color'], {
        duration: 200,
      }),

      [`&.${inputLabelClasses.shrink}`]: {
        transform: 'translate(14px, -9px) scale(0.75)',
        transformOrigin: 'top left',
        fontWeight: theme.typography.fontWeightMedium,
        color: theme.palette.grey[800],
        backgroundColor: theme.palette.background.paper,
        padding: '0 4px',
      },

      [`&.${inputLabelClasses.focused}`]: {
        color: theme.palette.primary.main,
      },

      [`&.${inputLabelClasses.error}`]: {
        color: theme.palette.error.main,
      },

      [`&.${inputLabelClasses.disabled}`]: {
        color: theme.palette.grey[500],
      },

      '&[data-shrink="true"]::after': {
        content: '"*"',
        color: theme.palette.error.main,
        marginLeft: '4px',
      },
    }),
  },
};

const MuiSelect: Components<Theme>['MuiSelect'] = {
  defaultProps: {
    IconComponent: ArrowDownIcon,
  },
  styleOverrides: {
    root: ({ theme }) => ({
      fontSize: theme.typography.body2.fontSize,
      fontWeight: theme.typography.fontWeightRegular,
      color: theme.palette.grey[800],
      height: 40,
      minHeight: 40,
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.grey[300],
        borderRadius: theme.shape.borderRadius,
        transition: theme.transitions.create(['border-color', 'box-shadow']),
      },
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.grey[400],
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
      },
      '&.Mui-error .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.error.main,
      },
      '&.Mui-disabled .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.grey[200],
      },
    }),
    select: ({ theme }) => ({
      padding: theme.spacing(1, 1.5),
      paddingRight: theme.spacing(4),
      '&.Mui-disabled': {
        backgroundColor: theme.palette.grey[100],
      },
    }),
    icon: ({ theme }) => ({
      right: theme.spacing(1.25),
      width: theme.spacing(2.25),
      height: theme.spacing(2.25),
      top: 'calc(50% - 9px)',
      color: theme.palette.grey[500],
      '&.Mui-disabled': {
        color: theme.palette.grey[400],
      },
    }),
  },
};

const MuiFormControl: Components<Theme>['MuiFormControl'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      '& .MuiFormHelperText-root': {
        marginLeft: 0,
        marginTop: theme.spacing(0.5),
      },
    }),
  },
};

export const select = { MuiSelect, MuiInputLabel, MuiFormControl };
