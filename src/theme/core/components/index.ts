import { menu } from './Menu';
import { list } from './List';
import { card } from './card';
import { chip } from './chip';
import { tabs } from './tabs';
import { form } from './form';
import { paper } from './paper';
import { badge } from './badge';
import { table } from './table';
import { stack } from './stack';
import { Alert } from './Alert';
import { button } from './button';
import { dialog } from './dialog';
import { select } from './select';
import { Avatar } from './Avatar';
import { switches } from './switch';
import { Stepper } from './Stepper';
import { checkbox } from './checkbox';
import { textfield } from './textfield';
import { pagination } from './pagination';
import { typography } from './typography';
import { buttonGroup } from './ButtonGroup';
import { autocomplete } from './autocomplete';

export const components = {
  ...autocomplete,
  ...badge,
  ...button,
  ...Stepper,
  ...buttonGroup,
  ...card,
  ...checkbox,
  ...chip,
  ...dialog,
  ...form,
  ...pagination,
  ...table,
  ...tabs,
  ...textfield,
  ...typography,
  ...select,
  ...stack,
  ...switches,
  ...Avatar,
  ...paper,
  ...Alert,
  ...menu,
  ...list,
};
