import { tableCellClasses } from '@mui/material/TableCell';
import { alpha, type Theme, type Components } from '@mui/material/styles';

const MuiTableContainer: Components<Theme>['MuiTableContainer'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      backgroundColor: theme.palette.common.white,
      borderRadius: theme.shape.borderRadius,
      scrollbarWidth: 'thin',
      scrollbarColor: `${theme.palette.grey[500]} ${alpha(theme.palette.grey[500], 0.08)}`,
    }),
  },
};

const MuiTable: Components<Theme>['MuiTable'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      borderCollapse: 'separate',
      borderSpacing: 0,
    }),
  },
};

const MuiTableRow: Components<Theme>['MuiTableRow'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      backgroundColor: theme.palette.common.white,
      '&:hover': {
        backgroundColor: theme.palette.grey[50],
      },
      '&:last-of-type': {
        [`& .${tableCellClasses.root}`]: {
          borderBottom: 'none',
        },
      },
    }),
  },
};

const MuiTableCell: Components<Theme>['MuiTableCell'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      fontSize: theme.typography.body2.fontSize,
      fontWeight: theme.typography.fontWeightRegular,
      color: theme.palette.grey[900],
      padding: theme.spacing(2, 4),
      borderBottom: `1px solid ${theme.palette.grey[200]}`,
    }),
    head: ({ theme }) => ({
      fontSize: theme.typography.caption.fontSize,
      fontWeight: theme.typography.fontWeightMedium,
      color: theme.palette.grey[600],
      textTransform: 'uppercase',
      backgroundColor: theme.palette.grey[200],
      padding: theme.spacing(2, 4),
      borderBottom: 'none',
    }),
  },
};

const MuiTablePagination: Components<Theme>['MuiTablePagination'] = {
  defaultProps: {
    backIconButtonProps: { size: 'small' },
    nextIconButtonProps: { size: 'small' },
    slotProps: { select: { name: 'table-pagination-select' } },
  },
  styleOverrides: {
    root: {
      width: '100%',
    },
    toolbar: ({ theme }) => ({
      height: theme.spacing(8),
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'center',
    }),
    actions: ({ theme }) => ({
      marginRight: theme.spacing(1),
      '& .MuiIconButton-root': {
        border: `1px solid ${theme.palette.grey[300]}`,
        borderRadius: theme.shape.borderRadius,
        padding: theme.spacing(1, 1.5),
        color: theme.palette.grey[800],
        '&:hover': {
          backgroundColor: theme.palette.grey[100],
        },
        '&.Mui-disabled': {
          borderColor: theme.palette.grey[200],
          color: theme.palette.grey[500],
        },
      },
    }),
    displayedRows: ({ theme }) => ({
      fontSize: theme.typography.body2.fontSize,
      color: theme.palette.grey[600],
    }),
  },
};

export const table = {
  MuiTable,
  MuiTableRow,
  MuiTableCell,
  MuiTableContainer,
  MuiTablePagination,
};
