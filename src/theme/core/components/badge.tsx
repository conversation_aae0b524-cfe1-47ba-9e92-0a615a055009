import type { Theme, Components } from '@mui/material/styles';

import { badgeClasses } from '@mui/material/Badge';

declare module '@mui/material/Badge' {
  interface BadgePropsVariantOverrides {
    alway: true;
    busy: true;
    online: true;
    offline: true;
    invisible: true;
  }
}

const baseStyles = (theme: Theme) => ({
  width: 10,
  height: 10,
  minWidth: 'auto',
  padding: 0,
  zIndex: 9,
  '&::before, &::after': {
    content: "''",
    borderRadius: 1,
    position: 'absolute',
    backgroundColor: theme.palette.common.white,
  },
});

const statusStyles = {
  online: (theme: Theme) => ({
    backgroundColor: theme.palette.success.main,
  }),
  alway: (theme: Theme) => ({
    backgroundColor: theme.palette.warning.main,
    '&::before': {
      width: 2,
      height: 4,
      transform: 'translateX(1px) translateY(-1px)',
    },
    '&::after': {
      width: 2,
      height: 4,
      transform: 'translateY(1px) rotate(125deg)',
    },
  }),
  busy: (theme: Theme) => ({
    backgroundColor: theme.palette.error.main,
    '&::before': {
      width: 6,
      height: 2,
    },
  }),
  offline: (theme: Theme) => ({
    backgroundColor: theme.palette.text.disabled,
    '&::before': {
      width: 6,
      height: 6,
      borderRadius: '50%',
    },
  }),
  invisible: {
    display: 'none',
  },
};

const MuiBadge: Components<Theme>['MuiBadge'] = {
  defaultProps: {
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'right',
    },
  },

  variants: [
    {
      props: ({ ownerState }) => ownerState.variant === 'online',
      style: ({ theme }) => ({
        [`& .${badgeClasses.badge}`]: {
          ...baseStyles(theme),
          ...statusStyles.online(theme),
        },
      }),
    },
    {
      props: ({ ownerState }) => ownerState.variant === 'alway',
      style: ({ theme }) => ({
        [`& .${badgeClasses.badge}`]: {
          ...baseStyles(theme),
          ...statusStyles.alway(theme),
        },
      }),
    },
    {
      props: ({ ownerState }) => ownerState.variant === 'busy',
      style: ({ theme }) => ({
        [`& .${badgeClasses.badge}`]: {
          ...baseStyles(theme),
          ...statusStyles.busy(theme),
        },
      }),
    },
    {
      props: ({ ownerState }) => ownerState.variant === 'offline',
      style: ({ theme }) => ({
        [`& .${badgeClasses.badge}`]: {
          ...baseStyles(theme),
          ...statusStyles.offline(theme),
        },
      }),
    },
    {
      props: ({ ownerState }) => ownerState.variant === 'invisible',
      style: {
        [`& .${badgeClasses.badge}`]: statusStyles.invisible,
      },
    },
  ],

  styleOverrides: {
    root: {
      position: 'relative',
    },
    badge: ({ theme }) => ({
      position: 'relative',
      transform: 'unset',
      borderRadius: '50%',
      transition: theme.transitions.create(['background-color'], {
        duration: theme.transitions.duration.shortest,
      }),
    }),
    dot: {
      borderRadius: '50%',
    },
    standard: ({ theme }) => ({
      [`&.${badgeClasses.anchorOriginTopRight}`]: {
        top: 4,
        right: 4,
      },
      [`&.${badgeClasses.anchorOriginBottomRight}`]: {
        bottom: 4,
        right: 4,
      },
      [`&.${badgeClasses.anchorOriginTopLeft}`]: {
        top: 4,
        left: 4,
      },
      [`&.${badgeClasses.anchorOriginBottomLeft}`]: {
        bottom: 4,
        left: 4,
      },
    }),
  },
};

export const badge = { MuiBadge };
