import type { Theme, Components } from '@mui/material/styles';

import { inputLabelClasses } from '@mui/material/InputLabel';

const MuiFormLabel: Components<Theme>['MuiFormLabel'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      ...theme.typography.body2,
      color: theme.palette.grey[600],
      fontWeight: theme.typography.fontWeightMedium,
      [`&.${inputLabelClasses.shrink}`]: {
        ...theme.typography.body2,
        fontWeight: theme.typography.fontWeightSemiBold,
        color: theme.palette.grey[800],
        transform: 'translate(12px, 6px) scale(0.75)',
        [`&.${inputLabelClasses.focused}`]: { color: theme.palette.grey[800] },
        [`&.${inputLabelClasses.error}`]: { color: theme.palette.error.main },
        [`&.${inputLabelClasses.disabled}`]: { color: theme.palette.grey[500] },
      },
    }),
  },
};

const MuiFormHelperText: Components<Theme>['MuiFormHelperText'] = {
  defaultProps: { component: 'div' },
  styleOverrides: {
    root: ({ theme }) => ({
      ...theme.typography.caption,
      color: theme.palette.grey[600],
      marginTop: theme.spacing(1),
      '&.Mui-error': { color: theme.palette.error.main },
      '&.Mui-disabled': { color: theme.palette.grey[500] },
    }),
  },
};

const MuiFormControlLabel: Components<Theme>['MuiFormControlLabel'] = {
  styleOverrides: {
    label: ({ theme }) => ({
      ...theme.typography.body2,
      color: theme.palette.grey[800],
      '&.Mui-disabled': { color: theme.palette.grey[500] },
    }),
  },
};

export const form = { MuiFormLabel, MuiFormHelperText, MuiFormControlLabel };
