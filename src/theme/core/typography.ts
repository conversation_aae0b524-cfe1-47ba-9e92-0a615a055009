import type { Theme } from '@mui/material';
import type { TypographyOptions } from '@mui/material/styles/createTypography';

import { setFont, pxToRem, responsiveFontSizes } from '../styles/utils';

declare module '@mui/material/styles' {
  interface TypographyVariants {
    fontWeightSemiBold: React.CSSProperties['fontWeight'];
  }
  interface TypographyVariantsOptions {
    fontWeightSemiBold?: React.CSSProperties['fontWeight'];
  }
  interface ThemeVars {
    typography: Theme['typography'];
  }
}

export const defaultFont = 'Inter';

export const primaryFont = setFont(defaultFont);

export const typography: TypographyOptions = {
  fontFamily: primaryFont,
  fontWeightLight: '300',
  fontWeightRegular: '400',
  fontWeightMedium: '500',
  fontWeightSemiBold: '600',
  fontWeightBold: '700',
  h1: {
    fontWeight: 700,
    lineHeight: 90 / 72,
    fontSize: pxToRem(60),
    letterSpacing: '-2%',
    ...responsiveFontSizes({ sm: 64, md: 68, lg: 72 }),
  },
  h2: {
    fontWeight: 700,
    lineHeight: 72 / 60,
    fontSize: pxToRem(48),
    letterSpacing: '-2%',
    ...responsiveFontSizes({ sm: 52, md: 56, lg: 60 }),
  },
  h3: {
    fontWeight: 600,
    lineHeight: 60 / 48,
    fontSize: pxToRem(36),
    letterSpacing: '-2%',
    ...responsiveFontSizes({ sm: 40, md: 44, lg: 48 }),
  },
  h4: {
    fontWeight: 600,
    lineHeight: 44 / 36,
    fontSize: pxToRem(24),
    letterSpacing: '-2%',
    ...responsiveFontSizes({ sm: 28, md: 32, lg: 36 }),
  },
  h5: {
    fontWeight: 600,
    lineHeight: 38 / 30,
    fontSize: pxToRem(18),
    ...responsiveFontSizes({ sm: 22, md: 26, lg: 30 }),
  },
  h6: {
    fontWeight: 500,
    lineHeight: 32 / 24,
    fontSize: pxToRem(12),
    ...responsiveFontSizes({ sm: 16, md: 20, lg: 24 }),
  },
  subtitle1: {
    fontWeight: 500,
    lineHeight: 30 / 20,
    fontSize: pxToRem(8),
    ...responsiveFontSizes({ sm: 12, md: 16, lg: 20 }),
  },
  subtitle2: {
    fontWeight: 500,
    lineHeight: 28 / 18,
    fontSize: pxToRem(6),
    ...responsiveFontSizes({ sm: 10, md: 14, lg: 18 }),
  },
  body1: {
    fontWeight: 400,
    lineHeight: 24 / 16,
    fontSize: pxToRem(4),
    ...responsiveFontSizes({ sm: 8, md: 12, lg: 16 }),
  },
  body2: {
    fontWeight: 400,
    lineHeight: 22 / 14,
    fontSize: pxToRem(2),
    ...responsiveFontSizes({ sm: 6, md: 10, lg: 14 }),
  },
  caption: {
    fontWeight: 400,
    lineHeight: 18 / 12,
    fontSize: pxToRem(1),
    ...responsiveFontSizes({ sm: 4, md: 8, lg: 12 }),
  },
  overline: {
    fontWeight: 600,
    lineHeight: 16 / 12,
    fontSize: pxToRem(12),
    textTransform: 'uppercase',
  },
  button: {
    fontWeight: 600,
    lineHeight: 24 / 14,
    fontSize: pxToRem(14),
    textTransform: 'unset',
  },
};
