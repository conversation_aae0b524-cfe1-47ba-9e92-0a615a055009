import { useState } from 'react';

import { CssB<PERSON><PERSON>, ThemeProvider as MUIThemeProvider } from '@mui/material';

import { darkTheme, lightTheme } from './theme';

type ThemeProviderProps = {
  children: React.ReactNode;
};

const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const [isDarkMode] = useState(false);
  return (
    <MUIThemeProvider theme={isDarkMode ? darkTheme : lightTheme}>
      <CssBaseline />
      {children}
    </MUIThemeProvider>
  );
};

export default ThemeProvider;
