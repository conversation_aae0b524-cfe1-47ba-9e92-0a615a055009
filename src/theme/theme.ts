import { createTheme } from '@mui/material/styles';

import { shadows } from './core/shadows';
import { typography } from './core/typography';
import { darkPalette, lightPalette } from './core/palette';
import { components as coreComponents } from './core/components';

export const lightTheme = createTheme({
  palette: lightPalette,
  typography,
  shadows: shadows('light'),
  components: coreComponents,
  shape: { borderRadius: 8 },
});

export const darkTheme = createTheme({
  palette: darkPalette,
  typography,
  shadows: shadows('dark'),
  components: coreComponents,
  shape: { borderRadius: 8 },
});
