import type { TypographyOptions } from '@mui/material/styles/createTypography';
import type {
  Theme as BaseTheme,
  ThemeOptions as BaseThemeOptions,
} from '@mui/material/styles/createTheme';

export interface CustomShadows {
  z1: string;
  z4: string;
  z8: string;
  z12: string;
  z16: string;
  z20: string;
  z24: string;
  primary: string;
  info: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  card: string;
  dialog: string;
  dropdown: string;
}

export interface ExtendedTheme extends BaseTheme {
  customShadows: CustomShadows;
}

export type Theme = ExtendedTheme;

export type ThemeUpdateOptions = Omit<BaseThemeOptions, 'typography'> & {
  typography?: TypographyOptions;
};

export type ThemeComponents = BaseThemeOptions['components'];

export type ThemeColorScheme = 'light' | 'dark';
export type ThemeDirection = 'ltr' | 'rtl';

export type ThemeLocaleComponents = { components: ThemeComponents };
