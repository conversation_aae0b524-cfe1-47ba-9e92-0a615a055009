import { useState } from 'react';

import {
  <PERSON>,
  Tab,
  Card,
  Grid,
  <PERSON>,
  Tabs,
  <PERSON><PERSON>,
  Badge,
  Table,
  Button,
  Dialog,
  Select,
  Checkbox,
  TableRow,
  MenuItem,
  TextField,
  TableBody,
  TableCell,
  TableHead,
  Typography,
  InputLabel,
  DialogTitle,
  FormControl,
  Autocomplete,
  DialogActions,
  DialogContent,
  TableContainer,
  FormHelperText,
  TablePagination,
  FormControlLabel,
} from '@mui/material';

const ThemeSetup = () => {
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const options = ['Option 1', 'Option 2', 'Option 3'];
  const tableData = [
    { id: 1, name: '<PERSON>', role: 'Developer' },
    { id: 2, name: '<PERSON>', role: 'Designer' },
    { id: 3, name: '<PERSON>', role: 'Manager' },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h3" gutterBottom>
        Core Components Demo
      </Typography>

      <Grid container spacing={3}>
        {/* Typography Section */}
        <Grid item xs={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Typography
            </Typography>
            <Stack spacing={1}>
              <Typography variant="h1">Heading 1</Typography>
              <Typography variant="h2">Heading 2</Typography>
              <Typography variant="body1">Body 1 Text</Typography>
              <Typography variant="body2">Body 2 Text</Typography>
            </Stack>
          </Card>
        </Grid>

        {/* Buttons Section */}
        <Grid item xs={12} md={6}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Buttons
            </Typography>
            <Stack spacing={2} direction="row" flexWrap="wrap" gap={2}>
              <Button variant="contained">Contained</Button>
              <Button variant="outlined">Outlined</Button>
              <Button variant="soft" color="primary">
                Soft
              </Button>
              <Button variant="text">Text</Button>
            </Stack>
          </Card>
        </Grid>

        {/* Badge Section */}
        <Grid item xs={12} md={6}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Badges
            </Typography>
            <Stack direction="row" spacing={3}>
              <Badge variant="online">
                <Box sx={{ width: 40, height: 40, bgcolor: 'grey.300', borderRadius: 1 }} />
              </Badge>
              <Badge variant="busy">
                <Box sx={{ width: 40, height: 40, bgcolor: 'grey.300', borderRadius: 1 }} />
              </Badge>
              <Badge variant="offline">
                <Box sx={{ width: 40, height: 40, bgcolor: 'grey.300', borderRadius: 1 }} />
              </Badge>
              <Badge variant="alway">
                <Box sx={{ width: 40, height: 40, bgcolor: 'grey.300', borderRadius: 1 }} />
              </Badge>
            </Stack>
          </Card>
        </Grid>

        {/* Form Elements */}
        <Grid item xs={12} md={6}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Form Elements
            </Typography>
            <Stack spacing={3}>
              <TextField label="Standard" />
              <TextField label="Outlined" variant="outlined" />
              <TextField label="Filled" variant="filled" />
              <TextField
                variant="outlined"
                placeholder="Enter text..."
                InputLabelProps={{ shrink: false }}
              />

              <Checkbox defaultChecked />
              <Autocomplete
                options={options}
                renderInput={(params) => <TextField {...params} label="Autocomplete" />}
              />

              <FormControl fullWidth>
                <InputLabel id="demo-simple-select-label">Department</InputLabel>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  label="Department"
                  defaultValue=""
                >
                  <MenuItem value="">None</MenuItem>
                  <MenuItem value="Customer Care">Customer Care</MenuItem>
                  <MenuItem value="Sales & Marketing">Sales & Marketing</MenuItem>
                  <MenuItem value="Operation">Operation</MenuItem>
                </Select>
              </FormControl>
            </Stack>
          </Card>
        </Grid>

        {/* Chips Section */}
        <Grid item xs={12} md={6}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Chips
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
              <Chip label="Default" />
              <Chip label="Primary" color="primary" />
              <Chip label="Soft" variant="soft" />
              <Chip label="Outlined" variant="outlined" />
              <Chip label="Deletable" onDelete={() => {}} />
            </Stack>

            <Typography variant="h6" gutterBottom>
              Other Examples
            </Typography>

            <Stack direction="row" spacing={2} flexWrap="wrap" gap={1}>
              <Chip label="Primary Filled" variant="filled" color="primary" />
              <Chip label="Secondary Outlined" variant="outlined" color="secondary" />
              <Chip label="Success Soft" variant="soft" color="success" />
              <Chip label="Primary Soft" variant="soft" color="primary" />
              <Chip label="Error Soft" variant="soft" color="error" />
              <Chip label="Warning Soft" variant="soft" color="warning" />
              <Chip label="Default" color="default" />
              <Chip label="Disabled" disabled />
              <Chip label="Small" size="small" />
            </Stack>
          </Card>
        </Grid>

        {/* Table Section */}
        <Grid item xs={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Table
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Role</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tableData.map((row) => (
                    <TableRow key={row.id}>
                      <TableCell>{row.id}</TableCell>
                      <TableCell>{row.name}</TableCell>
                      <TableCell>{row.role}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <TablePagination
                component="div"
                count={100}
                page={page}
                onPageChange={(e, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
              />
            </TableContainer>
          </Card>
        </Grid>

        {/* Tabs Section */}
        <Grid item xs={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Tabs
            </Typography>
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab label="Tab 1" />
              <Tab label="Tab 2" />
              <Tab label="Tab 3" />
            </Tabs>
            <Box sx={{ mt: 2 }}>
              {tabValue === 0 && <Typography>Tab 1 Content</Typography>}
              {tabValue === 1 && <Typography>Tab 2 Content</Typography>}
              {tabValue === 2 && <Typography>Tab 3 Content</Typography>}
            </Box>
          </Card>
        </Grid>

        {/* Dialog Demo Button */}
        <Grid item xs={12}>
          <Button variant="contained" onClick={() => setOpen(true)}>
            Open Dialog
          </Button>

          <Dialog open={open} onClose={() => setOpen(false)}>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogContent>
              <Typography>This is a sample dialog content.</Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpen(false)}>Cancel</Button>
              <Button variant="contained" onClick={() => setOpen(false)}>
                Confirm
              </Button>
            </DialogActions>
          </Dialog>
        </Grid>

        {/* form example  */}

        <Grid item xs={12}>
          <Card sx={{ p: 3 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <TextField placeholder="Name" defaultValue="Ayush Negi" />
            </FormControl>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <Select labelId="action-label" id="action" placeholder="Action" defaultValue="New">
                <MenuItem value="New">New</MenuItem>
                <MenuItem value="Assigned">Assigned</MenuItem>
                <MenuItem value="Closed">Closed</MenuItem>
              </Select>
              <FormHelperText>Choose an action</FormHelperText>
            </FormControl>

            <FormControlLabel control={<Checkbox />} label="Agree to terms" />
          </Card>
        </Grid>
        {/* Custom Stepper Formik Section */}
        <Grid item xs={12}>
          <StepperFormSection />
        </Grid>
      </Grid>
    </Box>
  );
};

function StepperFormSection() {
  return (
    <Card sx={{ p: 3, mt: 4 }}>
      <Typography variant="h6" gutterBottom>
        Custom Stepper Form (Formik)
      </Typography>
    </Card>
  );
}

export default ThemeSetup;
