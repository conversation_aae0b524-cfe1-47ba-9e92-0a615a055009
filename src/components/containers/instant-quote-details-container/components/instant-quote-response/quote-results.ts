import type { CSSProperties } from '@mui/material/styles/createMixins';
import type { EstimationLineItem } from 'src/shared/services/instant-quote/instant-quote.type';

import { formatCurrency } from 'src/utils/format-number';

import {
  ValueType,
  EstimationLineItemType,
} from 'src/shared/services/instant-quote/instant-quote.type';

export const getItemStyles = (lineItemType: EstimationLineItemType) => {
  switch (lineItemType) {
    case EstimationLineItemType.Primary:
      return { fontWeight: '600', fontSize: '0.875rem' } as CSSProperties;
    case EstimationLineItemType.Secondary:
      return { fontWeight: '400', fontSize: '0.875rem', color: '#4B5563' } as CSSProperties;
    case EstimationLineItemType.Tertiary:
      return { fontWeight: '400', fontSize: '0.875rem', color: '#6B7280' } as CSSProperties;
    case EstimationLineItemType.Information:
      return {
        fontWeight: '400',
        fontSize: '0.75rem',
        color: '#9CA3AF',
        fontStyle: 'italic',
      } as CSSProperties;
    default:
      return { fontSize: '0.875rem', color: '#4B5563' } as CSSProperties;
  }
};

export const getBackgroundStyles = (lineItemType: EstimationLineItemType) => {
  switch (lineItemType) {
    case EstimationLineItemType.Primary:
      return { backgroundColor: '#E0F2FE' } as CSSProperties;
    case EstimationLineItemType.Secondary:
      return { backgroundColor: '#F3F4F6' } as CSSProperties;
    case EstimationLineItemType.Tertiary:
      return { backgroundColor: '#F9FAFB' } as CSSProperties;
    case EstimationLineItemType.Information:
      return { backgroundColor: '#FFFFFF' } as CSSProperties;
    default:
      return { backgroundColor: '#FFFFFF' } as CSSProperties;
  }
};

export const formatValue = (value: number, estimationType: ValueType): string => {
  switch (estimationType) {
    case ValueType.Currency:
      return formatCurrency(value);
    case ValueType.Decimal:
      return value.toString();
    case ValueType.Integer:
      return value.toString();
    default:
      return value.toString();
  }
};

export const getFieldNameFromItem = (item: EstimationLineItem): string | null =>
  item.requestPropertyName || null;
