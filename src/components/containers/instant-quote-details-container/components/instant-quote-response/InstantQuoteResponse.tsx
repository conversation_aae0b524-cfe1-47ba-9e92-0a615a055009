import type {
  ValueType,
  Estimation,
  EstimationLineItem,
} from 'src/shared/services/instant-quote/instant-quote.type';

import React, { useState, useEffect } from 'react';

import { Box, Typography } from '@mui/material';

import { formatCurrency } from 'src/utils/format-number';

import { formatValue, getItemStyles, getBackgroundStyles } from './quote-results';
import {
  TableContent,
  InsuranceItem,
  TotalCostItem,
  ItemDescription,
  TableRowContent,
  TableCellContent,
  ItemRecommendation,
  TableContainerRoot,
} from './InstantQuoteResponse.style';

export interface InstantQuoteResponseProps {
  responseData: Estimation;
}

const InstantQuoteResponse: React.FC<InstantQuoteResponseProps> = (props) => {
  const { responseData } = props;

  const [lineItems, setLineItems] = useState<EstimationLineItem[]>(responseData?.lineItems);

  useEffect(() => {
    setLineItems(responseData?.lineItems);
  }, [responseData]);

  return (
    <Box role="presentation">
      {responseData && (
        <TableContainerRoot>
          <TableContent>
            {lineItems?.map((item, index) => (
              <TableRowContent key={index} style={getBackgroundStyles(item.lineItemType)}>
                <TableCellContent strong style={getItemStyles(item.lineItemType)}>
                  {item.name}
                  {item.description && <ItemDescription>({item.description})</ItemDescription>}
                  {item.isRecommendedValue && (
                    <ItemRecommendation>(Recommended)</ItemRecommendation>
                  )}
                </TableCellContent>
                <TableCellContent align="right" style={getItemStyles(item.lineItemType)}>
                  {item.showTextValue && item.textValue
                    ? item.textValue
                    : formatValue(item.value, item.estimationType as unknown as ValueType)}
                </TableCellContent>
              </TableRowContent>
            ))}

            {responseData.insurance !== undefined && (
              <InsuranceItem>
                <Typography fontWeight={500} fontSize="1.125rem">
                  Insurance
                </Typography>
                <Typography fontWeight={600} fontSize="1.125rem">
                  {formatCurrency(responseData.insurance)}
                </Typography>
              </InsuranceItem>
            )}

            {/* Total Cost summary using MUI components */}
            <TotalCostItem>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Total Cost
                </Typography>
              </Box>
              <Typography variant="h5" fontWeight="bold">
                {formatCurrency(responseData.totalCost)}
              </Typography>
            </TotalCostItem>
          </TableContent>
        </TableContainerRoot>
      )}
    </Box>
  );
};

export default InstantQuoteResponse;
