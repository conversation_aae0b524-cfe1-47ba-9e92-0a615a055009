import { Box, Typography } from '@mui/material';
import { alpha, styled } from '@mui/material/styles';

export const InstantEstimationSectionTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[900],
  fontWeight: theme.typography.fontWeightSemiBold,
  paddingBlock: theme.spacing(1),
}));

export const TableContainerRoot = styled(Box)(() => ({
  width: '100%',
}));

export const TableContent = styled(Box)(({ theme }) => ({
  minWidth: '100%',
  backgroundColor: theme.palette.background.paper,
  overflow: 'hidden',
  tableLayout: 'fixed',
}));

export const TableRowContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: '0.5rem 0.75rem',
  transition: 'all 0.2s',
  alignItems: 'center',
  backgroundColor: alpha(theme.palette.secondary.main, 0.1),
  borderRadius: theme.shape.borderRadius,

  marginBottom: '0.5rem',
}));

interface TableCellContentProps {
  align?: 'inherit' | 'left' | 'center' | 'right' | 'justify';
  strong?: boolean;
}

export const TableCellContent = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'align' && prop !== 'strong',
})<TableCellContentProps>(({ theme, align, strong }) => ({
  fontSize: '0.875rem',
  fontWeight: strong ? 600 : 400,
  textAlign: align || 'left',
  width: '50%',
  '&.header': {
    backgroundColor: theme.palette.grey[100],
    fontWeight: 600,
    color: theme.palette.text.secondary,
    borderRight: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderRight: 'none',
    },
  },
}));

export const ItemDescription = styled('span')(() => ({
  fontSize: '0.75rem',
  color: '#6B7280',
  marginLeft: '0.25rem',
  display: 'inline-block',
  fontWeight: 500,
}));

export const ItemRecommendation = styled('span')(() => ({
  fontSize: '0.75rem',
  color: '#718096',
  marginLeft: '0.25rem',
  display: 'inline-block',
  fontWeight: 500,
}));

export const InsuranceItem = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingTop: theme.spacing(1.5),
  paddingBottom: theme.spacing(1.5),
  paddingLeft: theme.spacing(3),
  paddingRight: theme.spacing(3),
  border: `1px solid ${theme.palette.grey[200]}`,
  backgroundColor: theme.palette.grey[50],
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(1),
  boxShadow: 'none',
  width: '100%',
}));

export const TotalCostItem = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingTop: theme.spacing(1),
  paddingBottom: theme.spacing(1),
  paddingLeft: theme.spacing(2),
  paddingRight: theme.spacing(2),
  border: `1px solid ${theme.palette.success.light}`,
  backgroundColor: theme.palette.success.lighter,
  color: theme.palette.success.dark,
  borderRadius: theme.shape.borderRadius,
  boxShadow: 'none',
  maxWidth: '48rem',
  marginLeft: 'auto',
  marginRight: 'auto',
  marginTop: theme.spacing(2),
}));
