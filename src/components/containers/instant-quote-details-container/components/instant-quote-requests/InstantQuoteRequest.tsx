import type { ServiceDDValue } from 'src/shared/types/ddValue';

import { ServiceName } from 'src/shared/enums/services.enum';

import NexStorage from './nex-storage/NexStorage';
import { RequestNoDataView } from './InstantQuoteRequest.style';
import NexAirAmbulance from './nex-air-ambulance/NexAirAmbulance';
import NexCourierParcel from './nex-courier-&-parcel/NexCourierParcel';
import NexTruckingPartload from './nex-trucking-&-partload/NexTruckingPartload';
import NexPackingMovingRequest from './nex-packing-&-moving/NexPackingMovingRequest';

interface InstantQuoteRequestProps {
  serviceType: ServiceDDValue;
  requestData: any;
}

const InstantQuoteRequest: React.FC<InstantQuoteRequestProps> = ({
  requestData,
  serviceType,
}: InstantQuoteRequestProps) => {
  const renderServiceComponent = () => {
    if (!serviceType || !serviceType.id) return null;
    const { name } = serviceType;

    switch (name) {
      case ServiceName.NEX_PACKING_MOVING:
        return (
          <NexPackingMovingRequest
            requestData={requestData}
            serviceId={serviceType?.id.toString()}
          />
        );
      case ServiceName.NEX_STORAGE:
        return <NexStorage requestData={requestData} serviceId={serviceType?.id.toString()} />;
      case ServiceName.NEX_TRUCKING_PART_LOAD:
        return <NexTruckingPartload requestData={requestData} />;
      case ServiceName.NEX_COURIER_PARCEL:
        return <NexCourierParcel requestData={requestData} />;
      case ServiceName.NEX_AMBULANCE:
        return <NexAirAmbulance requestData={requestData} />;
      case ServiceName.NEX_MARKET_PLACE:
        return <RequestNoDataView>Coming Soon</RequestNoDataView>;
      default:
        return <RequestNoDataView>No request data available</RequestNoDataView>;
    }
  };

  return <div>{renderServiceComponent()}</div>;
};

export default InstantQuoteRequest;
