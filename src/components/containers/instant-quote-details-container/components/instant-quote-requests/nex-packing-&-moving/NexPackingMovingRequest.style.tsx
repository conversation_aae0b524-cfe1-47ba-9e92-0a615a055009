import { styled } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';

export const RequestRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '0.5rem 0.75rem',
  backgroundColor: theme.palette.grey[50],
  borderRadius: theme.shape.borderRadius,
  marginBottom: '0.5rem',
  border: `1px solid ${theme.palette.grey[200]}`,
}));

export const RequestCell = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'strong',
})<{ strong?: boolean }>(({ strong, theme }) => ({
  fontSize: '0.95rem',
  fontWeight: strong ? 600 : 400,
  width: '50%',
  color: strong ? theme.palette.text.primary : theme.palette.text.secondary,
  textAlign: 'left',
}));
