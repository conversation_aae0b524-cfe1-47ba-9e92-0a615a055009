import type { Errors } from 'src/shared/types/errorResonse';

import { useState, useEffect, useCallback } from 'react';

import { Skeleton } from '@mui/material';

import { useToast } from 'src/providers/ToastProvider';
import {
  getVehicleOptions,
  getHouseTypeOptions,
  getPackageTypeOptions,
} from 'src/shared/services/instant-quote/instant-quote.service';

import { RequestRow, RequestCell } from './NexStorage.style';
import { RequestNoDataView } from '../InstantQuoteRequest.style';

interface NexStorageProps {
  requestData: Record<string, any>;
  serviceId: string;
}

const NexStorage: React.FC<NexStorageProps> = ({ requestData, serviceId }: NexStorageProps) => {
  const { showToast } = useToast();

  const [newRequestData, setNewRequestdata] = useState<Record<string, any> | null>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const [houseTypeData, vehicleData, packageTypeData] = await Promise.all([
        getHouseTypeOptions(serviceId),
        getVehicleOptions(),
        getPackageTypeOptions(),
      ]);

      const matchedHouseType = houseTypeData.find(
        (item) => item?.id === requestData?.houseHoldCapacityId
      );
      const matchedVehicleData = vehicleData.find((item) => item?.id === requestData?.vehicleId);
      const matchedPackageType = packageTypeData.find(
        (item) => item?.id === requestData?.packageTypeId
      );

      if (matchedHouseType?.name && matchedVehicleData?.name && matchedPackageType?.name) {
        const optimizedRequestData: Record<string, any> = {
          ...requestData,
          houseHoldCapacityId: matchedHouseType?.name,
          vehicleId: matchedVehicleData?.name,
          packageTypeId: matchedPackageType?.name,
        };

        setNewRequestdata(optimizedRequestData);
      } else {
        setNewRequestdata(null);
      }
    } catch (error) {
      const errorResponse = error as Errors;

      const errorMessage =
        typeof errorResponse.errors === 'string'
          ? errorResponse.errors
          : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [requestData, serviceId, showToast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  if (isLoading) {
    return (
      <div>
        {[...Array(8)].map((_, idx) => (
          <RequestRow key={idx}>
            <RequestCell strong>
              <Skeleton variant="text" width={80} height={24} />
            </RequestCell>
            <RequestCell>
              <Skeleton variant="text" width={120} height={24} />
            </RequestCell>
          </RequestRow>
        ))}
      </div>
    );
  }

  if (newRequestData === null)
    return <RequestNoDataView>No request data available</RequestNoDataView>;

  return (
    <div>
      {Object.entries(newRequestData || {}).map(([key, value]) => (
        <RequestRow key={key}>
          <RequestCell strong>{key}</RequestCell>
          <RequestCell>
            {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value?.toString()}
          </RequestCell>
        </RequestRow>
      ))}
    </div>
  );
};

export default NexStorage;
