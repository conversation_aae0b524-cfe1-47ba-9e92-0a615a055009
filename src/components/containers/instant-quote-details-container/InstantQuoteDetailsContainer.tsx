import type { EditMode } from 'src/shared/types/editMode';
import type { ddValue, ServiceDDValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type {
  InstantEstimation,
  EstimationResponse,
} from 'src/shared/services/instant-quote/instant-quote.type';

import { Formik } from 'formik';
import { useParams } from 'react-router-dom';
import { useState, useEffect, useCallback } from 'react';

import { appendZeroes } from 'src/utils/helper';
import { instantQuoteSchema } from 'src/utils/validation/instant-quote/InstantQuote.schema';
import { initialInstantEstimation } from 'src/utils/validation/instant-quote/InstantQuote.values';

import { getUsers } from 'src/shared/services/admin/users/users.service';
import { LogTableName, LogColumnName } from 'src/shared/enums/logs.enums';
import {
  updateInstantRequest,
  getInstantRequestById,
  getInstantRequestStatusOptions,
} from 'src/shared/services/instant-quote/instant-quote.service';

import Loader from 'src/components/common/loader/loader/Loader';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import LogDialog from '../../common/log-dialog';
import FormAction from './components/form-actions/FormAction';
import { CardWrapper } from './InstantQuoteDetailsContainer.style';
import InstantQuoteForm from '../instant-quote-container/components/instant-quote-form/InstantQuoteForm';

const InstantQuoteDetailsContainer = () => {
  const { instantQuoteId } = useParams<{ instantQuoteId: string }>();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [openLogsModal, setOpenLogsModal] = useState<boolean>(false);
  const [instantRequestStatusOptions, setInstantRequestStatusOptions] = useState<ddValue[]>([]);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);
  const [service, setService] = useState<ServiceDDValue | null>(null);
  const [adminOptions, setAdminOptions] = useState<AdminUser[]>([]);
  const [edit, setEdit] = useState<EditMode<InstantEstimation>>({} as EditMode<InstantEstimation>);

  const selectAdminOptions: ddValue[] = adminOptions.map((admin) => ({
    id: admin.id,
    name: admin.fullName || `${admin.firstName} ${admin.lastName}`,
    isDisabled: false,
  }));

  const _setIsLoading = (loading: boolean) => {
    setIsLoading(loading);
  };

  const fetchInitialData = useCallback(async () => {
    try {
      _setIsLoading(true);

      const [users, instantQuote, statusData] = await Promise.all([
        getUsers(),
        getInstantRequestById(Number(instantQuoteId)),
        getInstantRequestStatusOptions(),
      ]);
      if (Array.isArray(users)) {
        setAdminOptions(users);
      }

      setEdit((prev) => ({ ...prev, data: instantQuote }));
      setSelectedAdmin(instantQuote.assignedAdmin || null);
      setInstantRequestStatusOptions(statusData);
      setService(instantQuote?.service || null);

      if (instantQuote.assignedAdminId && Array.isArray(users)) {
        const admin = users.find((user) => user?.id === instantQuote.assignedAdminId);
        if (admin) {
          setSelectedAdmin(admin);
        }
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      _setIsLoading(false);
    }
  }, [instantQuoteId]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  const handleOpenLogsModal = () => {
    setOpenLogsModal(true);
  };

  const handleCloseLogsModal = () => {
    setOpenLogsModal(false);
  };

  const handleFormSubmit = async (values: InstantEstimation) => {
    setIsLoading(true);
    try {
      const postData: InstantEstimation = {
        ...values,
        adminActionRemark: values?.adminActionRemark,
        statusId: values.statusId,
        assignedAdminId: values.assignedAdminId ?? selectedAdmin?.id,
      };

      await updateInstantRequest(postData);
    } catch (error: any) {
      console.error(
        `Failed to update instant quote: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainContent pageTitle={`Instant Quote | ${appendZeroes(Number(instantQuoteId), 4)}`}>
      <Loader loading={isLoading} />
      <Formik<InstantEstimation>
        initialValues={initialInstantEstimation(edit?.data as InstantEstimation)}
        validationSchema={instantQuoteSchema}
        onSubmit={handleFormSubmit}
        enableReinitialize
      >
        {() => (
          <>
            <CardWrapper>
              <InstantQuoteForm
                readMode={isLoading}
                instantRequestStatusOptions={instantRequestStatusOptions}
                adminOptions={selectAdminOptions}
                response={edit?.data?.response as EstimationResponse}
                requestData={edit?.data?.requestDataJson as Record<string, any>}
                service={service as ServiceDDValue}
                createdDate={edit?.data?.createdDate ?? ''}
              />

              <LogDialog
                open={openLogsModal}
                onClose={handleCloseLogsModal}
                id={Number(instantQuoteId)}
                tableName={LogTableName.INSTANT_QUOTE_REQUEST_TABLE}
                columnName={LogColumnName.INSTANT_QUOTE_REQUEST_COLUMN}
              />
            </CardWrapper>
            <FormAction handleOpen={handleOpenLogsModal} />
          </>
        )}
      </Formik>
    </MainContent>
  );
};

export default InstantQuoteDetailsContainer;
