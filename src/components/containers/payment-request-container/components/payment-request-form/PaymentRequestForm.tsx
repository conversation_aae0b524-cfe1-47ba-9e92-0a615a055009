import type { ddValue } from 'src/shared/types/ddValue';
import type { PaymentRequest } from 'src/shared/services/payment-request/payment-request.type';

import { useState } from 'react';
import { useFormikContext } from 'formik';

import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Upload } from 'src/components/common/upload';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid } from './PaymentRequestForm.style';

interface PaymentRequestFormProps {
  readMode: boolean;
  paymentRequestStatusOptions: ddValue[];
}

const PaymentRequestForm = ({ readMode, paymentRequestStatusOptions }: PaymentRequestFormProps) => {
  const { values, handleChange, handleBlur, errors, touched, setFieldValue } =
    useFormikContext<PaymentRequest>();

  const [preview, setPreview] = useState(true);

  const handleDropInvoice = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];

      const mockResponse = {
        id: `inv-${Date.now()}`,
        fileName: file.name,
        fileUrl: URL.createObjectURL(file),
        uploadedAt: new Date().toISOString(),
      };
      setFieldValue('invoice', mockResponse);
    }
  };

  const handleRemoveInvoice = () => {
    setFieldValue('invoice', null);
  };

  return (
    <FormGrid container spacing={2}>
      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="customerName"
          name="customerName"
          label="Name"
          type="text"
          variant="outlined"
          value={values.customerName}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.customerName && touched.customerName)}
          helperText={touched.customerName ? errors.customerName : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="contactNumber"
          name="contactNumber"
          label="Contact Number"
          type="text"
          variant="outlined"
          value={values.contactNumber}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.contactNumber && touched.contactNumber)}
          helperText={touched.contactNumber ? errors.contactNumber : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="paymentId"
          name="paymentId"
          label="Payment ID"
          type="text"
          variant="outlined"
          value={values.paymentId}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.paymentId && touched.paymentId)}
          helperText={touched.paymentId ? errors.paymentId : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="city"
          name="city"
          label="City"
          type="text"
          variant="outlined"
          value={values.city}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.city && touched.city)}
          helperText={touched.city ? errors.city : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="service"
          name="service"
          label="Service"
          type="text"
          variant="outlined"
          value={values.service}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.service && touched.service)}
          helperText={touched.service ? errors.service : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="subCategory"
          name="subCategory"
          label="Sub Category"
          type="text"
          variant="outlined"
          value={values.subCategory}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.subCategory && touched.subCategory)}
          helperText={touched.subCategory ? errors.subCategory : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="amount"
          name="amount"
          label="Amount"
          type="number"
          variant="outlined"
          value={values.amount}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.amount && touched.amount)}
          helperText={touched.amount ? errors.amount : ''}
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="destination"
          name="destination"
          label="Destination"
          type="text"
          variant="outlined"
          value={values.destination}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.destination && touched.destination)}
          helperText={touched.destination ? errors.destination : ''}
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <FormControlLabel
          control={<Switch checked={preview} onChange={(e) => setPreview(e.target.checked)} />}
          label="Show Preview"
          sx={{ mb: 3, width: 1, justifyContent: 'flex-end' }}
        />
        <Upload
          multiple={false}
          thumbnail={preview}
          disabled={readMode}
          value={values.invoice?.fileUrl}
          error={Boolean(errors.invoice && touched.invoice)}
          helperText={touched.invoice ? (errors.invoice as string) : ''}
          onDrop={handleDropInvoice}
          onDelete={handleRemoveInvoice}
          accept={{
            'application/pdf': ['.pdf'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
          }}
        />
      </FormGrid>
    </FormGrid>
  );
};

export default PaymentRequestForm;
