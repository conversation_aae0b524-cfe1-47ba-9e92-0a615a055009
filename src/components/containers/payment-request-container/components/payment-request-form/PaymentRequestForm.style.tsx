import { styled } from '@mui/material/styles';
import { Grid, Button, Typography } from '@mui/material';

export const FormWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(2),
  paddingTop: '0rem',
  width: '100%',
  height: '100%',
}));

export const FormGrid = styled(Grid)(() => ({
  width: '100%',
  height: '100%',
}));

export const AdminSectionTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightSemiBold,
  paddingBlock: theme.spacing(1),
}));

export const FormSection = styled(Grid)(() => ({
  width: '100%',
}));

export const FieldWrapper = styled(Grid)(() => ({
  width: '100%',
}));

export const FileUploadBox = styled('div')(({ theme }) => ({
  border: `2px dashed ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),
  textAlign: 'center',
  backgroundColor: theme.palette.background.neutral,
}));

export const FilePreview = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.neutral,
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`,

  '& svg': {
    color: theme.palette.primary.main,
  },

  '& span': {
    flex: 1,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
}));

export const UploadButton = styled(Button)(({ theme }) => ({
  '&.MuiButton-root': {
    minWidth: 200,
  },
}));
