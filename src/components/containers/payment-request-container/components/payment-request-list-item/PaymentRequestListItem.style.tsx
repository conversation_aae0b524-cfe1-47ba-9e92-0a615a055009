import type { Theme } from 'src/theme/types';
import type { ChipProps } from '@mui/material';

import { Chip, Link } from '@mui/material';
import { styled } from '@mui/material/styles';

export const StatusChip = styled(Chip)(
  ({ theme, color = 'info' }: { theme?: Theme; color?: ChipProps['color'] }) => ({
    fontWeight: theme?.typography.fontWeightMedium,
    fontSize: theme?.typography.pxToRem(12),
    height: 24,
    minWidth: 72,
    '&:hover': {
      backgroundColor:
        color === 'default' ? theme?.palette.grey[300] : theme?.palette[color || 'info'].light,
    },
  })
);

export const UserNameLinkWrapper = styled(Link)(() => ({
  maxWidth: '200px',
  textDecoration: 'none',
  cursor: 'pointer',
  wordWrap: 'break-word',

  '&:hover': {
    textDecoration: 'underline',
  },
}));
