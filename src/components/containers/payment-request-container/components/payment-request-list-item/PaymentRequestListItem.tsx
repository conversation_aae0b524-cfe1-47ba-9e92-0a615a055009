import type { PaymentRequest } from 'src/shared/services/payment-request/payment-request.type';

import { Stack, Avatar, Tooltip, TableRow, TableCell, IconButton } from '@mui/material';

import { formatDate } from 'src/utils/date';

import { IconShare } from 'src/components/common/icons';

import { StatusChip, UserNameLinkWrapper } from './PaymentRequestListItem.style';

interface PaymentRequestListItemProps {
  request: PaymentRequest;
  onRowClick: () => void;
  onEditRow: () => void;
}

const getStatusColor = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'primary';
    case 'paid':
      return 'success';
    case 'expired':
      return 'error';
    default:
      return 'default';
  }
};

const PaymentRequestListItem = ({
  request,
  onRowClick,
  onEditRow,
}: PaymentRequestListItemProps) => (
  <TableRow hover onClick={onRowClick}>
    <TableCell align="center">{request.id}</TableCell>
    <TableCell
      onClick={(e) => {
        e.stopPropagation();
        onEditRow();
      }}
    >
      <Stack spacing={2} direction="row" alignItems="center">
        <Avatar alt={request.customerName} src="/static/mock-images/avatars/avatar_default.jpg" />
        <UserNameLinkWrapper color="inherit">{request.customerName}</UserNameLinkWrapper>
      </Stack>
    </TableCell>
    <TableCell>{formatDate(new Date(request.date))}</TableCell>
    <TableCell>{request.paymentId}</TableCell>
    <TableCell>{request.contactNumber}</TableCell>
    <TableCell>{request.city}</TableCell>
    <TableCell>
      <StatusChip
        variant="soft"
        label={request.status?.name || 'N/A'}
        size="small"
        color={getStatusColor(request.status?.name)}
      />
    </TableCell>
    <TableCell>
      <Tooltip title="Edit">
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onEditRow();
          }}
        >
          <IconShare width={20} />
        </IconButton>
      </Tooltip>
    </TableCell>
  </TableRow>
);

export default PaymentRequestListItem;
