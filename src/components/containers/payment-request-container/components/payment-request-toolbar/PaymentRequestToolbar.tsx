import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  PaymentRequestToolbarLeft,
  PaymentRequestFiltersView,
  PaymentRequestToolbarRight,
  PaymentRequestToolbarWrapper,
  PaymentRequestToolbarFormControl,
  PaymentRequestToolbarSearchWrapper,
} from './PaymentRequestToolbar.style';

interface PaymentRequestToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  paymentRequestStatusOptions: ddValue[];
}

const PaymentRequestToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  selectedStatus,
  paymentRequestStatusOptions,
  onFilterChange,
}: PaymentRequestToolbarProps) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = paymentRequestStatusOptions.find(
      ({ id }) => id.toString() === typeId.toString()
    );

    onStatusChange(status || ({} as ddValue));
    onFilterChange(
      {
        field: 'status',
        value: status?.id.toString() || '',
      },
      !status?.id
    );
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    onFilterChange(
      {
        field: 'status',
        value: '',
      },
      true
    );
  };

  const canReset = searchKey || selectedStatus?.id;

  return (
    <PaymentRequestToolbarWrapper>
      <PaymentRequestToolbarLeft>
        {canReset && (
          <PaymentRequestFiltersView>
            <FiltersResult totalResults={0}>
              <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
                <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
              </FiltersBlock>

              <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
                <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
              </FiltersBlock>
            </FiltersResult>
          </PaymentRequestFiltersView>
        )}
      </PaymentRequestToolbarLeft>

      <PaymentRequestToolbarRight>
        <PaymentRequestToolbarSearchWrapper>
          <SearchBar
            type="text"
            value={searchKey}
            onChange={handleSearch}
            placeholder="Search payment requests..."
          />
        </PaymentRequestToolbarSearchWrapper>
        <PaymentRequestToolbarFormControl>
          <CustomSelect
            value={selectedStatus?.id?.toString() ?? ''}
            handleChange={handleStatusChange}
            id="payment-request-status"
            label=""
            name="status"
            coreLabel="Status"
            options={paymentRequestStatusOptions}
            disabled={false}
            required={false}
          />
        </PaymentRequestToolbarFormControl>
      </PaymentRequestToolbarRight>
    </PaymentRequestToolbarWrapper>
  );
};

export default PaymentRequestToolbar;
