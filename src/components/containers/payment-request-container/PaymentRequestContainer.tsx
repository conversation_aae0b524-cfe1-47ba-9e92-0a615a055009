import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { Errors } from 'src/shared/types/errorResonse';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { PaymentRequest } from 'src/shared/services/payment-request/payment-request.type';

import { Formik } from 'formik';
import { useLocation, useNavigate } from 'react-router-dom';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { type SelectChangeEvent } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'src/hooks/use-debounce';

import { initialPaymentRequest } from 'src/utils/validation/payment-request/PaymentRequest.values';
import paymentRequestValidationSchema from 'src/utils/validation/payment-request/PaymentRequest.schema';

import { useToast } from 'src/providers/ToastProvider';
import {
  createPaymentRequest,
  updatePaymentRequest,
  deletePaymentRequest,
  searchPaymentRequests,
  getPaymentRequestStatusOptions,
} from 'src/shared/services/payment-request/payment-request.service';

import TableView from 'src/components/common/table/view/TableView';
import FormDialog from 'src/components/common/forms/form-dialog/FormDialog';
import NoDataView from 'src/components/common/error/no-data-view/NoDataView';
import { IconPlus, IconRefresh, IconDownload } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';
import {
  PaymentRequestButton,
  PaymentRequestWrapper,
  PaymentRequestOutlinedIconButton,
} from 'src/components/containers/payment-request-container/PaymentRequestContainer.style';

import PaymentRequestForm from './components/payment-request-form/PaymentRequestForm';
import PaymentRequestToolbar from './components/payment-request-toolbar/PaymentRequestToolbar';
import PaymentRequestListItem from './components/payment-request-list-item/PaymentRequestListItem';

const TABLE_HEAD = [
  { id: 'id', label: 'S.N', width: 80 },
  { id: 'customerName', label: 'Customer Name', width: 200 },
  { id: 'date', label: 'Date', width: 150 },
  { id: 'paymentId', label: 'Payment ID', width: 150 },
  { id: 'contactNumber', label: 'Contact', width: 150 },
  { id: 'city', label: 'City', width: 120 },
  { id: 'status', label: 'Status', width: 120 },
  { id: 'action', label: '', width: 80 },
];

const PaymentRequestContainer = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const openForm = useBoolean();
  const { showToast } = useToast();

  const [tableData, setTableData] = useState<PaymentRequest[]>([]);
  const [edit, setEdit] = useState<EditMode<PaymentRequest>>({} as EditMode<PaymentRequest>);
  const [isNew, setIsNew] = useState(false);

  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const debouncedSearchKey = useDebounce(searchKey, 500);
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [paymentRequestStatusOptions, setPaymentRequestStatusOptions] = useState<ddValue[]>([]);

  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);

  const getTableData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchPaymentRequests(criteria);
      setTableData(searchResult);
      setTotalCount(count);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch payment requests');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getTableData();
  }, [getTableData]);

  useEffect(() => {
    const loadPaymentRequestStatusOptions = async () => {
      try {
        setLoading(true);
        const data = await getPaymentRequestStatusOptions();
        setPaymentRequestStatusOptions(data);
      } catch (error) {
        setFetchError(error.message || 'failed to fetch');
      } finally {
        setLoading(false);
      }
    };

    loadPaymentRequestStatusOptions();
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleModalClose = useCallback(() => {
    openForm.onFalse();
    setEdit({} as EditMode<PaymentRequest>);
  }, [openForm]);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getTableData();
  }, [getTableData]);

  const handleEditRow = useCallback(
    (data: PaymentRequest) => {
      setEdit({ isRead: true, data, id: data?.id });
      openForm.onTrue();
    },
    [openForm]
  );

  const handleOpenDetails = useCallback(
    (requestId: number) => {
      navigate(`${pathname}/${requestId}`);
    },
    [navigate, pathname]
  );

  const handleCreateNew = useCallback(() => {
    setIsNew(true);
    setEdit({} as EditMode<PaymentRequest>);
    openForm.onTrue();
  }, [openForm]);

  const handleDelete = useCallback(
    async (request: PaymentRequest) => {
      try {
        setLoading(true);
        await deletePaymentRequest(request.id);
        await getTableData();
        showToast('Payment request deleted successfully.', 'success');
        handleModalClose();
      } catch (error) {
        showToast('Failed to delete payment request.', 'error');
      } finally {
        setLoading(false);
      }
    },
    [getTableData, showToast, handleModalClose]
  );

  const handleSubmit = useCallback(
    async (values: PaymentRequest): Promise<void> => {
      setLoading(true);

      try {
        if (isNew) {
          await createPaymentRequest(values);
          showToast('Payment request created successfully.', 'success');
        } else {
          await updatePaymentRequest(values.id, values);
          showToast('Payment request updated successfully.', 'success');
        }
        await getTableData();
        handleModalClose();
      } catch (error) {
        const errorResponse = error as Errors;
        const errorMessage =
          typeof errorResponse.errors === 'string'
            ? errorResponse.errors
            : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

        if (typeof errorMessage === 'string') {
          showToast(errorMessage, 'error');
        } else {
          Object.values(errorMessage).forEach((message) => showToast(message as string, 'error'));
        }
      } finally {
        setLoading(false);
      }
    },
    [isNew, getTableData, showToast, handleModalClose]
  );

  const memoizedTableData = useMemo(
    () => tableData.slice(0, rowsPerPage),
    [tableData, rowsPerPage]
  );

  return (
    <>
      <MainContent
        pageTitle="Payment Requests"
        buttons={
          <>
            <PaymentRequestOutlinedIconButton
              color="primary"
              size="large"
              onClick={handleRefreshData}
            >
              <IconRefresh />
            </PaymentRequestOutlinedIconButton>
            <PaymentRequestOutlinedIconButton size="large" color="primary">
              <IconDownload />
            </PaymentRequestOutlinedIconButton>
            <PaymentRequestButton
              variant="contained"
              color="primary"
              startIcon={<IconPlus />}
              onClick={handleCreateNew}
            >
              Add
            </PaymentRequestButton>
          </>
        }
      >
        <PaymentRequestWrapper>
          <PaymentRequestToolbar
            onSearchKey={handleSearchKey}
            searchKey={searchKey}
            paymentRequestStatusOptions={paymentRequestStatusOptions}
            selectedStatus={selectedStatus}
            onStatusChange={handleStatusUpdate}
            onFilterChange={handleFilterChange}
          />

          <TableView
            pagination={
              <TablePagination
                count={Math.ceil(totalCount / rowsPerPage)}
                currentPage={currentPage}
                onNext={() => setCurrentPage(currentPage + 1)}
                onPrev={() => setCurrentPage(currentPage - 1)}
                value={rowsPerPage.toString()}
                handleChange={handleRowsPerPageChange}
                totalCount={totalCount}
              />
            }
            tableHead={TABLE_HEAD}
            error={fetchError}
            loading={loading}
            tableData={memoizedTableData ?? []}
          >
            {memoizedTableData.length > 0 ? (
              memoizedTableData.map((row) => (
                <PaymentRequestListItem
                  key={row.id}
                  request={row}
                  onRowClick={() => handleOpenDetails(row.id)}
                  onEditRow={() => handleEditRow(row)}
                />
              ))
            ) : (
              <NoDataView />
            )}
          </TableView>
        </PaymentRequestWrapper>
      </MainContent>

      <Formik<PaymentRequest>
        initialValues={initialPaymentRequest(edit?.data as PaymentRequest)}
        onSubmit={handleSubmit}
        validationSchema={() => paymentRequestValidationSchema()}
        enableReinitialize
      >
        {() => (
          <FormDialog
            maxWidth="md"
            open={openForm.value}
            name={edit?.data?.customerName || ''}
            readMode={false}
            isNew={isNew}
            title={
              isNew ? 'Create Payment Request' : `Payment Request - ${edit?.data?.paymentId || ''}`
            }
            formLoading={loading}
            handleDeleteAction={() => handleDelete(edit?.data as PaymentRequest)}
            handleEditAction={() => {
              console.log('edit');
            }}
            onClose={handleModalClose}
            handleCancel={handleModalClose}
            content={
              <PaymentRequestForm
                readMode={loading}
                paymentRequestStatusOptions={paymentRequestStatusOptions}
              />
            }
          />
        )}
      </Formik>
    </>
  );
};

export default PaymentRequestContainer;
