import type { TFormEvent } from 'src/components/common/forms/types/event.types';

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { Stack, IconButton } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { validate } from 'src/utils/validation/auth/form-validation';

import { createPassword } from 'src/shared/services/auth/auth.service';

import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  CreateFormTitle,
  CreatePasswordForm,
  CreatePasswordWrapper,
  CreatePassLoadingButton,
} from './CreatePasswordContainer.style';

export interface CreatePasswordProps {
  token: string | null;
}

interface PasswordButtonActionProps {
  handleClickShowPassword: () => void;
  showPassword: boolean;
}

type CreatePasswordStateProps = {
  password: string;
  confirmPassword: string;
};

type CreatePasswordErrStateProps = {
  password: string;
  confirmPassword: string;
};

const CreatePasswordContainer = ({ token }: CreatePasswordProps) => {
  const navigate = useNavigate();
  const showPassword = useBoolean();
  const showConfirmPassword = useBoolean();
  const loading = useBoolean();
  const [createForm, setCreateForm] = useState({} as CreatePasswordStateProps);
  const [error, setErrors] = useState({} as CreatePasswordErrStateProps);

  const checkToken = () => {
    if (!token) {
      navigate('/auth/signin');
    }
  };

  useEffect(() => {
    checkToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCreateForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: TFormEvent) => {
    e.preventDefault();

    checkToken();

    const matchPassword = {
      password: createForm?.password,
      confirmPassword: createForm?.confirmPassword,
    };

    const { isValidPass, passErrorMsg, confirmPassErrorMsg } =
      validate.isSamePassword(matchPassword);

    if (isValidPass) {
      try {
        const { confirmPassword, password } = createForm;
        createPassword({ confirmPassword, password, url: token });
      } catch (err: unknown) {
        console.log(err);
      } finally {
        setCreateForm({ confirmPassword: '', password: '' });
      }
    } else {
      setErrors((prev) => ({ ...prev, password: passErrorMsg }));
      setErrors((prev) => ({ ...prev, confirmPassword: confirmPassErrorMsg }));
    }
  };

  return (
    <CreatePasswordWrapper>
      <CreatePasswordForm onSubmit={handleSubmit}>
        <CreateFormTitle variant="subtitle1">Create your Password</CreateFormTitle>
        <Stack direction="column" spacing={2}>
          <CustomTextField
            id="password"
            name="password"
            label="New Password"
            type={showPassword.value ? 'text' : 'password'}
            value={createForm.password ?? ''}
            variant="outlined"
            handleChange={handleInputChange}
            handleBlur={() => {}}
            error={!!error.password}
            helperText={error.password}
            endAdornment={
              <PasswordButtonAction
                handleClickShowPassword={() => showPassword.onToggle()}
                showPassword={showPassword.value}
              />
            }
          />
          <CustomTextField
            id="confirmPassword"
            name="confirmPassword"
            label="Confirm Password"
            type={showConfirmPassword.value ? 'text' : 'password'}
            value={createForm.confirmPassword ?? ''}
            variant="outlined"
            handleChange={handleInputChange}
            handleBlur={() => {}}
            error={!!error.confirmPassword}
            helperText={error.confirmPassword}
            endAdornment={
              <PasswordButtonAction
                handleClickShowPassword={() => showConfirmPassword.onToggle()}
                showPassword={showConfirmPassword.value}
              />
            }
          />
          <CreatePassLoadingButton
            fullWidth
            size="large"
            type="submit"
            variant="contained"
            loading={loading.value}
            disabled={loading.value}
            onClick={handleSubmit}
          >
            Create Password
          </CreatePassLoadingButton>
        </Stack>
      </CreatePasswordForm>
    </CreatePasswordWrapper>
  );
};

const PasswordButtonAction = ({
  handleClickShowPassword,
  showPassword,
}: PasswordButtonActionProps) => (
  <IconButton
    edge="start"
    onClick={handleClickShowPassword}
    aria-label={showPassword ? 'Hide password' : 'Show password'}
  >
    {showPassword ? (
      <Iconify icon={Icons.password.eyeOpen} />
    ) : (
      <Iconify icon={Icons.password.eyeClose} />
    )}
  </IconButton>
);

export default CreatePasswordContainer;
