import { useState } from 'react';

import {
  Table,
  Paper,
  Stack,
  Button,
  TableRow,
  Checkbox,
  TableBody,
  TableCell,
  TableHead,
  IconButton,
  Typography,
} from '@mui/material';

import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import {
  HeaderBox,
  ActionCell,
  IconWrapper,
  FileNameCell,
  FileTypeIcon,
  StyledTableContainer,
} from './JobApplicationFileTable.style';

interface FileTableProps {
  files: any[];
  onOpenFile: (fileUrl: string) => void;
  onDownloadFile: (fileUrl: string) => void;
}

const getFileIcon = (fileType: string) => {
  const iconProps = {
    width: 20,
  };

  switch (fileType.toLowerCase()) {
    case 'pdf':
      return <Iconify icon={Icons.files.pdf} sx={{ color: 'error.main' }} {...iconProps} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
      return <Iconify icon={Icons.files.image} sx={{ color: 'success.main' }} {...iconProps} />;
    case 'txt':
      return <Iconify icon={Icons.files.default} sx={{ color: 'info.main' }} {...iconProps} />;
    default:
      return <Iconify icon={Icons.files.default} sx={{ color: 'text.secondary' }} {...iconProps} />;
  }
};

const getFileType = (fileName: string) => fileName.split('.').pop() || 'unknown';

const FileTable = ({ files, onOpenFile, onDownloadFile }: FileTableProps) => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  const handleSelectAll = (checked: boolean) => {
    setSelectedFiles(checked ? files.map((file) => file.url) : []);
  };

  const handleSelectFile = (fileUrl: string, checked: boolean) => {
    setSelectedFiles((prev) => (checked ? [...prev, fileUrl] : prev.filter((f) => f !== fileUrl)));
  };

  const handleDownloadAll = () => {
    selectedFiles.forEach((fileUrl) => onDownloadFile(fileUrl));
  };

  return (
    <Paper elevation={0} variant="outlined">
      <HeaderBox>
        <Stack direction="row" spacing={2} alignItems="center">
          <Checkbox
            checked={selectedFiles?.length === files?.length}
            indeterminate={selectedFiles?.length > 0 && selectedFiles?.length < files?.length}
            onChange={(e) => handleSelectAll(e.target.checked)}
          />
          <Typography variant="subtitle1" fontWeight="medium">
            Files ({files?.length})
          </Typography>
        </Stack>
        <Button
          variant="contained"
          startIcon={<Iconify icon={Icons.files.download} />}
          disabled={selectedFiles?.length === 0}
          onClick={handleDownloadAll}
        >
          Download All
        </Button>
      </HeaderBox>

      <StyledTableContainer>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox" />
              <TableCell>File Name</TableCell>
              <TableCell>Size</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {files?.map((file, index) => {
              const fileType = getFileType(file?.name);

              return (
                <TableRow key={index} hover>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedFiles.includes(file?.url)}
                      onChange={(e) => handleSelectFile(file?.url, e.target.checked)}
                    />
                  </TableCell>
                  <FileNameCell>
                    <FileTypeIcon>
                      <IconWrapper>{getFileIcon(fileType)}</IconWrapper>
                      <Typography variant="body2" noWrap>
                        {file?.name}
                      </Typography>
                    </FileTypeIcon>
                  </FileNameCell>
                  <TableCell>{file?.size}</TableCell>
                  <ActionCell align="right">
                    <IconButton
                      onClick={() => onOpenFile(file?.url)}
                      title="View File"
                      sx={{ mr: 1 }}
                    >
                      <Iconify icon={Icons.files.view} width={20} />
                    </IconButton>
                    <IconButton onClick={() => onDownloadFile(file?.url)} title="Download File">
                      <Iconify icon={Icons.files.download} width={20} />
                    </IconButton>
                  </ActionCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </StyledTableContainer>
    </Paper>
  );
};

export default FileTable;
