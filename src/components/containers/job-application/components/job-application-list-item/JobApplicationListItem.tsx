import type { JobApplication } from 'src/shared/services/job-applications/job-applications.type';

import { useNavigate } from 'react-router';

import { Stack, TableRow, TableCell, IconButton } from '@mui/material';

import { appendZeroes } from 'src/utils/helper';
import { formatDate, formatTime } from 'src/utils/date';
import { copyToClipboard } from 'src/utils/copyToClipboard';

import { useToast } from 'src/providers/ToastProvider';

import { IconShare } from 'src/components/common/icons';
import IconCopy from 'src/components/common/icons/IconCopy';
import StatusChip from 'src/components/common/status-chip/StatusChip';

import {
  CopyWrapper,
  EmailWrapper,
  DateTimeRoot,
  DateFormatWrapper,
  TimeFormatWrapper,
  UserNameLinkWrapper,
} from './JobApplicationListItem.style';

interface JobApplicationListItemProps {
  jobApplication: JobApplication;
}

const JobApplicationListItem = ({ jobApplication }: JobApplicationListItemProps) => {
  const navigate = useNavigate();
  const { showToast } = useToast();

  const formattedDate = formatDate(jobApplication?.createdDate ?? '');
  const formattedTime = formatTime(jobApplication?.createdDate ?? '');

  const handleRowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/careers/applications/${jobApplication.id}`, { replace: true });
  };

  const handleCopyClick = (e: React.MouseEvent, text: string) => {
    e.stopPropagation();
    copyToClipboard(text);
    showToast(`Copied to Clipboard`, 'success');
  };

  return (
    <TableRow hover onClick={handleRowClick} style={{ cursor: 'pointer' }}>
      <TableCell>{appendZeroes(jobApplication.id, 4) ?? '---'}</TableCell>
      <TableCell>
        <Stack spacing={2} direction="row" alignItems="center">
          <UserNameLinkWrapper color="inherit">{jobApplication.name ?? '---'}</UserNameLinkWrapper>
        </Stack>
      </TableCell>
      <EmailWrapper>
        <CopyWrapper onClick={(e) => handleCopyClick(e, jobApplication.emailId ?? '')}>
          <span>{jobApplication.emailId ?? '---'}</span>
          <IconCopy style={{ width: 18, marginLeft: 6 }} />
        </CopyWrapper>
      </EmailWrapper>
      <TableCell>
        <CopyWrapper onClick={(e) => handleCopyClick(e, jobApplication.phone ?? '')}>
          <span>{jobApplication.phone ?? '---'}</span>
          <IconCopy style={{ width: 18, marginLeft: 6 }} />
        </CopyWrapper>
      </TableCell>

      <TableCell>{jobApplication?.jobPosition?.department?.name ?? '---'}</TableCell>
      <TableCell>
        <StatusChip
          variant="soft"
          label={jobApplication.jobApplicationStatus?.name ?? '---'}
          color={jobApplication.jobApplicationStatus?.colorCode ?? '#000000'}
        />
      </TableCell>
      <TableCell>
        <DateTimeRoot>
          <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
          <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
        </DateTimeRoot>
      </TableCell>
      <TableCell align="right">
        <IconButton onClick={handleRowClick}>
          <IconShare style={{ width: 24 }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default JobApplicationListItem;
