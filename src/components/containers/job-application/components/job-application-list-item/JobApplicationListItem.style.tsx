import { styled } from '@mui/material/styles';
import { Link, Stack, TableCell } from '@mui/material';

export const UserNameLinkWrapper = styled(Link)(() => ({
  maxWidth: '200px',
  textDecoration: 'none',
  cursor: 'pointer',
  wordWrap: 'break-word',

  '&:hover': {
    textDecoration: 'underline',
  },
}));

export const EmailWrapper = styled(TableCell)(() => ({
  maxWidth: '260px',
  wordWrap: 'break-word',
}));

export const DateTimeRoot = styled(Stack)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  alignItems: 'flex-start',
}));

export const DateFormatWrapper = styled('span')(({ theme }) => ({
  color: theme.palette.text.secondary,
  minWidth: 'max-content',
  width: '120px',
}));
export const TimeFormatWrapper = styled('span')(({ theme }) => ({
  color: theme.palette.text.disabled,
}));

export const StyledTextWrapper = styled('span')(() => ({
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'normal',
  maxWidth: '100%',
}));

export const CopyWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  cursor: 'pointer',
}));
