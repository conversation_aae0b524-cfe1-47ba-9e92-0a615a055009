import { styled } from '@mui/material/styles';

export const StyledToolbarWrapper = styled('div')(({ theme }) => ({}));
export const StyledFields = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingBlock: theme.spacing(2.5),
}));

export const StyledToolbarLeft = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  gap: '1rem',
}));

export const StyledToolbarRight = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
}));

export const StyledToolbarFormControl = styled('div')(({ theme }) => ({
  flexShrink: 0,
  width: 200,
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

export const StyledToolbarSearchWrapper = styled('div')(() => ({
  width: '60%',
}));

export const StyledFiltersView = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(1),
}));
