import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { useMemo } from 'react';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';
import { useAuth } from 'src/providers/AuthProvider';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  StyledFields,
  StyledToolbarLeft,
  StyledFiltersView,
  StyledToolbarRight,
  StyledToolbarWrapper,
  StyledToolbarFormControl,
  StyledToolbarSearchWrapper,
} from './JobApplicationToolbar.style';

export interface JobApplicationToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onAssignedAdminChange: (admin: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  selectedAssignedAdmin: ddValue;
  responseStatusOptions: ddValue[];
  assignedAdminOptions: ddValue[];
  totalCount?: number;
}

const JobApplicationToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  onAssignedAdminChange,
  selectedStatus,
  selectedAssignedAdmin,
  responseStatusOptions,
  assignedAdminOptions,
  onFilterChange,
  totalCount = 0,
}: JobApplicationToolbarProps) => {
  const { userName } = useAuth();

  const preparedAdminOptions = useMemo(
    () =>
      assignedAdminOptions
        .map((admin) => {
          if (admin.name === userName) {
            return { ...admin, name: 'Assigned to Me' };
          }
          return admin;
        })
        .sort((a, b) => {
          if (a.name === 'Assigned to Me') return -1;
          if (b.name === 'Assigned to Me') return 1;
          return 0;
        }),
    [assignedAdminOptions, userName]
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const statusId = event.target.value;
    const status = responseStatusOptions.find(({ id }) => id.toString() === statusId.toString());
    onStatusChange(status ?? ({} as ddValue));
    onFilterChange(
      {
        field: 'jobApplicationStatusId',
        value: status ? status.id.toString() : '',
      },
      !status?.id
    );
  };

  const handleAssignedAdminChange = (event: TInputChangeEvent) => {
    const adminId = event.target.value;
    const admin = assignedAdminOptions.find(({ id }) => id.toString() === adminId.toString());
    onAssignedAdminChange(admin || ({} as ddValue));
    onFilterChange(
      {
        field: 'assignedAdminId',
        value: admin?.id.toString() || '',
      },
      !admin?.id
    );
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    onFilterChange(
      {
        field: 'jobApplicationStatusId',
        value: '',
      },
      true
    );
  };

  const handleClearAssignedAdmin = () => {
    onAssignedAdminChange({} as ddValue);
    onFilterChange(
      {
        field: 'assignedAdminId',
        value: '',
      },
      true
    );
  };

  const canReset =
    Boolean(searchKey) || Boolean(selectedStatus?.id) || Boolean(selectedAssignedAdmin?.id);

  return (
    <StyledToolbarWrapper>
      <StyledFields>
        <StyledToolbarLeft>
          <StyledToolbarSearchWrapper>
            <SearchBar
              type="text"
              value={searchKey}
              onChange={handleSearch}
              placeholder="Search applications..."
            />
          </StyledToolbarSearchWrapper>
        </StyledToolbarLeft>
        <StyledToolbarRight>
          <StyledToolbarFormControl>
            <CustomSelect
              value={selectedStatus?.id?.toString() ?? ''}
              handleChange={handleStatusChange}
              id="job-application-status"
              label=""
              name="status"
              coreLabel="Status"
              options={responseStatusOptions}
              disabled={responseStatusOptions.length === 0}
              required={false}
            />
          </StyledToolbarFormControl>
          <StyledToolbarFormControl>
            <CustomSelect
              value={selectedAssignedAdmin?.id?.toString() ?? ''}
              handleChange={handleAssignedAdminChange}
              id="job-application-assigned-admin"
              label=""
              name="assignedAdmin"
              coreLabel="Assigned to"
              options={preparedAdminOptions}
              disabled={preparedAdminOptions.length === 0}
              required={false}
            />
          </StyledToolbarFormControl>
        </StyledToolbarRight>
      </StyledFields>

      {canReset && (
        <StyledFiltersView>
          <FiltersResult totalResults={totalCount}>
            <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
              <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
            </FiltersBlock>
            <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
              <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
            </FiltersBlock>
            <FiltersBlock label="Assigned to:" isShow={Boolean(selectedAssignedAdmin?.id)}>
              <Chip
                {...chipProps}
                label={selectedAssignedAdmin?.name}
                onDelete={handleClearAssignedAdmin}
              />
            </FiltersBlock>
          </FiltersResult>
        </StyledFiltersView>
      )}
    </StyledToolbarWrapper>
  );
};

export default JobApplicationToolbar;
