import type { SelectChangeEvent } from '@mui/material';
import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { JobApplication } from 'src/shared/services/job-applications/job-applications.type';

import { useMemo, useState, useEffect, useCallback } from 'react';

import { useDebounce } from 'src/hooks/use-debounce';

import { getUsers } from 'src/shared/services/admin/users/users.service';
import {
  searchJobApplications,
  getJobApplicationStatusOptions,
} from 'src/shared/services/job-applications/job-applications.service';

import { IconRefresh } from 'src/components/common/icons';
import TableView from 'src/components/common/table/view/TableView';
import NoDataView from 'src/components/common/error/no-data-view/NoDataView';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import JobApplicationToolbar from './components/job-application-toolbar/JobApplicationToolbar';
import JobApplicationListItem from './components/job-application-list-item/JobApplicationListItem';
import {
  JobPositionsWrapper,
  JobPositionsOutlinedIconButton,
} from '../job-positions-container/JobPositionsContainer.style';

const TABLE_HEAD = [
  { id: 'id', label: 'ID' },
  { id: 'name', label: 'Name', width: 300 },
  { id: 'emailId', label: 'Email', width: 400 },
  { id: 'phone', label: 'Phone', width: 150 },
  { id: 'department', label: 'Department', width: 250 },
  { id: 'jobApplicationStatus', label: 'Status', width: 80 },
  { id: 'createdDate', label: 'Applied On', width: 180 },
  { id: 'action', label: '', align: 'right' },
];

const JobApplicationContainer = () => {
  const [tableData, setTableData] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [jobApplicationStatusOptions, setJobApplicationStatusOptions] = useState<ddValue[]>([]);
  const [assignedAdminOptions, setAssignedAdminOptions] = useState<ddValue[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);
  const [selectedAssignedAdmin, setSelectedAssignedAdmin] = useState<ddValue>({} as ddValue);

  const debouncedSearchKey = useDebounce(searchKey, 500);

  const getJobApplicationsData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchJobApplications(criteria);
      setTableData(searchResult);
      setTotalCount(count);
      setFetchError(null);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch job applications');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getJobApplicationsData();
  }, [getJobApplicationsData]);

  useEffect(() => {
    let isMounted = true;
    const loadOptions = async () => {
      try {
        const [statusData, adminUsers] = await Promise.all([
          getJobApplicationStatusOptions(),
          getUsers(),
        ]);
        if (isMounted) {
          setJobApplicationStatusOptions(statusData);
          const adminData = (adminUsers as any[]).map((user) => ({
            id: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}`,
            isDisabled: false,
          }));
          setAssignedAdminOptions(adminData);
        }
      } catch (error: any) {
        setFetchError(error.message || 'Failed to load options');
      }
    };
    loadOptions();
    return () => {
      isMounted = false;
    };
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleAssignedAdminChange = useCallback((admin: ddValue): void => {
    setSelectedAssignedAdmin(admin);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
    setCurrentPage(1);
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getJobApplicationsData();
  }, [getJobApplicationsData]);

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <MainContent
      pageTitle="Job Applications"
      buttons={
        <JobPositionsOutlinedIconButton onClick={handleRefreshData}>
          <IconRefresh style={{ width: 18 }} />
        </JobPositionsOutlinedIconButton>
      }
    >
      <JobPositionsWrapper>
        <JobApplicationToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          responseStatusOptions={jobApplicationStatusOptions}
          assignedAdminOptions={assignedAdminOptions}
          selectedStatus={selectedStatus}
          selectedAssignedAdmin={selectedAssignedAdmin}
          onStatusChange={handleStatusUpdate}
          onAssignedAdminChange={handleAssignedAdminChange}
          onFilterChange={handleFilterChange}
          totalCount={totalCount}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              currentPage={currentPage}
              onNext={() => setCurrentPage((prev) => prev + 1)}
              onPrev={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
              totalCount={totalCount}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData?.length > 0 ? (
            memoizedTableData.map((row) => (
              <JobApplicationListItem key={row.id} jobApplication={row} />
            ))
          ) : (
            <NoDataView />
          )}
        </TableView>
      </JobPositionsWrapper>
    </MainContent>
  );
};

export default JobApplicationContainer;
