import { Link } from 'react-router-dom';

import { LoadingButton } from '@mui/lab';
import { styled, Typography } from '@mui/material';

export const LoginFormViewWrapper = styled('div')(({ theme }) => ({
  width: '100%',
  height: '100%',
}));

export const LoginFormContainer = styled('form')(() => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  alignContent: 'flex-start',
  gap: '10px',
  width: '100%',
  marginInline: 'auto',
}));

export const LoginFormContainerTitle = styled(Typography)(({ theme }) => ({
  fontWeight: theme.typography.fontWeightBold,
  marginBottom: '1.5rem',
}));

export const LoginFormForgotPassword = styled(Link)(({ theme }) => ({
  color: theme.palette.common.black,
  fontSize: theme.typography.h6.fontSize,
  width: 'fit-content',
}));

export const LoginFormLoadingButton = styled(LoadingButton)(({ theme }) => ({
  background: '#B92F12',
  marginTop: '1rem',
  '&:hover': {
    background: '#c93618',
  },
}));
