import type { TFormEvent } from 'src/components/common/forms/types/event.types';

import { useState } from 'react';

import { Stack, IconButton } from '@mui/material';

import { paths } from 'src/routes/paths';

import { validate } from 'src/utils/validation/auth/form-validation';

import { useAuth } from 'src/providers/AuthProvider';

import Loader from 'src/components/common/loader/loader/Loader';
import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  LoginFormContainer,
  LoginFormViewWrapper,
  LoginFormLoadingButton,
  LoginFormContainerTitle,
  LoginFormForgotPassword,
} from './LoginContainer.style';

interface PasswordButtonActionProps {
  handleClickShowPassword: () => void;
  showPassword: boolean;
}

type LoginForm = {
  email: string;
  password: string;
};

type LoginError = {
  emailError: string;
  passwordError: string;
};

const LoginContainer = () => {
  const { login, loading } = useAuth();
  const [loginForm, setLoginForm] = useState({} as LoginForm);
  const [errors, setErrors] = useState({} as LoginError);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setLoginForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleClickShowPassword = () => {
    setShowPassword((prev) => !prev);
  };

  const handleSubmit = async (e: TFormEvent) => {
    e.preventDefault();

    setErrors({ emailError: '', passwordError: '' });
    const { isValidEmail, emailErrorMsg } = validate.email(loginForm?.email);
    const { isValidPass, passErrorMsg } = validate.password(loginForm?.password);

    if (isValidEmail && isValidPass) {
      try {
        const { email, password } = loginForm;
        login({ email, password });
      } catch (err: unknown) {
        console.log('LOGIN', err);
      } finally {
        setLoginForm({ email: '', password: '' });
      }
    } else {
      setErrors((prev) => ({ ...prev, emailError: emailErrorMsg }));
      setErrors((prev) => ({ ...prev, passwordError: passErrorMsg }));
    }
  };

  return (
    <>
      <Loader loading={loading} />
      <LoginFormViewWrapper>
        <LoginFormContainer onSubmit={handleSubmit}>
          <LoginFormContainerTitle variant="subtitle1">
            Sign in to your account
          </LoginFormContainerTitle>
          <Stack direction="column" spacing={2} width="100%">
            <CustomTextField
              id="email"
              name="email"
              label="Email Address"
              type="email"
              value={loginForm.email ?? ''}
              variant="outlined"
              handleChange={handleInputChange}
              handleBlur={() => {}}
              error={!!errors.emailError}
              helperText={errors.emailError}
            />
            <CustomTextField
              id="password"
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={loginForm.password ?? ''}
              variant="outlined"
              handleChange={handleInputChange}
              handleBlur={() => {}}
              error={!!errors.passwordError}
              helperText={errors.passwordError}
              endAdornment={
                <PasswordButtonAction
                  handleClickShowPassword={handleClickShowPassword}
                  showPassword={showPassword}
                />
              }
            />
            <LoginFormForgotPassword to={paths.auth.forgorPassword}>
              Forgot Password?
            </LoginFormForgotPassword>
            <LoginFormLoadingButton
              fullWidth
              size="large"
              type="submit"
              variant="contained"
              loading={loading}
              disabled={loading}
              loadingIndicator="Signing in..."
              onClick={handleSubmit}
            >
              Sign in
            </LoginFormLoadingButton>
          </Stack>
        </LoginFormContainer>
      </LoginFormViewWrapper>
    </>
  );
};

const PasswordButtonAction = ({
  handleClickShowPassword,
  showPassword,
}: PasswordButtonActionProps) => (
  <IconButton
    edge="start"
    onClick={handleClickShowPassword}
    aria-label={showPassword ? 'Hide password' : 'Show password'}
  >
    {showPassword ? (
      <Iconify icon={Icons.password.eyeOpen} />
    ) : (
      <Iconify icon={Icons.password.eyeClose} />
    )}
  </IconButton>
);

export default LoginContainer;
