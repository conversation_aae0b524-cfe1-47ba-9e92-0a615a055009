import { styled } from '@mui/material/styles';
import { Box, Button, Typography } from '@mui/material';

export const JobPostingSearchWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(3),
  marginInline: theme.spacing(4),
  padding: theme.spacing(4, 3),
  background: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius * 1.5,
  border: `1px solid ${theme.palette.grey[300]}`,
}));

export const StyledTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.secondary.main,
}));

export const FieldRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'flex-end',
  gap: theme.spacing(2),
}));

export const StyledButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(2.6, 3),
}));

export const JobPositionSelectFlex = styled('div')({
  flex: 1,
});

export const CreateJobPostingBtn = styled('button')(({ theme }) => ({
  minWidth: 180,
}));
