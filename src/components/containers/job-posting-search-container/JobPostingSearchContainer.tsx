import type { ddValue } from 'src/shared/types/ddValue';
import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { useToast } from 'src/providers/ToastProvider';
import { createJobPosting } from 'src/shared/services/job-postings/job-postings.service';
import { searchJobPositions } from 'src/shared/services/job-positions/job-positions.service';

import Loader from 'src/components/common/loader/loader/Loader';
import CustomSelect from 'src/components/common/forms/select/Select';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import {
  FieldRow,
  StyledTitle,
  StyledButton,
  JobPositionSelectFlex,
  JobPostingSearchWrapper,
} from './JobPostingSearchContainer.style';

const JobPostingSearchContainer = () => {
  const [positions, setPositions] = useState<ddValue[]>([]);
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { showToast } = useToast();

  useEffect(() => {
    const fetchPositions = async () => {
      setLoading(true);
      try {
        const { searchResult } = await searchJobPositions({
          searchKey: '',
          filters: [{ field: 'jobPositionStatusId', value: '1' }],
          sortOptions: [],
          pagination: { pageNumber: 1, pageSize: 100 },
        });
        setPositions(
          searchResult.map((pos: JobPosition) => ({
            id: pos.id,
            name: `${pos.positionTitle} | ${pos.department?.name || ''} | ${pos.jobPositionType?.name || ''}`,
            isDisabled: false,
          }))
        );
      } catch (e) {
        setPositions([]);
      } finally {
        setLoading(false);
      }
    };
    fetchPositions();
  }, []);

  const handleChange = (e: React.ChangeEvent<{ value: string }>) => {
    setSelectedPosition(e.target.value as string);
  };

  const handleCreateJobPosting = async () => {
    if (selectedPosition) {
      try {
        setLoading(true);
        const POST_DATA: JobPosting = {
          id: 0,
          jobPositionId: Number(selectedPosition),
          jobPostingStatusId: 3,
          jobPostingTitle: '',
          minimumExperienceInYears: 0,
          jobLocations: [],
          lastModified: new Date().toISOString(),
          createdDate: new Date().toISOString(),
          createdById: 1,
          lastModifiedById: 1,
          shouldKeepOpenUntilStopped: false,
        };
        const created = await createJobPosting(POST_DATA);
        console.log(created, 'Job Posting Created');
        showToast('Job Posting is created', 'success');
        navigate(`/careers/postings/${created?.id}`);
      } catch (error: any) {
        showToast(error?.message || 'Failed to create job posting', 'error');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <MainContent pageTitle="Job Posting | New">
      <Loader loading={loading} />
      <JobPostingSearchWrapper>
        <StyledTitle variant="h5">Select Job Position</StyledTitle>
        <FieldRow>
          <JobPositionSelectFlex>
            <CustomSelect
              id="job-position-select"
              name="jobPositionId"
              label="Job Position"
              options={positions}
              value={selectedPosition}
              handleChange={handleChange}
              required
            />
          </JobPositionSelectFlex>
          <StyledButton
            variant="contained"
            color="primary"
            onClick={handleCreateJobPosting}
            disabled={!selectedPosition}
          >
            Create New Job Posting
          </StyledButton>
        </FieldRow>
      </JobPostingSearchWrapper>
    </MainContent>
  );
};

export default JobPostingSearchContainer;
