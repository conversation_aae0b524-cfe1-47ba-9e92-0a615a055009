import { styled } from '@mui/material/styles';
import { Card, Button, Tooltip, IconButton } from '@mui/material';

import { IconTrash } from 'src/components/common/icons';

export const CardWrapper = styled(Card)(({ theme }) => ({
  boxShadow: 'none',
  border: `1px solid ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  marginInline: theme.spacing(4),
  marginBlock: theme.spacing(2),
  padding: theme.spacing(4),
}));

export interface ButtonsProps {
  isLoading: boolean;
  handleOpenDeleteDialog: () => void;
  handlePublish: () => void;
}

export const Buttons = (props: ButtonsProps) => {
  const { isLoading, handleOpenDeleteDialog, handlePublish } = props;

  return (
    <>
      <Tooltip title="Delete" key="delete">
        <IconButton color="error" onClick={handleOpenDeleteDialog} disabled={isLoading}>
          <IconTrash style={{ width: 24 }} />
        </IconButton>
      </Tooltip>
      <Button variant="contained" color="info" onClick={handlePublish}>
        Publish
      </Button>
    </>
  );
};
