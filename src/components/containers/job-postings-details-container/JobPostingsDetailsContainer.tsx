import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';

import { Formik } from 'formik';
import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

import { Button } from '@mui/material';

import { initialJobPosting } from 'src/utils/validation/careers/job-posting/job-posting.values';

import { useToast } from 'src/providers/ToastProvider';
import {
  getDepartmentOptions,
  getJobPositionTypeOptions,
} from 'src/shared/services/job-positions/job-positions.service';
import {
  createJobPosting,
  updateJobPosting,
  deleteJobPosting,
  getJobPostingById,
} from 'src/shared/services/job-postings/job-postings.service';

import Loader from 'src/components/common/loader/loader/Loader';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import FormAction from './components/form-actions/FormAction';
import { CardWrapper } from './JobPostingsDetailsContainer.style';
import JobPostingsForm from './components/job-postings-form/JobPostingsForm';

const JobPostingsDetailsContainer = () => {
  const { postingId } = useParams<{ postingId: string }>();
  const navigate = useNavigate();
  const { showToast } = useToast();

  const [edit, setEdit] = useState<EditMode<JobPosting>>({} as EditMode<JobPosting>);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [departmentOptions, setDepartmentOptions] = useState<ddValue[]>([]);
  const [jobPositionTypeOptions, setJobPositionTypeOptions] = useState<ddValue[]>([]);
  const [notFound, setNotFound] = useState<boolean>(false);

  const isNew = postingId === 'new';

  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true);
      let jobPosting: JobPosting | undefined;
      let isRead = false;

      if (isNew) {
        jobPosting = initialJobPosting();
      } else if (Number.isInteger(Number(postingId))) {
        jobPosting = await getJobPostingById(Number(postingId));
        isRead = true;
        if (!jobPosting) {
          setNotFound(true);
          return;
        }
      } else {
        setNotFound(true);
        return;
      }

      setEdit({
        data: jobPosting as JobPosting,
        isEdit: false,
        isRead,
      });

      const [departmentData, jobPositionTypeData] = await Promise.all([
        getDepartmentOptions(),
        getJobPositionTypeOptions(),
      ]);

      setDepartmentOptions(departmentData);
      setJobPositionTypeOptions(jobPositionTypeData);
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [postingId, isNew]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  const handleEditMode = () => {
    setEdit((prev) => ({ ...prev, isEdit: true, isRead: false }));
  };

  const handleCancel = () => {
    setEdit((prev) => ({ ...prev, isEdit: false, isRead: true }));
  };

  const handleDelete = async () => {
    if (!edit?.data?.id) return;
    setIsLoading(true);
    try {
      await deleteJobPosting(edit.data.id);
      showToast('Job Posting Successfully Deleted', 'success');
      navigate('/careers/postings', { replace: true });
    } catch (error) {
      showToast(`Failed to delete: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePublish = async () => {
    if (!edit?.data?.id) return;
    setIsLoading(true);
    try {
      const postData: JobPosting = {
        id: edit?.data?.id,
        jobPostingStatusId: 1,
        jobPositionId: edit?.data?.jobPositionId,
        jobPostingTitle: edit?.data?.jobPostingTitle,
        minimumExperienceInYears: edit?.data?.minimumExperienceInYears,
        createdById: 2,
        lastModifiedById: 2,
        jobLocations: edit?.data?.jobLocations || [],
        lastModified: edit?.data?.lastModified || '',
        createdDate: edit?.data?.createdDate || '',
        shouldKeepOpenUntilStopped: true,
      };
      await updateJobPosting(postData);
      showToast('Job Posting Successfully Published', 'success');
      await fetchInitialData();
      setEdit((prev) => ({ ...prev, isEdit: false, isRead: true }));
    } catch (error) {
      showToast(`Failed to publish job posting: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseJob = async () => {
    if (!edit?.data?.id) return;
    setIsLoading(true);
    try {
      const postData: JobPosting = {
        ...edit.data,
        jobPostingStatusId: 2,
      };
      await updateJobPosting(postData);
      showToast('Job Posting Successfully Closed', 'success');
      await fetchInitialData();
      setEdit((prev) => ({ ...prev, isEdit: false, isRead: true }));
    } catch (error) {
      showToast(`Failed to close job posting: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSubmit = async (values: JobPosting) => {
    setIsLoading(true);
    try {
      const postData: Partial<JobPosting> = {
        id: isNew ? 0 : Number(postingId),
        jobPositionId: Number(values.jobPositionId),
        jobPostingStatusId: 3,
        jobPostingTitle: values.jobPostingTitle,
        jobPostingDescription: values.jobPostingDescription,
        jobPostingRequirements: values.jobPostingRequirements,
        minimumExperienceInYears: values.minimumExperienceInYears,
        jobLocations: Array.isArray(values.jobLocations)
          ? values.jobLocations
          : typeof values.jobLocations === 'string'
            ? (values.jobLocations as string)
                .split(',')
                .map((loc) => loc.trim())
                .filter(Boolean)
            : [],
        shouldKeepOpenUntilStopped: values.shouldKeepOpenUntilStopped,
        createdById: 2,
        lastModifiedById: 2,
      };

      console.log(postData, 'Post Data for Job Posting');

      if (isNew) {
        const created = await createJobPosting(postData);
        if (created && created?.id) {
          navigate(`/careers/postings/${created.id}`, { replace: true });
        }
        showToast('Job Posting Successfully Created', 'success');
      } else {
        await updateJobPosting(postData);
        showToast('Job Posting Successfully Updated', 'success');
      }
      await fetchInitialData();
      setEdit((prev) => ({ ...prev, isEdit: false, isRead: true }));
    } catch (error) {
      showToast(`Failed to update career listing: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainContent
      pageTitle={`Job Posting ${!notFound ? `| ${edit?.data?.id || 'New'}` : 'Invalid ID'}`}
      buttons={
        <>
          {edit?.data?.jobPostingStatusId !== 1 && (
            <Button variant="contained" color="info" onClick={handlePublish}>
              Publish
            </Button>
          )}
          {edit?.data?.jobPostingStatusId !== 2 && (
            <Button variant="contained" color="error" onClick={handleCloseJob}>
              Close Job
            </Button>
          )}
        </>
      }
    >
      <Loader loading={isLoading} />
      <Formik<JobPosting>
        initialValues={initialJobPosting(edit?.data as JobPosting)}
        onSubmit={handleFormSubmit}
        enableReinitialize
      >
        {() => (
          <>
            <CardWrapper>
              <JobPostingsForm
                readMode={Boolean(edit?.isRead)}
                departmentOptions={departmentOptions}
                jobPositionTypeOptions={jobPositionTypeOptions}
              />
            </CardWrapper>
            <FormAction
              handleCancel={handleCancel}
              handleEditAction={handleEditMode}
              readMode={Boolean(edit?.isRead)}
              isLoading={isLoading}
              handleDeleteAction={handleDelete}
            />
          </>
        )}
      </Formik>
    </MainContent>
  );
};

export default JobPostingsDetailsContainer;
