import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';

import { useState } from 'react';
import { useFormikContext } from 'formik';

import { Button } from '@mui/material';

import { IconEdit, IconTrash } from 'src/components/common/icons';
import { ConfirmDialog } from 'src/components/common/confirm-dialog/ConfirmDialog';

import { ButtonRoot, FormActionRoot, FormActionWrapper } from './FormAction.style';

export interface FormActionProps {
  isLoading?: boolean;
  handleEditAction?: () => void;
  handleCancel: () => void;
  readMode?: boolean;
  handleDeleteAction?: () => void;
}

const FormAction = ({
  handleEditAction,
  handleCancel,
  readMode,
  isLoading,
  handleDeleteAction,
}: FormActionProps) => {
  const { resetForm, dirty, handleSubmit } = useFormikContext<QuoteRequest>();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const handleResetForm = () => {
    handleCancel();
    resetForm();
  };

  const handleOpenDeleteDialog = () => setOpenDeleteDialog(true);
  const handleCloseDeleteDialog = () => setOpenDeleteDialog(false);

  const handleConfirmDelete = () => {
    setOpenDeleteDialog(false);
    if (handleDeleteAction) handleDeleteAction();
  };

  return (
    <FormActionRoot>
      <FormActionWrapper alignItems="center" justifyContent="flex-end" direction="row">
        <ButtonRoot>
          {readMode ? (
            <>
              <Button
                startIcon={<IconTrash style={{ width: 16 }} />}
                variant="outlined"
                color="error"
                onClick={handleOpenDeleteDialog}
                disabled={isLoading}
              >
                Delete
              </Button>
              <Button
                startIcon={<IconEdit style={{ width: 16 }} />}
                variant="contained"
                onClick={handleEditAction}
              >
                Edit
              </Button>
            </>
          ) : (
            <>
              <Button variant="outlined" color="error" onClick={handleResetForm}>
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleSubmit()}
                disabled={isLoading || !dirty}
              >
                Save
              </Button>
            </>
          )}
        </ButtonRoot>
      </FormActionWrapper>

      <ConfirmDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        title="Delete Job Posting"
        content="Are you sure you want to delete this job posting? This action cannot be undone."
        action={
          <Button color="error" variant="contained" onClick={handleConfirmDelete} autoFocus>
            Delete
          </Button>
        }
      />
    </FormActionRoot>
  );
};

export default FormAction;
