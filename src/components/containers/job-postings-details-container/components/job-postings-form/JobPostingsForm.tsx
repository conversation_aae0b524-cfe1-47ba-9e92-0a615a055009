import type { ddValue } from 'src/shared/types/ddValue';
import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';

import { useFormikContext } from 'formik';

import { Typography } from '@mui/material';

import CustomSelect from 'src/components/common/forms/select/Select';
import { RichTextEditor } from 'src/components/common/forms/rich-text-editor';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid, FormWrapper, FormGridRoot } from './JobPostingsForm.style';

interface JobPostingsFormProps {
  readMode: boolean;
  departmentOptions: ddValue[];
  jobPositionTypeOptions: ddValue[];
}

const JobPostingsForm = ({
  readMode,
  departmentOptions,
  jobPositionTypeOptions,
}: JobPostingsFormProps) => {
  const { values, handleChange, handleBlur, errors, touched } = useFormikContext<JobPosting>();

  return (
    <FormWrapper>
      <FormGridRoot>
        <FormGrid>
          <CustomSelect
            id="jobPosition.departmentId"
            name="jobPosition.departmentId"
            label="Department"
            options={departmentOptions}
            value={
              departmentOptions
                .find((option) => option.id === values.jobPosition?.departmentId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(
              (errors.jobPosition as any)?.departmentId &&
                (touched.jobPosition as any)?.departmentId
            )}
            helperText={
              (touched.jobPosition as any)?.departmentId &&
              (errors.jobPosition as any)?.departmentId
                ? (errors.jobPosition as any).departmentId
                : ''
            }
            required
          />
        </FormGrid>
        <FormGrid>
          <CustomTextField
            id="jobPostingTitle"
            name="jobPostingTitle"
            label="Position Title"
            variant="outlined"
            type="text"
            value={values.jobPostingTitle ?? ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.jobPostingTitle && touched.jobPostingTitle)}
            helperText={
              touched.jobPostingTitle && errors.jobPostingTitle ? errors.jobPostingTitle : ''
            }
            required
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="jobPosition.jobPositionTypeId"
            name="jobPosition.jobPositionTypeId"
            label="Position Type"
            options={jobPositionTypeOptions}
            value={
              jobPositionTypeOptions
                .find((option) => option.id === values.jobPosition?.jobPositionTypeId)
                ?.id?.toString() ?? ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(
              (errors.jobPosition as any)?.jobPositionTypeId &&
                (touched.jobPosition as any)?.jobPositionTypeId
            )}
            helperText={
              (touched.jobPosition as any)?.jobPositionTypeId &&
              (errors.jobPosition as any)?.jobPositionTypeId
                ? (errors.jobPosition as any).jobPositionTypeId
                : ''
            }
            required
          />
        </FormGrid>

        <FormGrid>
          <CustomTextField
            id="minimumExperienceInYears"
            name="minimumExperienceInYears"
            label="Minimum Experience (Years)"
            type="number"
            variant="outlined"
            value={values.minimumExperienceInYears}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            endAdornment={<Typography variant="body2">Years</Typography>}
            error={Boolean(errors.minimumExperienceInYears && touched.minimumExperienceInYears)}
            helperText={touched.minimumExperienceInYears ? errors.minimumExperienceInYears : ''}
          />
        </FormGrid>
        <FormGrid>
          <CustomTextField
            id="jobLocations"
            name="jobLocations"
            label="Job Locations (comma separated)"
            type="text"
            variant="outlined"
            value={
              Array.isArray(values.jobLocations)
                ? values.jobLocations.join(', ')
                : values.jobLocations || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.jobLocations && touched.jobLocations)}
            helperText={
              touched.jobLocations && typeof errors.jobLocations === 'string'
                ? errors.jobLocations
                : ''
            }
          />
        </FormGrid>

        <FormGrid fullWidth>
          <RichTextEditor
            id="jobPostingDescription"
            name="jobPostingDescription"
            label="Job Description"
            placeholder="This field is required (min 10 characters)..."
            value={values.jobPostingDescription ?? ''}
            error={touched.jobPostingDescription && !!errors.jobPostingDescription}
            handleChange={handleChange}
            handleBlur={handleBlur}
            required
          />
        </FormGrid>
        <FormGrid fullWidth>
          <RichTextEditor
            id="jobPostingRequirements"
            name="jobPostingRequirements"
            label="Job Requirements"
            placeholder="This field is required (min 10 characters)..."
            value={values.jobPostingRequirements ?? ''}
            error={touched.jobPostingRequirements && !!errors.jobPostingRequirements}
            handleChange={handleChange}
            handleBlur={handleBlur}
            required
          />
        </FormGrid>
      </FormGridRoot>
    </FormWrapper>
  );
};

export default JobPostingsForm;
