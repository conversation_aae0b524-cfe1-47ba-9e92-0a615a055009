import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { JobApplication } from 'src/shared/services/job-applications/job-applications.type';

import { Formik } from 'formik';
import { useParams } from 'react-router-dom';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { appendZeroes } from 'src/utils/helper';
import { initialJobApplication } from 'src/utils/validation/careers/job-application/job-application.values';

import { useToast } from 'src/providers/ToastProvider';
import { getUsers } from 'src/shared/services/admin/users/users.service';
import {
  updateJobApplication,
  getJobApplicationById,
  getJobApplicationStatusOptions,
} from 'src/shared/services/job-applications/job-applications.service';

import Loader from 'src/components/common/loader/loader/Loader';
import LogDialog from 'src/components/common/log-dialog/LogDialog';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';
import JobApplicationDetails from 'src/components/containers/job-application-details/components/job-application-details/JobApplicationDetails';

import FormAction from './components/form-actions/FormAction';
import { CardWrapper } from './JobApplicationDetailsContainer.style';

const JobApplicationDetailsContainer = () => {
  const { applicationId } = useParams<{ applicationId: string }>();
  const { showToast } = useToast();

  const [edit, setEdit] = useState<EditMode<JobApplication>>({} as EditMode<JobApplication>);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [openLogsModal, setOpenLogsModal] = useState<boolean>(false);

  const [jobApplicationStatusOptions, setJobApplicationStatusOptions] = useState<ddValue[]>([]);

  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);
  const [adminOptions, setAdminOptions] = useState<AdminUser[]>([]);

  const selectAdminOptions: ddValue[] = useMemo(
    () =>
      adminOptions.map((admin) => ({
        id: admin.id,
        name: admin.fullName || `${admin.firstName} ${admin.lastName}`,
        isDisabled: false,
      })),
    [adminOptions]
  );

  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true);

      const [users, jobApplicationData, statusData] = await Promise.all([
        getUsers(),
        getJobApplicationById(Number(applicationId)),
        getJobApplicationStatusOptions(),
      ]);

      if (Array.isArray(users)) {
        setAdminOptions(users);
      }

      setEdit((prev) => ({ ...prev, data: jobApplicationData }));
      setSelectedAdmin(jobApplicationData?.assignedAdmin || null);

      setJobApplicationStatusOptions(statusData);

      if (jobApplicationData.assignedAdminId && Array.isArray(users)) {
        const admin = users.find((user) => user?.id === jobApplicationData.assignedAdminId);
        if (admin) {
          setSelectedAdmin(admin);
        }
      }
    } catch (error) {
      showToast('Error fetching initial data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [applicationId, showToast]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  const handleOpenLogs = () => {
    setOpenLogsModal(true);
  };

  const handleCloseLogs = () => {
    setOpenLogsModal(false);
  };

  const handleFormSubmit = useCallback(
    async (values: JobApplication) => {
      try {
        setIsLoading(true);
        await updateJobApplication(values);
        showToast('Job application updated successfully', 'success');
        fetchInitialData();
      } catch (error: any) {
        showToast(error?.message || 'Failed to update job application', 'error');
      } finally {
        setIsLoading(false);
      }
    },
    [fetchInitialData, showToast]
  );

  return (
    <MainContent pageTitle={`Job Application | ${appendZeroes(Number(applicationId), 4)}`}>
      <Loader loading={isLoading} />
      <Formik
        initialValues={initialJobApplication(edit?.data as JobApplication) as JobApplication}
        onSubmit={handleFormSubmit}
        enableReinitialize
      >
        {() => (
          <>
            <CardWrapper>
              <JobApplicationDetails
                jobApplicationStatusOptions={jobApplicationStatusOptions}
                selectedAdmin={selectedAdmin}
                adminOptions={selectAdminOptions}
                jobApplication={edit.data as JobApplication}
                departmentOption={{} as ddValue}
              />

              <LogDialog
                open={openLogsModal}
                onClose={handleCloseLogs}
                id={Number(applicationId)}
                tableName=""
                columnName=""
              />
            </CardWrapper>
            <FormAction handleOpen={handleOpenLogs} />
          </>
        )}
      </Formik>
    </MainContent>
  );
};

export default JobApplicationDetailsContainer;
