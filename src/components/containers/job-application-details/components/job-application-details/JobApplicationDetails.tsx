import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { JobApplication } from 'src/shared/services/job-applications/job-applications.type';

import { useFormikContext } from 'formik';
import { useMemo, useCallback } from 'react';

import { Button, Tooltip } from '@mui/material';

import { formatDate } from 'src/utils/date';
import { copyToClipboard } from 'src/utils/copyToClipboard';

import { useToast } from 'src/providers/ToastProvider';
import { downloadFile } from 'src/shared/services/files/files.service';

import IconCopy from 'src/components/common/icons/IconCopy';
import IconCheck from 'src/components/common/icons/IconCheck';
import CustomSelect from 'src/components/common/forms/select/Select';
import { IconClose, IconDownload } from 'src/components/common/icons';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  FormGrid,
  FormWrapper,
  CopyWrapper,
  FormGridRoot,
  StyledSubtitle,
  DownloadWrapper,
  StyledCaptionText,
  StyledSectionTitle,
  StyledApplicantName,
  DownloadLoadingButton,
  StyledCheckButtonWrapper,
} from './JobApplicationDetails.style';

interface JobApplicationDetailsProps {
  selectedAdmin: AdminUser | null;
  adminOptions: ddValue[];
  jobApplicationStatusOptions: ddValue[];
  departmentOption: ddValue;
  jobApplication: JobApplication;
}

const JobApplicationDetails = ({
  selectedAdmin,
  adminOptions,
  jobApplicationStatusOptions,
  departmentOption,
  jobApplication,
}: JobApplicationDetailsProps) => {
  const { values, handleChange, handleBlur, errors, touched } = useFormikContext<JobApplication>();
  const { showToast } = useToast();

  console.log(values?.assignedAdminId, 'assignedAdminId');

  const formattedAddress = useMemo(
    () =>
      `${jobApplication?.addressLine1}, ${jobApplication?.addressLine2}, ${jobApplication?.city}, ${jobApplication?.state} ${jobApplication?.zipcode}`,
    [
      jobApplication?.addressLine1,
      jobApplication?.addressLine2,
      jobApplication?.city,
      jobApplication?.state,
      jobApplication?.zipcode,
    ]
  );

  const handleCopyClick = useCallback(
    (text: string) => {
      copyToClipboard(text);
      showToast(`Copied to Clipboard`, 'success');
    },
    [showToast]
  );

  const handleDownloadResume = useCallback(async () => {
    if (!jobApplication?.resumeDocumentUrl?.length) {
      showToast('No resume available to download', 'error');
      return;
    }
    try {
      await downloadFile(jobApplication.resumeDocumentUrl);
      showToast('Resume downloaded', 'success');
    } catch (error: any) {
      showToast(error?.message || 'Failed to download resume', 'error');
    }
  }, [jobApplication?.resumeDocumentUrl, showToast]);

  return (
    <FormWrapper>
      <FormGridRoot>
        <FormGrid>
          <StyledApplicantName variant="h4">{jobApplication?.name}</StyledApplicantName>
          <CopyWrapper onClick={() => handleCopyClick(jobApplication?.emailId)}>
            <StyledCaptionText variant="body1">{jobApplication?.emailId}</StyledCaptionText>
            <IconCopy style={{ width: 18 }} />
          </CopyWrapper>

          <CopyWrapper onClick={() => handleCopyClick(jobApplication?.emailId)}>
            <StyledCaptionText variant="body1">+91 {jobApplication?.phone}</StyledCaptionText>
            <IconCopy style={{ width: 18 }} />
          </CopyWrapper>

          <StyledCaptionText variant="body1">{formattedAddress}</StyledCaptionText>
        </FormGrid>
        <FormGrid>
          <DownloadWrapper>
            <DownloadLoadingButton
              variant="contained"
              startIcon={<IconDownload style={{ width: 18 }} />}
              color="primary"
              onClick={handleDownloadResume}
            >
              Download Resume
            </DownloadLoadingButton>
          </DownloadWrapper>
        </FormGrid>

        <FormGrid fullWidth>
          <StyledCheckButtonWrapper>
            <Tooltip title="Is the applicant authorized to work in India?" placement="top" arrow>
              <Button
                variant={jobApplication?.isAuthorizedToWorkInIndia ? 'soft' : 'soft'}
                startIcon={
                  jobApplication?.isAuthorizedToWorkInIndia ? (
                    <IconCheck style={{ width: 18 }} />
                  ) : (
                    <IconClose style={{ width: 18 }} />
                  )
                }
                color={jobApplication?.isAuthorizedToWorkInIndia ? 'success' : 'error'}
                disableRipple
              >
                {jobApplication?.isAuthorizedToWorkInIndia
                  ? 'Authorized to Work in India'
                  : 'Not Authorized to Work in India'}
              </Button>
            </Tooltip>

            <Tooltip title="Does the applicant require visa sponsorship?" placement="top" arrow>
              <Button
                variant={jobApplication?.requireVisaSponsorship ? 'soft' : 'soft'}
                startIcon={
                  jobApplication?.requireVisaSponsorship ? (
                    <IconCheck style={{ width: 18 }} />
                  ) : (
                    <IconClose style={{ width: 18 }} />
                  )
                }
                color={jobApplication?.requireVisaSponsorship ? 'success' : 'error'}
                disableRipple
              >
                {jobApplication?.requireVisaSponsorship
                  ? 'Requires Visa Sponsorship'
                  : 'Does Not Require Visa Sponsorship'}
              </Button>
            </Tooltip>

            <Tooltip
              title="Has the applicant accepted the terms and conditions?"
              placement="top"
              arrow
            >
              <Button
                variant={jobApplication?.isTCAccepted ? 'soft' : 'soft'}
                startIcon={
                  jobApplication?.isTCAccepted ? (
                    <IconCheck style={{ width: 18 }} />
                  ) : (
                    <IconClose style={{ width: 18 }} />
                  )
                }
                color={jobApplication?.isTCAccepted ? 'success' : 'error'}
                disableRipple
              >
                {jobApplication?.isTCAccepted ? 'Terms Accepted' : 'Terms Not Accepted'}
              </Button>
            </Tooltip>
          </StyledCheckButtonWrapper>
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot columns="repeat(4, 1fr)" gap={4}>
        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Position Title
          </StyledCaptionText>
          <StyledSubtitle variant="h6">{jobApplication?.jobPosition?.positionTitle}</StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Department
          </StyledCaptionText>
          <StyledSubtitle variant="h6">
            {departmentOption?.name || jobApplication?.jobPosition?.department?.name || '---'}
          </StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Available From
          </StyledCaptionText>
          <StyledSubtitle variant="h6">
            {formatDate(jobApplication?.availableFromDate) || '---'}
          </StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Total Relevant Experience (in years)
          </StyledCaptionText>
          <StyledSubtitle variant="h6">
            {jobApplication?.totalRelevantExperienceInYears != null
              ? `${jobApplication.totalRelevantExperienceInYears} Years`
              : '---'}
          </StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Previous Employer
          </StyledCaptionText>
          <StyledSubtitle variant="h6">{jobApplication?.previousEmployer || '---'}</StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Previous Job Positions
          </StyledCaptionText>
          <StyledSubtitle variant="h6">
            {jobApplication?.previousJobPosition || '---'}
          </StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Previous Employment In Years
          </StyledCaptionText>
          <StyledSubtitle variant="h6">
            {jobApplication?.previousExploymentInYears || '---'}
          </StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Job Location
          </StyledCaptionText>
          <StyledSubtitle variant="h6">{jobApplication?.jobLocation || '---'}</StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Preferred Contact Method
          </StyledCaptionText>
          <StyledSubtitle variant="h6">
            {jobApplication?.preferredContactMethod?.name || '---'}
          </StyledSubtitle>
        </FormGrid>

        <FormGrid>
          <StyledCaptionText variant="body2" lineHeight={1}>
            Reference
          </StyledCaptionText>
          <StyledSubtitle variant="h6">{jobApplication?.reference || '---'}</StyledSubtitle>
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid fullWidth>
          <StyledSectionTitle variant="h6">Admin Action</StyledSectionTitle>
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="assignedAdminId"
            name="assignedAdminId"
            label="Assign Admin"
            placeholder="Select admin"
            options={adminOptions}
            value={
              (adminOptions
                ?.find((option) => option.id === values.assignedAdminId)
                ?.id?.toString() ||
                selectedAdmin?.id.toString()) ??
              ''
            }
            handleChange={handleChange}
            error={Boolean(errors.assignedAdminId && touched.assignedAdminId)}
            helperText={touched.assignedAdminId ? (errors.assignedAdminId as string) : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="jobApplicationStatusId"
            name="jobApplicationStatusId"
            label="Status"
            options={jobApplicationStatusOptions}
            value={
              jobApplicationStatusOptions
                ?.find((option) => option?.id === values.jobApplicationStatusId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={!values?.assignedAdminId}
            error={Boolean(errors.jobApplicationStatusId && touched.jobApplicationStatusId)}
            helperText={touched.jobApplicationStatusId ? errors.jobApplicationStatusId : ''}
          />
        </FormGrid>

        <FormGrid fullWidth>
          <CustomTextField
            id="adminActionRemark"
            name="adminActionRemark"
            label="Remark"
            type="text"
            variant="outlined"
            multiline
            minRows={5}
            maxRows={5}
            value={values.adminActionRemark ?? ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            error={Boolean(errors.adminActionRemark && touched.adminActionRemark)}
            helperText={touched.adminActionRemark ? errors.adminActionRemark : ''}
          />
        </FormGrid>
      </FormGridRoot>
    </FormWrapper>
  );
};

export default JobApplicationDetails;
