import { LoadingButton } from '@mui/lab';
import { styled } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';

export const FormWrapper = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  gridColumn: '1 / -1',
}));

export const FormGridRoot = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'columns' && prop !== 'gap',
})<{ columns?: string; gap?: number }>(({ theme, columns, gap }) => ({
  display: 'grid',
  gridTemplateColumns: columns || 'repeat(2, 1fr)',
  gap: theme.spacing(typeof gap === 'number' ? gap : 2),
  columnGap: theme.spacing(4),
  width: '100%',

  '&:not(:last-child)': {
    marginBottom: theme.spacing(6),
  },

  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
  },

  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: '1fr',
  },
}));

export const FormGrid = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'fullWidth',
})<{ fullWidth?: boolean }>(({ fullWidth, theme }) => ({
  gridColumn: fullWidth ? '1 / -1' : undefined,
}));

export const StyledSectionTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightSemiBold,
  paddingBlock: theme.spacing(1),
}));

export const CopyWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  cursor: 'pointer',
  gap: theme.spacing(0.5),
}));

export const StyledCheckButtonWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

export const StyledApplicantName = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightRegular,
}));

export const StyledSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightRegular,
}));

interface StyledCaptionTextProps {
  lineHeight?: number;
}

export const StyledCaptionText = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'lineHeight',
})<StyledCaptionTextProps>(({ theme, lineHeight = 1.5 }) => ({
  color: theme.palette.grey[600],
  fontWeight: theme.typography.fontWeightMedium,
  lineHeight,
}));

export const DownloadWrapper = styled('div')(() => ({
  textAlign: 'right',
}));

export const DownloadLoadingButton = styled(LoadingButton)(({ theme }) => ({
  ':disabled': {
    backgroundColor: theme.palette.grey[200],
    color: theme.palette.grey[500],
  },
}));
