import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  StyledFields,
  StyledFiltersView,
  StyledToolbarLeft,
  StyledToolbarRight,
  StyledToolbarWrapper,
  StyledToolbarFormControl,
  StyledToolbarSearchWrapper,
} from './JobPositionsToolbar.style';

export interface IJobPositionsToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onPositionTypeChange: (type: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  selectedPositionType: ddValue;
  jobPositionStatusOptions: ddValue[];
  jobPositionTypeOptions: ddValue[];
  totalCount?: number;
}

const JobPositionsToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  onPositionTypeChange,
  selectedStatus,
  selectedPositionType,
  jobPositionStatusOptions,
  jobPositionTypeOptions,
  onFilterChange,
  totalCount = 0,
}: IJobPositionsToolbarProps) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = jobPositionStatusOptions.find(({ id }) => id.toString() === typeId.toString());
    onStatusChange(status ?? ({} as ddValue));
    onFilterChange({
      field: 'jobPositionStatusId',
      value: status ? status.id.toString() : '',
    });
  };

  const handlePositionTypeChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const type = jobPositionTypeOptions.find(({ id }) => id.toString() === typeId.toString());
    onPositionTypeChange(type ?? ({} as ddValue));
    onFilterChange({
      field: 'jobPositionTypeId',
      value: type ? type.id.toString() : '',
    });
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    onFilterChange(
      {
        field: 'jobPositionStatusId',
        value: '',
      },
      true
    );
  };
  const handleClearType = () => {
    onPositionTypeChange({} as ddValue);
    onFilterChange(
      {
        field: 'jobPositionTypeId',
        value: '',
      },
      true
    );
  };

  const canReset =
    Boolean(searchKey) || Boolean(selectedStatus?.id) || Boolean(selectedPositionType?.id);

  return (
    <StyledToolbarWrapper>
      <StyledFields>
        <StyledToolbarLeft>
          <StyledToolbarSearchWrapper>
            <SearchBar
              type="text"
              value={searchKey}
              onChange={handleSearch}
              placeholder="Search..."
            />
          </StyledToolbarSearchWrapper>
        </StyledToolbarLeft>
        <StyledToolbarRight>
          <StyledToolbarFormControl>
            <CustomSelect
              value={selectedStatus?.id?.toString() ?? ''}
              handleChange={handleStatusChange}
              id="job-position-status"
              label=""
              name="status"
              coreLabel="Status"
              options={jobPositionStatusOptions}
              disabled={jobPositionStatusOptions.length === 0}
              required={false}
            />
          </StyledToolbarFormControl>
          <StyledToolbarFormControl>
            <CustomSelect
              value={selectedPositionType?.id?.toString() ?? ''}
              handleChange={handlePositionTypeChange}
              id="job-position-type"
              label=""
              name="type"
              coreLabel="Type"
              options={jobPositionTypeOptions}
              disabled={jobPositionTypeOptions.length === 0}
              required={false}
            />
          </StyledToolbarFormControl>
        </StyledToolbarRight>
      </StyledFields>
      {canReset && (
        <StyledFiltersView>
          <FiltersResult totalResults={totalCount}>
            <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
              <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
            </FiltersBlock>
            <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
              <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
            </FiltersBlock>
            <FiltersBlock label="Position Type:" isShow={Boolean(selectedPositionType?.id)}>
              <Chip {...chipProps} label={selectedPositionType?.name} onDelete={handleClearType} />
            </FiltersBlock>
          </FiltersResult>
        </StyledFiltersView>
      )}
    </StyledToolbarWrapper>
  );
};

export default JobPositionsToolbar;
