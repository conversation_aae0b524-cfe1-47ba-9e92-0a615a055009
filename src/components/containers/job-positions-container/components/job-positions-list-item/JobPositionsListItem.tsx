import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { Tooltip, TableRow, TableCell, IconButton } from '@mui/material';

import { formatTime, formatDate } from 'src/utils/date';

import { IconShare } from 'src/components/common/icons';
import StatusChip from 'src/components/common/status-chip/StatusChip';

import {
  DateTimeRoot,
  StyledTextWrapper,
  DateFormatWrapper,
  TimeFormatWrapper,
  UserNameLinkWrapper,
} from './JobPositionsListItem.style';

interface IJobPositionsListItemProps {
  request: JobPosition;
}

export const JobPositionsListItem = ({ request }: IJobPositionsListItemProps) => {
  const navigate = useNavigate();

  const formattedDate = formatDate(request?.createdDate ?? '');
  const formattedTime = formatTime(request?.createdDate ?? '');

  const handleClick = useCallback(() => {
    navigate(`/careers/positions/${request.id}`);
  }, [navigate, request.id]);

  const handleEditRow = useCallback(
    (e: { stopPropagation: () => void }) => {
      e.stopPropagation();
      handleClick();
    },
    [handleClick]
  );

  return (
    <TableRow hover onClick={handleClick}>
      <TableCell>
        <UserNameLinkWrapper color="inherit">{request.jobCode}</UserNameLinkWrapper>
      </TableCell>
      <TableCell>{request.positionTitle ?? '---'}</TableCell>
      <TableCell>
        <Tooltip title={request.jobDescription} placement="top">
          <StyledTextWrapper>{request.jobDescription ?? '---'}</StyledTextWrapper>
        </Tooltip>
      </TableCell>
      <TableCell>{request.department?.name ?? '---'}</TableCell>
      <TableCell>{request.jobPositionType?.name ?? '---'}</TableCell>
      <TableCell>
        <DateTimeRoot>
          <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
          <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
        </DateTimeRoot>
      </TableCell>
      <TableCell>
        <StatusChip
          variant="soft"
          label={request.jobPositionStatus?.name ?? '---'}
          color={request.jobPositionStatus?.colorCode ?? '#000000'}
        />
      </TableCell>
      <TableCell align="right">
        <IconButton onClick={handleEditRow}>
          <IconShare style={{ width: 24 }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};
