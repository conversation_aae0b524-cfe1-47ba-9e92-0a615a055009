import type { SelectChangeEvent } from '@mui/material';
import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

import { useNavigate } from 'react-router';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { Button } from '@mui/material';

import { useDebounce } from 'src/hooks/use-debounce';

import {
  searchJobPositions,
  getJobPositionTypeOptions,
  getJobPositionStatusOptions,
} from 'src/shared/services/job-positions/job-positions.service';

import TableView from 'src/components/common/table/view/TableView';
import { IconPlus, IconRefresh } from 'src/components/common/icons';
import NoDataView from 'src/components/common/error/no-data-view/NoDataView';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import JobPositionsToolbar from './components/job-positions-toolbar/JobPositionsToolbar';
import { JobPositionsListItem } from './components/job-positions-list-item/JobPositionsListItem';
import { JobPositionsWrapper, JobPositionsOutlinedIconButton } from './JobPositionsContainer.style';

const TABLE_HEAD = [
  { id: 'code', label: 'Code' },
  { id: 'positionTitle', label: 'Title', width: 400 },
  { id: 'description', label: 'Description', width: 400 },
  { id: 'department', label: 'Department', width: 200 },
  { id: 'type', label: 'Type', width: 150 },
  { id: 'date', label: 'Created date', width: 300 },
  { id: 'status', label: 'Status', width: 200 },
  { id: 'action', label: '' },
];

export const JobPositionsContainer = () => {
  const navigate = useNavigate();

  const [tableData, setTableData] = useState<JobPosition[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [jobPositionStatusOptions, setjobPositionStatusOptions] = useState<ddValue[]>([]);
  const [jobPositionTypeOptions, setjobPositionTypeOptions] = useState<ddValue[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);
  const [selectedPositionType, setSelectedPositionType] = useState<ddValue>({} as ddValue);

  const debouncedSearchKey = useDebounce(searchKey, 500);

  const getJobPositionsData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchJobPositions(criteria);
      setTableData(searchResult);
      setTotalCount(count);
      setFetchError(null);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch job positions');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getJobPositionsData();
  }, [getJobPositionsData]);

  useEffect(() => {
    let isMounted = true;
    const loadOptions = async () => {
      try {
        const [statusData, typeData] = await Promise.all([
          getJobPositionStatusOptions(),
          getJobPositionTypeOptions(),
        ]);
        if (isMounted) {
          setjobPositionStatusOptions(statusData);
          setjobPositionTypeOptions(typeData);
        }
      } catch (error: any) {
        setFetchError(error.message || 'Failed to load options');
      }
    };
    loadOptions();
    return () => {
      isMounted = false;
    };
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handlePositionTypeUpdate = useCallback((type: ddValue): void => {
    setSelectedPositionType(type);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
    setCurrentPage(1);
  }, []);

  const handleAddNewPositionClick = useCallback(() => {
    navigate(`/careers/positions/new`);
  }, [navigate]);

  const handleRefreshData = useCallback(async () => {
    await getJobPositionsData();
  }, [getJobPositionsData]);

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <MainContent
      pageTitle="Job Positions"
      buttons={
        <>
          <JobPositionsOutlinedIconButton onClick={handleRefreshData}>
            <IconRefresh style={{ width: 18 }} />
          </JobPositionsOutlinedIconButton>
          <Button
            variant="contained"
            color="primary"
            startIcon={<IconPlus style={{ width: 18 }} />}
            onClick={handleAddNewPositionClick}
          >
            Add
          </Button>
        </>
      }
    >
      <JobPositionsWrapper>
        <JobPositionsToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          jobPositionStatusOptions={jobPositionStatusOptions}
          jobPositionTypeOptions={jobPositionTypeOptions}
          selectedStatus={selectedStatus}
          selectedPositionType={selectedPositionType}
          onStatusChange={handleStatusUpdate}
          onPositionTypeChange={handlePositionTypeUpdate}
          onFilterChange={handleFilterChange}
          totalCount={totalCount}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              currentPage={currentPage}
              onNext={() => setCurrentPage((prev) => prev + 1)}
              onPrev={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
              totalCount={totalCount}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData?.length > 0 ? (
            memoizedTableData.map((row) => <JobPositionsListItem key={row.id} request={row} />)
          ) : (
            <NoDataView />
          )}
        </TableView>
      </JobPositionsWrapper>
    </MainContent>
  );
};

export default JobPositionsContainer;
