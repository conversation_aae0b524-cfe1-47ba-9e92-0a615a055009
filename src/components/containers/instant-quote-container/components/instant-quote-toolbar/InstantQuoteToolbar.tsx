import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { useMemo } from 'react';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';
import { useAuth } from 'src/providers/AuthProvider';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  InstantQuoteFields,
  InstantQuoteToolbarLeft,
  InstantQuoteFiltersView,
  InstantQuoteToolbarRight,
  InstantQuoteToolbarWrapper,
  InstantQuoteToolbarFormControl,
  InstantQuoteToolbarSearchWrapper,
} from './InstantQuoteToolbar.style';

interface InstantQuoteToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onAssignedAdminChange: (admin: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  selectedAssignedAdmin: ddValue;
  instantRequestStatusOptions: ddValue[];
  assignedAdminOptions: ddValue[];
  totalCount?: number;
}

const InstantQuoteToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  onAssignedAdminChange,
  selectedStatus,
  selectedAssignedAdmin,
  instantRequestStatusOptions,
  assignedAdminOptions,
  onFilterChange,
  totalCount = 0,
}: InstantQuoteToolbarProps) => {
  const { userName } = useAuth();

  const preparedAdminOptions = useMemo(
    () =>
      assignedAdminOptions
        .map((admin) => {
          if (admin.name === userName) {
            return { ...admin, name: 'Assigned to Me' };
          }
          return admin;
        })
        .sort((a, b) => {
          if (a.name === 'Assigned to Me') return -1;
          if (b.name === 'Assigned to Me') return 1;
          return 0;
        }),
    [assignedAdminOptions, userName]
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = instantRequestStatusOptions.find(
      ({ id }) => id.toString() === typeId.toString()
    );

    onStatusChange(status ?? ({} as ddValue));
    const filterOption: FilterOption = {
      field: 'statusId',
      value: status?.id.toString(),
    };

    onFilterChange(filterOption);
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    const filterOption: FilterOption = {
      field: 'statusId',
      value: '',
    };

    onFilterChange(filterOption, true);
  };

  const handleAssignedAdminChange = (event: TInputChangeEvent) => {
    const adminId = event.target.value;
    const admin = assignedAdminOptions.find(({ id }) => id.toString() === adminId.toString());

    onAssignedAdminChange(admin || ({} as ddValue));
    onFilterChange(
      {
        field: 'assignedAdminId',
        value: admin?.id.toString() || '',
      },
      !admin?.id
    );
  };

  const handleClearAssignedAdmin = () => {
    onAssignedAdminChange({} as ddValue);
    onFilterChange(
      {
        field: 'assignedAdminId',
        value: '',
      },
      true
    );
  };

  const canReset = searchKey || selectedStatus?.id || selectedAssignedAdmin?.id;

  return (
    <InstantQuoteToolbarWrapper>
      <InstantQuoteFields>
        <InstantQuoteToolbarLeft>
          <InstantQuoteToolbarSearchWrapper>
            <SearchBar
              type="text"
              value={searchKey}
              onChange={handleSearch}
              placeholder="Search..."
            />
          </InstantQuoteToolbarSearchWrapper>
        </InstantQuoteToolbarLeft>

        <InstantQuoteToolbarRight>
          <InstantQuoteToolbarFormControl>
            <CustomSelect
              value={selectedStatus?.id?.toString() ?? ''}
              handleChange={handleStatusChange}
              id="instant-quote-status"
              label=""
              name="status"
              coreLabel="Status"
              options={instantRequestStatusOptions}
              disabled={instantRequestStatusOptions.length === 0}
              required={false}
            />
          </InstantQuoteToolbarFormControl>
          <InstantQuoteToolbarFormControl>
            <CustomSelect
              value={selectedAssignedAdmin?.id?.toString() ?? ''}
              handleChange={handleAssignedAdminChange}
              id="instant-quote-assigned-admin"
              label=""
              name="assignedAdmin"
              coreLabel="Assigned to"
              options={preparedAdminOptions}
              disabled={preparedAdminOptions.length === 0}
              required={false}
            />
          </InstantQuoteToolbarFormControl>
        </InstantQuoteToolbarRight>
      </InstantQuoteFields>

      {canReset && (
        <InstantQuoteFiltersView>
          <FiltersResult totalResults={totalCount}>
            <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
              <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
            </FiltersBlock>

            <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
              <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
            </FiltersBlock>

            <FiltersBlock label="Assigned to:" isShow={Boolean(selectedAssignedAdmin?.id)}>
              <Chip
                {...chipProps}
                label={selectedAssignedAdmin?.name}
                onDelete={handleClearAssignedAdmin}
              />
            </FiltersBlock>
          </FiltersResult>
        </InstantQuoteFiltersView>
      )}
    </InstantQuoteToolbarWrapper>
  );
};

export default InstantQuoteToolbar;
