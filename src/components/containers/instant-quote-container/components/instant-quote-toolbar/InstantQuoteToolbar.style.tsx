import { styled } from '@mui/material/styles';

export const InstantQuoteToolbarWrapper = styled('div')(({ theme }) => ({}));

export const InstantQuoteFields = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingBlock: theme.spacing(2.5),
}));

export const InstantQuoteToolbarLeft = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  gap: '1rem',
}));

export const InstantQuoteToolbarRight = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
  minWidth: '420px',
}));

export const InstantQuoteToolbarFormControl = styled('div')(({ theme }) => ({
  flexShrink: 0,
  width: 200,
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

export const InstantQuoteToolbarSearchWrapper = styled('div')(() => ({
  width: '60%',
}));

export const InstantQuoteFiltersView = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));
