import { styled } from '@mui/material/styles';
import { Box, Stack, Typography } from '@mui/material';

export const FormWrapper = styled('div')(({ theme }) => ({
  width: '100%',
  height: '100%',
  gridColumn: '1 / -1',
}));

export const FormGridRoot = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'columns',
})<{ columns?: string }>(({ theme, columns }) => ({
  display: 'grid',
  gridTemplateColumns: columns || 'repeat(2, 1fr)',
  gap: theme.spacing(2),
  columnGap: theme.spacing(4),
  width: '100%',

  '&:not(:last-child)': {
    marginBottom: theme.spacing(4),
  },

  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
  },

  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: '1fr',
  },
}));

export const FormGrid = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'fullWidth',
})<{ fullWidth?: boolean }>(({ fullWidth }) => ({
  gridColumn: fullWidth ? '1 / -1' : undefined,
}));

export const AdminSectionTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[900],
  fontWeight: theme.typography.fontWeightSemiBold,
  marginBottom: theme.spacing(3),
}));

export const DisplayWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: theme.spacing(2),
}));

export const DateTimeRoot = styled(Stack)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  gap: theme.spacing(0.5),
  alignItems: 'center',
}));

export const DateFormatWrapper = styled('span')(({ theme }) => ({
  color: theme.palette.text.secondary,
  minWidth: 'max-content',
  width: '120px',
}));
export const TimeFormatWrapper = styled('span')(({ theme }) => ({
  color: theme.palette.text.disabled,
}));

export const ResponseNoDataView = styled('div')(({ theme }) => ({
  width: '100%',
  textAlign: 'center',
  paddingBlock: theme.spacing(6),
  color: theme.palette.text.secondary,
  border: '1px dashed',
  borderColor: theme.palette.divider,
}));
