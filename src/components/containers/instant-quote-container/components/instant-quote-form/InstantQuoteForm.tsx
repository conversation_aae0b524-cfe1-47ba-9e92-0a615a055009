import type { ddValue, ServiceDDValue } from 'src/shared/types/ddValue';
import type {
  Vehicle,
  InstantEstimation,
  EstimationResponse,
} from 'src/shared/services/instant-quote/instant-quote.type';

import { memo, useState } from 'react';
import { useFormikContext } from 'formik';

import { Box, Tab, Tabs } from '@mui/material';

import { formatDate, formatTime } from 'src/utils/date';

import CustomSelect from 'src/components/common/forms/select/Select';
import VehicleDetails from 'src/components/common/vehicle-details/VehicleDetails';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';
import InstantQuoteRequest from 'src/components/containers/instant-quote-details-container/components/instant-quote-requests/InstantQuoteRequest';
import InstantQuoteResponse from 'src/components/containers/instant-quote-details-container/components/instant-quote-response/InstantQuoteResponse';

import {
  FormGrid,
  FormWrapper,
  FormGridRoot,
  DateTimeRoot,
  DisplayWrapper,
  AdminSectionTitle,
  DateFormatWrapper,
  TimeFormatWrapper,
  ResponseNoDataView,
} from './InstantQuoteForm.style';

interface InstantQuoteFormProps {
  readMode: boolean;
  instantRequestStatusOptions: ddValue[];
  adminOptions: ddValue[];
  response: EstimationResponse;
  requestData: Record<string, any>;
  service: ServiceDDValue;
  createdDate: string;
}

const InstantQuoteForm = ({
  readMode,
  instantRequestStatusOptions,
  adminOptions,
  response,
  requestData,
  service,
  createdDate,
}: InstantQuoteFormProps) => {
  const { values, handleChange, handleBlur, errors, touched } =
    useFormikContext<InstantEstimation>();
  const [tabIndex, setTabIndex] = useState(0);

  const formattedDate = formatDate(createdDate);
  const formattedTime = formatTime(createdDate);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  return (
    <FormWrapper role="presentation">
      <FormGrid fullWidth>
        <DisplayWrapper>
          <AdminSectionTitle variant="h6">{service?.name}</AdminSectionTitle>
          <DateTimeRoot>
            <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
            <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
          </DateTimeRoot>
        </DisplayWrapper>
      </FormGrid>

      <FormGridRoot>
        <FormGrid>
          <VehicleDetails vehicle={response?.estimations[0]?.vehicle as Vehicle} />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid>
          <InstantQuoteRequest requestData={requestData} serviceType={service} />
        </FormGrid>

        <FormGrid>
          {response?.estimations && response?.estimations.length > 0 ? (
            <Box sx={{ width: '100%' }}>
              {response.estimations.length > 1 && (
                <Tabs
                  value={tabIndex}
                  onChange={handleTabChange}
                  sx={{ mb: 2, width: '100%' }}
                  variant="fullWidth"
                >
                  {response.estimations.map((est, idx) => (
                    <Tab key={est.name || idx} label={est?.name || `Estimation ${idx + 1}`} />
                  ))}
                </Tabs>
              )}
              <InstantQuoteResponse responseData={response.estimations[tabIndex]} />
            </Box>
          ) : (
            <ResponseNoDataView>No estimation data available.</ResponseNoDataView>
          )}
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid fullWidth>
          <AdminSectionTitle variant="h6">Admin Action</AdminSectionTitle>
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="assignedAdminId"
            name="assignedAdminId"
            label="Assign Admin"
            placeholder="Select admin"
            options={adminOptions}
            value={
              adminOptions.find((option) => option.id === values.assignedAdminId)?.id?.toString() ||
              ''
            }
            handleChange={handleChange}
            error={Boolean(errors.assignedAdminId && touched.assignedAdminId)}
            helperText={touched.assignedAdminId ? (errors.assignedAdminId as string) : ''}
            disabled={readMode}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="statusId"
            name="statusId"
            label="Status"
            options={instantRequestStatusOptions}
            value={
              instantRequestStatusOptions
                .find((option) => option.id === values.statusId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={!values?.assignedAdmin?.id}
            error={Boolean(errors.statusId && touched.statusId)}
            helperText={touched.statusId ? errors.statusId : ''}
            required
          />
        </FormGrid>

        <FormGrid fullWidth>
          <CustomTextField
            id="adminActionRemark"
            name="adminActionRemark"
            label="Remark"
            type="text"
            variant="outlined"
            value={values.adminActionRemark ?? ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            multiline
            minRows={5}
            maxRows={5}
            error={Boolean(errors.adminActionRemark && touched.adminActionRemark)}
            helperText={touched.adminActionRemark ? errors.adminActionRemark : ''}
          />
        </FormGrid>
      </FormGridRoot>
    </FormWrapper>
  );
};

export default memo(InstantQuoteForm);
