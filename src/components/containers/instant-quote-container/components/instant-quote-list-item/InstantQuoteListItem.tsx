import type { InstantEstimation } from 'src/shared/services/instant-quote/instant-quote.type';

import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { TableRow, TableCell, IconButton } from '@mui/material';

import { appendZeroes } from 'src/utils/helper';
import { formatTime, formatDate } from 'src/utils/date';
import { copyToClipboard } from 'src/utils/copyToClipboard';

import { useToast } from 'src/providers/ToastProvider';

import { IconShare } from 'src/components/common/icons';
import IconCopy from 'src/components/common/icons/IconCopy';
import StatusChip from 'src/components/common/status-chip/StatusChip';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import {
  CopyWrapper,
  DateTimeRoot,
  EmailWrapper,
  DateFormatWrapper,
  TimeFormatWrapper,
  UserNameLinkWrapper,
} from './InstantQuoteListItem.style';

interface InstantQuoteListItemProps {
  request: InstantEstimation;
}

const InstantQuoteListItem = ({ request }: InstantQuoteListItemProps) => {
  const navigate = useNavigate();
  const { showToast } = useToast();

  const formattedDate = formatDate(request?.createdDate ?? '');
  const formattedTime = formatTime(request?.createdDate ?? '');

  const handleRowClick = useCallback(() => {
    navigate(`/instant-quotes/${request.id}`);
  }, [navigate, request.id]);

  const handleCopyClick = (e: React.MouseEvent, text: string) => {
    e.stopPropagation();
    copyToClipboard(text);

    showToast(`Copied to Clipboard`, 'success');
  };

  return (
    <TableRow hover onClick={handleRowClick}>
      <TableCell>{appendZeroes(request.id, 4) ?? '---'}</TableCell>
      <TableCell>
        <UserNameLinkWrapper color="inherit">{request.name}</UserNameLinkWrapper>
      </TableCell>
      <TableCell>
        <CopyWrapper onClick={(e) => handleCopyClick(e, request?.phone?.toString())}>
          <span>{request?.phone ?? '---'}</span>
          <IconCopy style={{ width: 18 }} />
        </CopyWrapper>
      </TableCell>
      <EmailWrapper>
        <CopyWrapper onClick={(e) => handleCopyClick(e, request?.emailId?.toString())}>
          <span>{request?.emailId ?? '---'}</span>
          <IconCopy style={{ width: 18 }} />
        </CopyWrapper>
      </EmailWrapper>
      <TableCell>
        <DateTimeRoot>
          <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
          <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
        </DateTimeRoot>
      </TableCell>
      <TableCell>{request.service?.name ?? '---'}</TableCell>
      <TableCell align="center">
        {request.isResponseGenerated ? (
          <Iconify icon="icon-park-twotone:check-one" color="success.main" width={32} height={32} />
        ) : (
          <Iconify
            icon="iconamoon:sign-times-circle-duotone"
            color="error.main"
            width={32}
            height={32}
          />
        )}
      </TableCell>
      <TableCell>
        <StatusChip
          variant="soft"
          label={request.status?.name ?? '---'}
          color={request.status?.colorCode ?? '#000000'}
        />
      </TableCell>
      <TableCell align="right">
        <IconButton onClick={handleRowClick}>
          <IconShare style={{ width: 24 }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default InstantQuoteListItem;
