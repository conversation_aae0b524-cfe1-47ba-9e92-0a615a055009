import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { InstantEstimation } from 'src/shared/services/instant-quote/instant-quote.type';

import { useMemo, useState, useEffect, useCallback } from 'react';

import { type SelectChangeEvent } from '@mui/material';

import { useDebounce } from 'src/hooks/use-debounce';

import { getUsers } from 'src/shared/services/admin/users/users.service';
import {
  searchInstantRequests,
  getInstantRequestStatusOptions,
} from 'src/shared/services/instant-quote/instant-quote.service';

import TableView from 'src/components/common/table/view/TableView';
import { IconRefresh, IconDownload } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';
import {
  InstantQuoteWrapper,
  InstantQuoteFilledIconButton,
  InstantQuoteOutlinedIconButton,
} from 'src/components/containers/instant-quote-container/InstantQuoteContainer.style';

import InstantQuoteToolbar from './components/instant-quote-toolbar/InstantQuoteToolbar';
import InstantQuoteListItem from './components/instant-quote-list-item/InstantQuoteListItem';

const TABLE_HEAD = [
  { id: 'id', label: 'ID' },
  { id: 'name', label: 'Name', width: 400 },
  { id: 'phone', label: 'Phone', width: 150 },
  { id: 'email', label: 'Email', width: 400 },
  { id: 'date', label: 'Created Date', width: 230 },
  { id: 'service', label: 'Service', width: 300 },
  { id: 'isResponseGenerated', label: 'Response', width: 20, align: 'center' },
  { id: 'status', label: 'Status', width: 200 },
  { id: 'action', label: '' },
];

const InstantQuoteList = () => {
  const [tableData, setTableData] = useState<InstantEstimation[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [instantRequestStatusOptions, setInstantRequestStatusOptions] = useState<ddValue[]>([]);
  const [assignedAdminOptions, setAssignedAdminOptions] = useState<ddValue[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);
  const [selectedAssignedAdmin, setSelectedAssignedAdmin] = useState<ddValue>({} as ddValue);

  const debouncedSearchKey = useDebounce(searchKey, 500);

  const getInstantQuoteData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchInstantRequests(criteria);

      setTableData(searchResult);
      setTotalCount(count);
      setFetchError(null);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch instant quotes');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getInstantQuoteData();
  }, [getInstantQuoteData]);

  useEffect(() => {
    let isMounted = true;

    const loadOptions = async () => {
      try {
        const [statusData, adminUsers] = await Promise.all([
          getInstantRequestStatusOptions(),
          getUsers(),
        ]);

        if (isMounted) {
          const adminData = (adminUsers as AdminUser[]).map((user) => ({
            id: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}`,
            isDisabled: false,
          }));

          setInstantRequestStatusOptions(statusData);
          setAssignedAdminOptions(adminData);
        }
      } catch (error: any) {
        setFetchError(error.message || 'Failed to fetch options');
      }
    };

    loadOptions();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleAssignedAdminChange = useCallback((admin: ddValue): void => {
    setSelectedAssignedAdmin(admin);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getInstantQuoteData();
  }, [getInstantQuoteData]);

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <MainContent
      pageTitle="Instant Quotes"
      buttons={
        <>
          <InstantQuoteOutlinedIconButton color="primary" size="large" onClick={handleRefreshData}>
            <IconRefresh style={{ width: 18 }} />
          </InstantQuoteOutlinedIconButton>
          <InstantQuoteFilledIconButton size="large" color="primary">
            <IconDownload style={{ width: 18 }} />
          </InstantQuoteFilledIconButton>
        </>
      }
    >
      <InstantQuoteWrapper>
        <InstantQuoteToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          instantRequestStatusOptions={instantRequestStatusOptions}
          assignedAdminOptions={assignedAdminOptions}
          selectedStatus={selectedStatus}
          selectedAssignedAdmin={selectedAssignedAdmin}
          onStatusChange={handleStatusUpdate}
          onAssignedAdminChange={handleAssignedAdminChange}
          onFilterChange={handleFilterChange}
          totalCount={totalCount}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              currentPage={currentPage}
              onNext={() => setCurrentPage(currentPage + 1)}
              onPrev={() => setCurrentPage(currentPage - 1)}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
              totalCount={totalCount}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData.map((row) => (
            <InstantQuoteListItem key={row.id} request={row} />
          ))}
        </TableView>
      </InstantQuoteWrapper>
    </MainContent>
  );
};

export default InstantQuoteList;
