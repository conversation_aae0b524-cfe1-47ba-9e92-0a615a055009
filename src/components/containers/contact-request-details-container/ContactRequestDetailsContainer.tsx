import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { Errors } from 'src/shared/types/errorResonse';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { ContactRequest } from 'src/shared/services/contact-request/contact-request.type';

import { Formik } from 'formik';
import { useParams } from 'react-router-dom';
import { useState, useEffect, useCallback } from 'react';

import { appendZeroes } from 'src/utils/helper';
import { contactRequestSchema } from 'src/utils/validation/cotact-request/ContactRequest.schema';
import { initialContactRequest } from 'src/utils/validation/cotact-request/ContactRequest.values';

import { useToast } from 'src/providers/ToastProvider';
import { getUsers } from 'src/shared/services/admin/users/users.service';
import { LogTableName, LogColumnName } from 'src/shared/enums/logs.enums';
import {
  getSubject,
  getReference,
  getContactMethods,
  updateContactRequest,
  getContactRequestById,
  getContactRequestStatusOptions,
} from 'src/shared/services/contact-request/contact-request.service';

import Loader from 'src/components/common/loader/loader/Loader';
import NotFoundView from 'src/components/common/error/not-found-view/NotFoundView';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import LogDialog from '../../common/log-dialog';
import FormAction from './components/form-actions/FormAction';
import { CardWrapper } from './ContactRequestDetailsContainer.style';
import ContactRequestForm from '../contact-request-container/components/contact-request-form/ContactRequestForm';

const ContactRequestDetailsContainer = () => {
  const { contactId } = useParams<{ contactId: string }>();
  const { showToast } = useToast();

  const [edit, setEdit] = useState<EditMode<ContactRequest>>({} as EditMode<ContactRequest>);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [openLogsModal, setOpenLogsModal] = useState<boolean>(false);
  const [notFound, setNotFound] = useState<boolean>(false);

  const [contactRequestStatusOptions, setContactRequestStatusOptions] = useState<ddValue[]>([]);
  const [subjectOptions, setSubjectOptions] = useState<ddValue[]>([]);
  const [referenceOptions, setReferenceOptions] = useState<ddValue[]>([]);
  const [contactMethodData, setContactMethodData] = useState<ddValue[]>([]);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);
  const [adminOptions, setAdminOptions] = useState<AdminUser[]>([]);

  const selectAdminOptions: ddValue[] = adminOptions.map((admin) => ({
    id: admin.id,
    name: admin.fullName || `${admin.firstName} ${admin.lastName}`,
    isDisabled: false,
  }));

  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true);

      const [
        users,
        contactRequest,
        statusData,
        subjectData,
        referenceData,
        preferredContactMethodData,
      ] = await Promise.all([
        getUsers(),
        getContactRequestById(Number(contactId)),
        getContactRequestStatusOptions(),
        getSubject(),
        getReference(),
        getContactMethods(),
      ]);

      if (!contactRequest?.id) {
        showToast('Contact Request not found', 'error');
        setNotFound(true);
        setIsLoading(false);

        return;
      }

      if (Array.isArray(users)) {
        setAdminOptions(users);
      }

      setEdit((prev) => ({ ...prev, data: contactRequest }));
      setSelectedAdmin(contactRequest?.assignedAdmin || null);
      setContactRequestStatusOptions(statusData);
      setSubjectOptions(subjectData);
      setReferenceOptions(referenceData);
      setContactMethodData(preferredContactMethodData);

      if (contactRequest.assignedAdminId && Array.isArray(users)) {
        const admin = users.find((user) => user?.id === contactRequest?.assignedAdminId);
        if (admin) {
          setSelectedAdmin(admin);
        }
      }
    } catch (error) {
      showToast('Error fetching initial data', 'error');
      setNotFound(true);
    } finally {
      setIsLoading(false);
    }
  }, [contactId, showToast]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  const handleOpenLogs = () => {
    setOpenLogsModal(true);
  };

  const handleCloseLogs = () => {
    setOpenLogsModal(false);
  };

  const handleFormSubmit = async (values: ContactRequest) => {
    setIsLoading(true);

    try {
      const postData: ContactRequest = {
        name: values.name,
        phone: values.phone,
        emailId: values.emailId,
        id: Number(contactId),
        adminActionRemark: values?.adminActionRemark,
        preferredContactMethodId: values.preferredContactMethodId,
        assignedAdminId: values?.assignedAdminId || selectedAdmin?.id,
        statusId: values?.statusId,
        reference: values?.reference,
        subject: values?.subject,
        message: values?.message,
      };

      await updateContactRequest(postData);
      await fetchInitialData();

      showToast('Admin Action Updated Successfully', 'success');
    } catch (error) {
      const errorResponse = error as Errors;

      const errorMessage =
        typeof errorResponse.errors === 'string'
          ? errorResponse.errors
          : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainContent
      pageTitle={`Contact Request |  ${!notFound ? appendZeroes(Number(contactId), 4) : 'Invalid ID'}`}
    >
      <Loader loading={isLoading} />

      <Formik<ContactRequest>
        initialValues={initialContactRequest(edit?.data as ContactRequest)}
        onSubmit={handleFormSubmit}
        validationSchema={contactRequestSchema}
        enableReinitialize
      >
        {() => (
          <>
            <CardWrapper>
              {notFound ? (
                <NotFoundView
                  title="Contact Request Not Found"
                  description="The requested contact request does not exist."
                />
              ) : (
                <>
                  <ContactRequestForm
                    readMode={isLoading}
                    contactRequestStatusOptions={contactRequestStatusOptions}
                    subjectOptions={subjectOptions}
                    referenceOptions={referenceOptions}
                    contactMethodData={contactMethodData}
                    selectedAdmin={selectedAdmin}
                    adminOptions={selectAdminOptions}
                  />

                  <LogDialog
                    open={openLogsModal}
                    onClose={handleCloseLogs}
                    id={Number(contactId)}
                    tableName={LogTableName.CONTACT_REQUEST_TABLE}
                    columnName={LogColumnName.CONTACT_REQUEST_COLUMN}
                  />
                </>
              )}
            </CardWrapper>
            <FormAction handleOpen={handleOpenLogs} />
          </>
        )}
      </Formik>
    </MainContent>
  );
};

export default ContactRequestDetailsContainer;
