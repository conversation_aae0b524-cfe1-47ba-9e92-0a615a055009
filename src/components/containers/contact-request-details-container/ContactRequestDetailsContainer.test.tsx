import { render, screen, waitFor } from '@testing-library/react';

import ContactRequestDetailsContainer from './ContactRequestDetailsContainer';

jest.mock('src/providers/ToastProvider', () => ({
  useToast: () => ({ showToast: jest.fn() }),
}));
jest.mock('src/shared/services/admin/users/users.service', () => ({
  getUsers: jest.fn(() => Promise.resolve([])),
}));
jest.mock('src/shared/services/contact-request/contact-request.service', () => ({
  getContactRequestById: jest.fn(() => Promise.resolve({ id: 1 })),
  getContactRequestStatusOptions: jest.fn(() => Promise.resolve([])),
  getSubject: jest.fn(() => Promise.resolve([])),
  getReference: jest.fn(() => Promise.resolve([])),
  getContactMethods: jest.fn(() => Promise.resolve([])),
  updateContactRequest: jest.fn(() => Promise.resolve()),
}));

jest.mock('src/components/common/loader/loader/Loader', () => () => <div data-testid="loader" />);
jest.mock('src/components/common/error/not-found-view/NotFoundView', () => () => (
  <div data-testid="not-found" />
));
jest.mock(
  'src/components/common/main-content-wrapper/MainContentWrapper',
  () =>
    ({ children }: any) => <div>{children}</div>
);
jest.mock('./components/form-actions/FormAction', () => () => <div data-testid="form-action" />);
jest.mock(
  '../contact-request-container/components/contact-request-form/ContactRequestForm',
  () => () => <div data-testid="contact-request-form" />
);
jest.mock('../../common/log-dialog', () => () => <div data-testid="log-dialog" />);

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ contactId: '1' }),
}));

describe('ContactRequestDetailsContainer', () => {
  it('renders loader and form', async () => {
    render(<ContactRequestDetailsContainer />);
    expect(screen.getByTestId('loader')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('contact-request-form')).toBeInTheDocument());
  });

  it('renders not found view if contact request is missing', async () => {
    const { mockResolvedValueOnce } = jest.requireMock(
      'src/shared/services/contact-request/contact-request.service'
    ).getContactRequestById;
    mockResolvedValueOnce({});
    render(<ContactRequestDetailsContainer />);
    await waitFor(() => expect(screen.getByTestId('not-found')).toBeInTheDocument());
  });
});
