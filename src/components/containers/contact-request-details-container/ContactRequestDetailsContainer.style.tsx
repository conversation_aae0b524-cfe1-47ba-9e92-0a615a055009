import { Card, styled } from '@mui/material';

export const ContactRequestWrapper = styled('div')(() => ({
  height: '100vh',
  overflowY: 'scroll',
}));

export const CardWrapper = styled(Card)(({ theme }) => ({
  boxShadow: 'none',
  border: `1px solid ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  marginInline: theme.spacing(4),
  marginBlock: theme.spacing(2),
  padding: theme.spacing(4),
}));
