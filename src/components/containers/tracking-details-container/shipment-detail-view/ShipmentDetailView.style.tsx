import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

export const ShipmentDetailViewWrapper = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  background: theme.palette.background.default,
  borderRadius: theme.spacing(2),
}));

export const SectionPaper = styled(Box)(({ theme }) => ({
  background: theme.palette.background.paper,
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[2],
  padding: theme.spacing(4, 3),
  marginBottom: theme.spacing(4),
  border: `1px solid ${theme.palette.divider}`,
}));

export const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const SectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1.5),
  fontSize: 28,
}));

export const FieldRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(1.5),
}));

export const FieldLabel = styled(Box)(({ theme }) => ({
  minWidth: 120,
  color: theme.palette.text.secondary,
  fontWeight: 500,
  fontSize: 15,
}));

export const FieldValue = styled(Box)(({ theme }) => ({
  fontWeight: 400,
  fontSize: 16,
  flex: 1,
}));

export const MapContainer = styled(Box)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  height: 250,
  marginBottom: theme.spacing(2),
}));

export const ShipmentImage = styled('img')(({ theme }) => ({
  maxWidth: 350,
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[1],
}));
