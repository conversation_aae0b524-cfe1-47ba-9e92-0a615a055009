import type { TrackingShipment } from 'src/shared/services/tracking/tracking.type';

import { Box, Typography } from '@mui/material';

import Field from './components/Field/Field';
import Section from './components/Section/Section';
import MapSection from './components/MapSection/MapSection';
import ShipmentImage from './components/ShipmentImage/ShipmentImage';
import { ShipmentDetailViewWrapper } from './ShipmentDetailView.style';
import TrackingTimeline from './components/TrackingTimeline/TrackingTimeline';

interface ShipmentDetailViewProps {
  shipment: TrackingShipment;
}

const statusSteps = ['Booked', 'Picked Up', 'In Transit', 'Out for Delivery', 'Delivered'];

const ShipmentDetailView = ({ shipment }: ShipmentDetailViewProps) => {
  const currentStatusIdx = statusSteps.findIndex((s) =>
    shipment.status.some((st) => st.name.toLowerCase().includes(s.toLowerCase()))
  );
  const currentStep = currentStatusIdx === -1 ? 0 : currentStatusIdx;

  return (
    <ShipmentDetailViewWrapper>
      <Typography variant="h4" mb={2} fontWeight={700} color="primary.main">
        Tracking Details
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" mb={4}>
        AWB: <b>{shipment.awbNumber}</b>
      </Typography>
      <TrackingTimeline statuses={statusSteps} current={currentStep} />
      <Box display="flex" gap={4} flexWrap="wrap" mb={4}>
        <Box flex={2} minWidth={340}>
          <Section
            title="Customer"
            icon={
              <span role="img" aria-label="user">
                👤
              </span>
            }
          >
            <Field label="Name" value={shipment.customerName} highlight />
            <Field label="Email" value={shipment.customerEmailId} />
            <Field label="Phone" value={shipment.customerPhoneNumber} highlight />
            <Field label="Address" value={shipment.customerAddressLine1} />
            {shipment.customerAddressLine2 && (
              <Field label="Address Line 2" value={shipment.customerAddressLine2} />
            )}
            <Field label="City" value={shipment.customerCity} />
            <Field label="State" value={shipment.customerState} />
            <Field label="Country" value={shipment.customerCountry} />
            <Field label="Pincode" value={shipment.customerPincode} />
            <MapSection
              address={`${shipment.customerAddressLine1} ${shipment.customerAddressLine2 || ''} ${shipment.customerCity} ${shipment.customerState} ${shipment.customerCountry}`}
              label="Customer Location"
            />
          </Section>

          <Section
            title="Shipment Info"
            icon={
              <span role="img" aria-label="box">
                📦
              </span>
            }
          >
            <Field label="Status" value={shipment.status.map((s) => s.name).join(', ')} highlight />
            <Field
              label="Service Category"
              value={shipment.serviceCategory.map((s) => s.name).join(', ')}
            />
            <Field label="At Risk" value={shipment.isShipmentAtRisk ? 'Yes' : 'No'} />
            <Field label="Created At" value={shipment.createdAt} />
            <Field label="Updated At" value={shipment.updatedAt} />
          </Section>

          <Section
            title="Package Details"
            icon={
              <span role="img" aria-label="package">
                🎁
              </span>
            }
          >
            <Field
              label="Content Type"
              value={shipment.contentType.map((c) => c.name).join(', ')}
            />
            <Field label="Description" value={shipment.contentDescription} />
            <Field label="Weight" value={`${shipment.weight} ${shipment.weightUnit}`} />
            <Field
              label="Dimensions"
              value={`${shipment.length} × ${shipment.width} × ${shipment.height} ${shipment.dimensionsUnit}`}
            />
            <Field label="Shipment Value" value={shipment.shipmentValue} />
          </Section>

          {shipment.shipmentImageUrl && (
            <Section
              title="Shipment Image"
              icon={
                <span role="img" aria-label="image">
                  🖼️
                </span>
              }
            >
              <ShipmentImage src={shipment.shipmentImageUrl} alt="Shipment" />
            </Section>
          )}
        </Box>
        <Box flex={1} minWidth={300}>
          <Section
            title="Route"
            icon={
              <span role="img" aria-label="location">
                📍
              </span>
            }
          >
            <Field label="Origin Location" value={shipment.originLocation} />
            <Field label="Origin Pincode" value={shipment.originPincode} />
            <MapSection address={shipment.originLocation} label="Origin Location" />
            <Field label="Destination Location" value={shipment.destinationLocation} />
            <Field label="Destination Pincode" value={shipment.destinationPincode} />
            <MapSection address={shipment.destinationLocation} label="Destination Location" />
          </Section>
          <Section
            title="Pickup & Dropoff"
            icon={
              <span role="img" aria-label="truck">
                🚚
              </span>
            }
          >
            <Field label="Pickup Location" value={shipment.pickupLocation} />
            <Field label="Pickup Address" value={shipment.pickupAddressLine1} />
            {shipment.pickupAddressLine2 && (
              <Field label="Pickup Address Line 2" value={shipment.pickupAddressLine2} />
            )}
            <Field label="Pickup City" value={shipment.pickupCity} />
            <Field label="Pickup State" value={shipment.pickupState} />
            <Field label="Pickup Country" value={shipment.pickupCountry} />
            <Field label="Pickup Pincode" value={shipment.pickupPincode} />
            <MapSection
              address={`${shipment.pickupAddressLine1} ${shipment.pickupAddressLine2 || ''} ${shipment.pickupCity} ${shipment.pickupState} ${shipment.pickupCountry}`}
              label="Pickup Location"
            />
            <Field
              label="Dropoff Type"
              value={shipment.dropOffLocationType.map((d) => d.name).join(', ')}
            />
          </Section>
        </Box>
      </Box>
    </ShipmentDetailViewWrapper>
  );
};

export default ShipmentDetailView;
