import type { TrackingShipment } from 'src/shared/services/tracking/tracking.type';

import React from 'react';

import { Box, Grid, Typography } from '@mui/material';

import { Iconify } from 'src/components/common/icons/iconify';
import StatusChip from 'src/components/common/status-chip/StatusChip';

import Field from './components/Field/Field';
import Section from './components/Section/Section';
import MapSection from './components/MapSection/MapSection';
import TrackingTimeline from './components/TrackingTimeline/TrackingTimeline';
import {
  MainColumn,
  SideColumn,
  ContentGrid,
  HeaderSection,
  ShipmentDetailViewWrapper,
} from './ShipmentDetailView.style';

interface ShipmentDetailViewProps {
  shipment: TrackingShipment;
}

const statusSteps = ['Booked', 'Picked Up', 'In Transit', 'Out for Delivery', 'Delivered'];

const ShipmentDetailView = ({ shipment }: ShipmentDetailViewProps) => {
  const currentStatusIdx = statusSteps.findIndex((s) =>
    shipment.status.some((st) => st.name.toLowerCase().includes(s.toLowerCase()))
  );
  const currentStep = currentStatusIdx === -1 ? 0 : currentStatusIdx;

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(value);

  const getStatusColor = (status: string) => {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('delivered')) return '#4caf50';
    if (statusLower.includes('transit') || statusLower.includes('picked')) return '#ff9800';
    if (statusLower.includes('booked')) return '#2196f3';
    return '#757575';
  };

  const buildFullAddress = (
    line1: string,
    line2?: string,
    city?: string,
    state?: string,
    country?: string
  ) => {
    const parts = [line1, line2, city, state, country].filter(Boolean);
    return parts.join(', ');
  };

  return (
    <ShipmentDetailViewWrapper>
      <HeaderSection>
        <Box>
          <Typography variant="h4" color="primary.main">
            Tracking Details
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            AWB: <strong>{shipment.awbNumber}</strong>
          </Typography>
        </Box>
        <Box>
          {shipment.status.map((status) => (
            <StatusChip
              key={status.id}
              label={status.name}
              variant="soft"
              color={getStatusColor(status.name)}
            />
          ))}
        </Box>
      </HeaderSection>

      <TrackingTimeline statuses={statusSteps} current={currentStep} />

      <ContentGrid container spacing={3}>
        <MainColumn item xs={12} lg={8}>
          <Section title="Customer Information" icon={<Iconify icon="mdi:account" width={24} />}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Field label="Name" value={shipment.customerName} highlight />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Field label="Phone" value={shipment.customerPhoneNumber} highlight />
              </Grid>
              <Grid item xs={12}>
                <Field label="Email" value={shipment.customerEmailId} />
              </Grid>
              <Grid item xs={12}>
                <Field
                  label="Address"
                  value={buildFullAddress(
                    shipment.customerAddressLine1,
                    shipment.customerAddressLine2,
                    shipment.customerCity,
                    shipment.customerState,
                    shipment.customerCountry
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Field label="City" value={shipment.customerCity} />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Field label="Pincode" value={shipment.customerPincode} />
              </Grid>
            </Grid>
            <MapSection
              address={buildFullAddress(
                shipment.customerAddressLine1,
                shipment.customerAddressLine2,
                shipment.customerCity,
                shipment.customerState,
                shipment.customerCountry
              )}
              label="Customer Location"
            />
          </Section>

          <Section
            title="Shipment Information"
            icon={<Iconify icon="mdi:package-variant" width={24} />}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Field
                  label="Service Category"
                  value={shipment.serviceCategory.map((s) => s.name).join(', ')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Field
                  label="At Risk"
                  value={shipment.isShipmentAtRisk ? 'Yes' : 'No'}
                  highlight={shipment.isShipmentAtRisk}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Field label="Created At" value={formatDate(shipment.createdAt)} />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Field label="Updated At" value={formatDate(shipment.updatedAt)} />
              </Grid>
            </Grid>
          </Section>
        </MainColumn>

        <SideColumn item xs={12} lg={4}>
          <Section
            title="Route Information"
            icon={<Iconify icon="mdi:map-marker-path" width={24} />}
          >
            <Field label="Origin Location" value={shipment.originLocation} />
            <Field label="Origin Pincode" value={shipment.originPincode} />
            <MapSection address={shipment.originLocation} label="Origin Location" />

            <Box mt={2}>
              <Field label="Destination Location" value={shipment.destinationLocation} />
              <Field label="Destination Pincode" value={shipment.destinationPincode} />
              <MapSection address={shipment.destinationLocation} label="Destination Location" />
            </Box>
          </Section>
        </SideColumn>
      </ContentGrid>
    </ShipmentDetailViewWrapper>
  );
};

export default ShipmentDetailView;
