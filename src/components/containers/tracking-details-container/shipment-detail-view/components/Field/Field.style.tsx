import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

export const FieldRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(2),
  gap: theme.spacing(2),
  minHeight: 32,
}));

export const FieldLabel = styled(Box)(({ theme }) => ({
  minWidth: 140,
  color: theme.palette.text.secondary,
  fontWeight: 600,
  fontSize: 15.5,
  paddingTop: 2,
  letterSpacing: 0.12,
  lineHeight: 1.4,
  flexShrink: 0,
}));

export const FieldValue = styled(Box, {
  shouldForwardProp: (prop) => prop !== '$highlight',
})<{ $highlight?: boolean }>(({ theme, $highlight }) => ({
  fontWeight: $highlight ? 700 : 400,
  fontSize: 16.5,
  flex: 1,
  color: $highlight ? theme.palette.primary.main : theme.palette.text.primary,
  wordBreak: 'break-word',
  background: theme.palette.action.selected,
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1),
  transition: 'background 0.2s',
  lineHeight: 1.5,
}));
