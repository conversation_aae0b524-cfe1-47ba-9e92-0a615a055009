import { Box, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';

export const SectionPaper = styled(Paper)(({ theme }) => ({
  background: theme.palette.background.paper,
  borderRadius: theme.spacing(3),
  boxShadow: theme.shadows[1],
  padding: theme.spacing(4, 4, 3, 4),
  marginBottom: theme.spacing(5),
  border: `1px solid ${theme.palette.divider}`,
  transition: 'box-shadow 0.2s',
  '&:hover': {
    boxShadow: theme.shadows[3],
  },
}));

export const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2.5),
  paddingBottom: theme.spacing(1),
  borderBottom: `1.5px solid ${theme.palette.divider}`,
  gap: theme.spacing(1.5),
}));

export const SectionIcon = styled(Box)(({ theme }) => ({
  fontSize: 28,
  color: theme.palette.primary.main,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: theme.palette.action.hover,
  borderRadius: '50%',
  width: 40,
  height: 40,
  boxShadow: theme.shadows[0],
}));
