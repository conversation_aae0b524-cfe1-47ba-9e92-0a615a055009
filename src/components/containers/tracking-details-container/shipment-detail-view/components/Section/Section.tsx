import { Box, Typography } from '@mui/material';

import { SectionIcon, SectionPaper, SectionHeader } from './Section.style';

interface SectionProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  subtitle?: string;
}

const Section = ({ title, children, icon, subtitle }: SectionProps) => (
  <SectionPaper>
    <SectionHeader>
      {icon && <SectionIcon>{icon}</SectionIcon>}
      <Box>
        <Typography
          variant="h6"
          fontWeight={700}
          color="primary.main"
          sx={{ letterSpacing: 0.3, mb: 0.2, fontSize: 22 }}
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mt: 0.5, fontWeight: 500, letterSpacing: 0.15, fontSize: 15 }}
          >
            {subtitle}
          </Typography>
        )}
      </Box>
    </SectionHeader>
    <Box pt={2}>{children}</Box>
  </SectionPaper>
);

export default Section;
