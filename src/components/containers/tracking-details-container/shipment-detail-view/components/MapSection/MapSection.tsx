import { Typography } from '@mui/material';

import { MapContainer } from './MapSection.style';

interface MapSectionProps {
  address: string;
  label: string;
}

const MapSection = ({ address, label }: MapSectionProps) => {
  if (!address) return null;
  const query = encodeURIComponent(address);
  const src = `https://maps.google.com/maps?q=${query}&t=&z=13&ie=UTF8&iwloc=&output=embed`;
  return (
    <>
      <Typography variant="subtitle2" mb={1}>
        {label} on Map
      </Typography>
      <MapContainer>
        <iframe
          title={label}
          width="100%"
          height="250"
          src={src}
          style={{ border: 0 }}
          loading="lazy"
          allowFullScreen
        />
      </MapContainer>
    </>
  );
};

export default MapSection;
