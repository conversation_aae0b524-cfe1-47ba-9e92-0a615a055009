import { Typography as MuiTypography } from '@mui/material';

import { Iconify } from 'src/components/common/icons/iconify';

import { Step, Stepper, StepLabel, CustomConnector } from './TrackingTimeline.style';

const StepIcon = (props: any) => {
  const { active, completed } = props;
  let iconName = '';
  let color = '';
  if (completed) {
    iconName = 'mdi:check-circle';
    color = '#1976d2';
  } else if (active) {
    iconName = 'mdi:circle';
    color = '#9c27b0';
  } else {
    iconName = 'mdi:circle-outline';
    color = '#bdbdbd';
  }
  return <Iconify icon={iconName} width={28} color={color} />;
};

interface TrackingTimelineProps {
  statuses: string[];
  current: number;
}

const TrackingTimeline = ({ statuses, current }: TrackingTimelineProps) => (
  <Stepper alternativeLabel activeStep={current} connector={<CustomConnector />}>
    {statuses.map((label, idx) => (
      <Step key={label} completed={idx < current}>
        <StepLabel StepIconComponent={StepIcon}>
          <MuiTypography
            variant="caption"
            sx={{ fontWeight: 600, color: idx === current ? '#1976d2' : 'inherit' }}
          >
            {label}
          </MuiTypography>
        </StepLabel>
      </Step>
    ))}
  </Stepper>
);

export default TrackingTimeline;
