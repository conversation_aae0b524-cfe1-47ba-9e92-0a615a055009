import {
  styled,
  StepConnector,
  Step as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>tep<PERSON>abe<PERSON>,
} from '@mui/material';

export const CustomConnector = styled(StepConnector)(({ theme }) => ({
  top: 18,
  left: 'calc(-50% + 18px)',
  right: 'calc(50% + 18px)',
  '& .MuiStepConnector-line': {
    borderColor: theme.palette.primary.main,
    borderTopWidth: 4,
    borderRadius: 2,
    minHeight: 4,
    background: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[300],
    transition: 'background 0.3s',
  },
}));

export const Stepper = styled(MuiStepper)(({ theme }) => ({
  background: 'transparent',
  padding: theme.spacing(3, 0),
  '& .MuiStepConnector-root': {
    minHeight: 40,
  },
}));

export const Step = styled(MuiStep)(({ theme }) => ({
  '& .MuiStepLabel-root': {
    padding: theme.spacing(1, 0),
  },
}));

export const StepLabel = styled(MuiStepLabel)(({ theme }) => ({
  '& .MuiStepLabel-label': {
    color: theme.palette.text.secondary,
    fontWeight: 500,
    fontSize: 14,
    [theme.breakpoints.up('sm')]: {
      fontSize: 16,
    },
  },
  '& .MuiStepLabel-iconContainer': {
    paddingRight: theme.spacing(1),
  },
}));
