import type { TrackingShipment } from 'src/shared/services/tracking/tracking.type';

import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { appendZeroes } from 'src/utils/helper';

import { getTrackingById } from 'src/shared/services/tracking/tracking.service';

import Loader from 'src/components/common/loader/loader/Loader';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import StepperForm from './stepper-form/StepperForm';
import ShipmentDetailView from './shipment-detail-view/ShipmentDetailView';

const TrackingDetailsContainer = () => {
  const { shipmentId } = useParams<{ shipmentId: string }>();

  const [notFound, setNotFound] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [shipment, setShipment] = useState<TrackingShipment | null>(null);

  const getPageTitle = () => {
    if (shipmentId === 'new') return 'Tracking Shipment | New';
    if (!notFound && shipmentId && !Number.isNaN(Number(shipmentId))) {
      return `Tracking Shipment | ${appendZeroes(Number(shipmentId), 4)}`;
    }
    return 'Tracking Shipment | Invalid ID';
  };

  useEffect(() => {
    const fetchShipment = async () => {
      if (shipmentId && shipmentId !== 'new') {
        setIsLoading(true);
        try {
          const data = await getTrackingById(Number(shipmentId));
          setShipment(data);
          setNotFound(false);
        } catch (err) {
          setNotFound(true);
          setShipment(null);
        } finally {
          setIsLoading(false);
        }
      } else {
        setShipment(null);
        setNotFound(false);
      }
    };
    fetchShipment();
  }, [shipmentId]);

  return (
    <MainContent pageTitle={getPageTitle()}>
      <Loader loading={isLoading} />
      {shipmentId === 'new' ? (
        <StepperForm />
      ) : notFound ? (
        <div style={{ padding: 32, textAlign: 'center', color: '#b71c1c' }}>
          Shipment not found.
        </div>
      ) : shipment ? (
        <ShipmentDetailView shipment={shipment} />
      ) : null}
    </MainContent>
  );
};

export default TrackingDetailsContainer;
