import { useFormikContext } from 'formik';

import { Grid, Divider, Container } from '@mui/material';

import { Icons, Iconify } from '../../../../../common/icons/iconify';
import {
  FieldGrid,
  FieldLabel,
  FieldValue,
  ReviewTitle,
  SectionsRow,
  SectionPaper,
  SectionTitle,
  HighlightChip,
  SectionsStack,
  FieldContainer,
  SectionDivider,
  ReviewContainer,
  SubsectionTitle,
  ReviewDescription,
} from './ReviewComponent.styles';

import type { ShipmentFormValues } from '../../types';

// Component to display each section's field

const SectionField = ({
  label,
  value,
  highlight,
}: {
  label: string;
  value: string;
  highlight?: boolean;
}) => (
  <FieldContainer>
    <FieldGrid container>
      <Grid item xs={12} sm={5} md={4}>
        <FieldLabel variant="body2">{label}:</FieldLabel>
      </Grid>
      <Grid item xs={12} sm={7} md={8}>
        <FieldValue variant="body2">
          {value || 'Not provided'}
          {highlight && <HighlightChip size="small" color="primary" label="Important" />}
        </FieldValue>
      </Grid>
    </FieldGrid>
  </FieldContainer>
);

// Component to display each section

const Section = ({
  title,
  children,
  icon,
}: {
  title: string;
  children: React.ReactNode;
  icon: React.ReactNode;
}) => (
  <SectionPaper elevation={0} variant="outlined">
    <SectionTitle variant="h6">
      {icon}
      {title}
    </SectionTitle>
    <Divider />
    <SectionDivider />
    {children}
  </SectionPaper>
);

const ReviewComponent = () => {
  const { values } = useFormikContext<ShipmentFormValues>();

  return (
    <ReviewContainer>
      <Container maxWidth="lg">
        <ReviewTitle variant="h5">Review Shipment Details</ReviewTitle>
        <ReviewDescription variant="body2" color="text.secondary">
          Please review all the information below before submitting your shipment request.
        </ReviewDescription>

        <SectionsStack>
          {/* First Row - Customer Info and Origin/Destination */}
          <SectionsRow container spacing={3}>
            <Grid item xs={12} md={6}>
              <Section
                title="Customer Information"
                icon={<Iconify icon={Icons.tracking.customer} width={24} />}
              >
                <SectionField label="Name" value={values.customerName} highlight />
                <SectionField label="Email" value={values.customerEmail} />
                <SectionField label="Phone" value={values.customerPhone} highlight />
                <SectionField label="Address" value={values.customerAddressLine1} />
                {values.customerAddressLine2 && (
                  <SectionField label="Address Line 2" value={values.customerAddressLine2} />
                )}
                <SectionField label="City" value={values.customerCity} />
                <SectionField label="State" value={values.customerState} />
                <SectionField label="Country" value={values.customerCountry} />
                <SectionField label="Pincode" value={values.customerPincode} />
              </Section>
            </Grid>

            <Grid item xs={12} md={6}>
              <Section
                title="Origin & Destination"
                icon={<Iconify icon={Icons.tracking.pincode} width={24} />}
              >
                <SectionField label="Origin Pincode" value={values.originPincode} highlight />
                <SectionField
                  label="Destination Pincode"
                  value={values.destinationPincode}
                  highlight
                />
              </Section>
            </Grid>
          </SectionsRow>

          {/* Second Row - Package Details and Service Options */}
          <SectionsRow container spacing={3}>
            <Grid item xs={12} md={6}>
              <Section
                title="Package Details"
                icon={<Iconify icon={Icons.tracking.content} width={24} />}
              >
                <SectionField label="Content Type" value={values.contentType} />
                <SectionField label="Content Value" value={values.contentValue} />
                <SectionField label="Description" value={values.contentDescription} />
                {values.weight && (
                  <SectionField
                    label="Weight"
                    value={`${values.weight} ${values.weightUnit}`}
                    highlight
                  />
                )}
                {(values.length || values.width || values.height) && (
                  <SectionField
                    label="Dimensions"
                    value={`${values.length || '-'} × ${values.width || '-'} × ${values.height || '-'}`}
                  />
                )}
                {values.shipmentValue && (
                  <SectionField label="Shipment Value" value={values.shipmentValue} />
                )}
              </Section>
            </Grid>

            <Grid item xs={12} md={6}>
              <Section
                title="Service Options"
                icon={<Iconify icon={Icons.tracking.service} width={24} />}
              >
                <SectionField label="Service Type" value={values.serviceType} highlight />
                <SectionField label="Package Type" value={values.packageType} />
              </Section>
            </Grid>
          </SectionsRow>

          {/* Third Row - Pickup & Delivery (full width) */}
          <SectionsRow container spacing={3}>
            <Grid item xs={12}>
              <Section
                title="Pickup & Delivery"
                icon={<Iconify icon={Icons.tracking.shipping} width={24} />}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <SubsectionTitle variant="subtitle2">Pickup Address:</SubsectionTitle>
                    <SectionField label="Address" value={values.pickupAddressLine1} />
                    {values.pickupAddressLine2 && (
                      <SectionField label="Address Line 2" value={values.pickupAddressLine2} />
                    )}
                    <SectionField label="City" value={values.pickupCity} />
                    <SectionField label="State" value={values.pickupState} />
                    <SectionField label="Zip Code" value={values.pickupZipCode} />
                    <SectionField label="Country" value={values.pickupCountry} />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <SubsectionTitle variant="subtitle2">Delivery Address:</SubsectionTitle>
                    <SectionField label="Address" value={values.deliveryAddressLine1} />
                    {values.deliveryAddressLine2 && (
                      <SectionField label="Address Line 2" value={values.deliveryAddressLine2} />
                    )}
                    <SectionField label="City" value={values.deliveryCity} />
                    <SectionField label="State" value={values.deliveryState} />
                    <SectionField label="Zip Code" value={values.deliveryZipCode} />
                    <SectionField label="Country" value={values.deliveryCountry} />
                    <SectionField label="Recipient Name" value={values.recipientName} highlight />
                    <SectionField label="Recipient Phone" value={values.recipientPhone} highlight />
                    {values.deliveryCountryType && (
                      <SectionField
                        label="Delivery Country Type"
                        value={values.deliveryCountryType}
                      />
                    )}
                    {values.deliveryType && (
                      <SectionField label="Delivery Type" value={values.deliveryType} />
                    )}
                  </Grid>
                </Grid>
              </Section>
            </Grid>
          </SectionsRow>
        </SectionsStack>
      </Container>
    </ReviewContainer>
  );
};

export default ReviewComponent;
