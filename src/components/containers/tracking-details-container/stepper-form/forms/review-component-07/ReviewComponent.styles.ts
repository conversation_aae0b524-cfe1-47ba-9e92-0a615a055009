import { styled } from '@mui/material/styles';
import { <PERSON>, Card, Chip, Grid, alpha, Stack, Typography } from '@mui/material';

// Container styles

export const ReviewContainer = styled(Box)(({ theme }) => ({
  maxWidth: '100%',
  margin: '0 auto',
}));

export const ReviewTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 800,
  color: theme.palette.primary.main,
  fontSize: '1.75rem',
  position: 'relative',
  display: 'inline-block',
}));

export const ReviewDescription = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  color: theme.palette.text.secondary,
  fontSize: '1rem',
  maxWidth: '700px',
}));

export const SectionsStack = styled(Stack)(({ theme }) => ({
  gap: theme.spacing(3),
}));

export const SectionsRow = styled(Grid)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

// Section styles

export const SectionPaper = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(0),
  borderRadius: theme.shape.borderRadius * 1.5,
  boxShadow: `0 2px 24px 0 ${alpha(theme.palette.grey[500], 0.1)}`,
  height: '100%',
  transition: 'all 0.3s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    boxShadow: `0 10px 40px 0 ${alpha(theme.palette.primary.main, 0.1)}`,
    transform: 'translateY(-4px)',
  },
}));

export const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  fontWeight: 700,
  display: 'flex',
  alignItems: 'center',
  fontSize: '1.1rem',
  '& svg': {
    marginRight: theme.spacing(1.5),
    color: theme.palette.primary.main,
  },
}));

export const SectionDivider = styled('div')(({ theme }) => ({
  marginBottom: theme.spacing(2),
}));

// Field styles

export const FieldContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(1.5),
  padding: theme.spacing(1.25),
  borderRadius: theme.shape.borderRadius,
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.04),
    transform: 'translateX(4px)',
  },
}));

export const FieldGrid = styled(Grid)({
  alignItems: 'center',
});

export const FieldLabel = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontWeight: 600,
  fontSize: '0.875rem',
}));

export const FieldValue = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  wordBreak: 'break-word',
  color: theme.palette.text.primary,
  display: 'flex',
  alignItems: 'center',
  fontSize: '0.875rem',
}));

export const SubsectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  marginTop: theme.spacing(3),
  fontWeight: 600,
  color: theme.palette.primary.dark,
  borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  paddingBottom: theme.spacing(1),
  fontSize: '1rem',
  position: 'relative',
  '&:after': {
    content: '""',
    position: 'absolute',
    bottom: -2,
    left: 0,
    width: 40,
    height: 2,
    backgroundColor: theme.palette.primary.main,
  },
}));

export const HighlightChip = styled(Chip)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  height: 22,
  fontSize: '0.75rem',
  fontWeight: 600,
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.dark,
  borderRadius: '12px',
  '& .MuiChip-label': {
    padding: '0 8px',
  },
}));
