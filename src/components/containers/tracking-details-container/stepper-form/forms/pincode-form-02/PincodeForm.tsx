/* eslint-disable react-hooks/exhaustive-deps */
import { useFormikContext } from 'formik';
import React, { useState, useEffect, useCallback } from 'react';

import { Box, Grid, Divider, Typography } from '@mui/material';

import { useDebounce } from 'src/hooks/use-debounce';

import { fetchPincodeDetails } from 'src/shared/services/pincode-validation/pincode-validation.service';

import Loader from 'src/components/common/loader/loader/Loader';
import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  FormPaper,
  InfoAlert,
  InfoAlertIcon,
  SectionDivider,
  InfoSectionIcon,
  InfoSectionHeader,
  PincodeFormWrapper,
} from './PincodeForm.style';

interface PincodeFormValues {
  originPincode: string;
  destinationPincode: string;
}

const PincodeForm: React.FC = () => {
  const { values, handleChange, handleBlur, errors, touched, setFieldError } =
    useFormikContext<PincodeFormValues>();
  const [loadingOrigin, setLoadingOrigin] = useState(false);
  const [loadingDestination, setLoadingDestination] = useState(false);
  const [originValid, setOriginValid] = useState<boolean | null>(null);
  const [destinationValid, setDestinationValid] = useState<boolean | null>(null);
  const [originCity, setOriginCity] = useState<string>('');
  const [destinationCity, setDestinationCity] = useState<string>('');

  const debouncedOrigin = useDebounce(values.originPincode, 500);
  const debouncedDestination = useDebounce(values.destinationPincode, 500);

  const validatePincode = useCallback(
    async (field: 'originPincode' | 'destinationPincode', value: string) => {
      if (!value || value.length < 6) {
        if (field === 'originPincode') {
          setOriginValid(null);
          setOriginCity('');
        } else {
          setDestinationValid(null);
          setDestinationCity('');
        }
        setFieldError(field, '');
        return;
      }

      if (field === 'originPincode' && value === values.destinationPincode) {
        setFieldError(field, 'Origin and destination pincodes cannot be the same.');
        setOriginValid(false);
        return;
      }

      if (field === 'destinationPincode' && value === values.originPincode) {
        setFieldError(field, 'Origin and destination pincodes cannot be the same.');
        setDestinationValid(false);
        return;
      }

      if (field === 'originPincode') setLoadingOrigin(true);
      else setLoadingDestination(true);

      try {
        const res = await fetchPincodeDetails(value);
        const data = Array.isArray(res) ? res[0] : res;

        if (data.Status === 'Error') {
          setFieldError(field, data.Message || 'Invalid pincode');
          if (field === 'originPincode') {
            setOriginValid(false);
            setOriginCity('');
          } else {
            setDestinationValid(false);
            setDestinationCity('');
          }
        } else if (!data.PostOffice || !data.PostOffice.length) {
          setFieldError(field, 'No post office found for this pincode');
          if (field === 'originPincode') {
            setOriginValid(false);
            setOriginCity('');
          } else {
            setDestinationValid(false);
            setDestinationCity('');
          }
        } else if (data.PostOffice[0].DeliveryStatus === 'Non-Delivery') {
          setFieldError(field, 'This pincode is not serviceable (Non-Delivery)');
          if (field === 'originPincode') {
            setOriginValid(false);
            setOriginCity('');
          } else {
            setDestinationValid(false);
            setDestinationCity('');
          }
        } else {
          setFieldError(field, '');
          const cityName = data.PostOffice[0].District;
          if (field === 'originPincode') {
            setOriginValid(true);
            setOriginCity(cityName);
          } else {
            setDestinationValid(true);
            setDestinationCity(cityName);
          }
        }
      } catch (e) {
        setFieldError(field, 'Failed to validate pincode');
        if (field === 'originPincode') {
          setOriginValid(false);
          setOriginCity('');
        } else {
          setDestinationValid(false);
          setDestinationCity('');
        }
      } finally {
        if (field === 'originPincode') {
          setLoadingOrigin(false);
        } else {
          setLoadingDestination(false);
        }
      }
    },
    [setFieldError]
  );

  useEffect(() => {
    if (debouncedOrigin.length === 6) {
      validatePincode('originPincode', debouncedOrigin);
    }
  }, [debouncedOrigin, validatePincode]);

  useEffect(() => {
    if (debouncedDestination.length === 6) {
      validatePincode('destinationPincode', debouncedDestination);
    }
  }, [debouncedDestination, validatePincode]);

  return (
    <PincodeFormWrapper container spacing={3}>
      <Grid item xs={12}>
        <FormPaper elevation={0}>
          <Grid container spacing={2}>
            {/* Pincode Information Section */}
            <Grid item xs={12}>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.pincode} width={18} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Pincode Validation
                </Typography>
              </InfoSectionHeader>
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="originPincode"
                name="originPincode"
                label="Origin Pincode"
                variant="outlined"
                type="text"
                value={values.originPincode}
                handleChange={handleChange}
                handleBlur={handleBlur}
                startAdornment={<Iconify icon="mdi:map-marker" width={20} />}
                endAdornment={
                  loadingOrigin ? (
                    <Loader loading />
                  ) : originValid === true ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                      <Typography variant="caption" sx={{ mr: 0.5, fontWeight: 'bold' }}>
                        {originCity?.toUpperCase()}
                      </Typography>
                      <Iconify icon="mdi:check-circle" width={20} />
                    </Box>
                  ) : originValid === false ? (
                    <Box sx={{ color: 'error.main' }}>
                      <Iconify icon="mdi:alert-circle" width={20} />
                    </Box>
                  ) : undefined
                }
                error={Boolean(errors.originPincode && touched.originPincode)}
                helperText={touched.originPincode ? errors.originPincode : ''}
                required
                maxLength={6}
                placeholder="Enter 6-digit pincode"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="destinationPincode"
                name="destinationPincode"
                label="Destination Pincode"
                variant="outlined"
                type="text"
                value={values.destinationPincode}
                handleChange={handleChange}
                handleBlur={handleBlur}
                startAdornment={<Iconify icon="mdi:map-marker-check" width={20} />}
                endAdornment={
                  loadingDestination ? (
                    <Loader loading />
                  ) : destinationValid === true ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                      <Typography variant="caption" sx={{ mr: 0.5, fontWeight: 'bold' }}>
                        {destinationCity?.toUpperCase()}
                      </Typography>
                      <Iconify icon="mdi:check-circle" width={20} />
                    </Box>
                  ) : destinationValid === false ? (
                    <Box sx={{ color: 'error.main' }}>
                      <Iconify icon="mdi:alert-circle" width={20} />
                    </Box>
                  ) : undefined
                }
                error={Boolean(errors.destinationPincode && touched.destinationPincode)}
                helperText={touched.destinationPincode ? errors.destinationPincode : ''}
                required
                maxLength={6}
                placeholder="Enter 6-digit pincode"
              />
            </Grid>
            <Grid item xs={12}>
              <SectionDivider>
                <Divider />
              </SectionDivider>
            </Grid>
            <Grid item xs={12}>
              <InfoAlert>
                <InfoAlertIcon>
                  <Iconify icon="mdi:information" width={24} />
                </InfoAlertIcon>
                <Typography variant="body2" color="text.secondary">
                  Pincode validation checks the serviceability of your shipment locations. Both
                  origin and destination pincodes must be valid to proceed.
                </Typography>
              </InfoAlert>
            </Grid>
          </Grid>
        </FormPaper>
      </Grid>
    </PincodeFormWrapper>
  );
};

export default PincodeForm;
