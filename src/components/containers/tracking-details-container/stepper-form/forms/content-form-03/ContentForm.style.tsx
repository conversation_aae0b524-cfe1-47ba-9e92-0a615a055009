import { styled } from '@mui/material/styles';
import { Box, Grid, Paper } from '@mui/material';

export const ContentFormWrapper = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  ...theme.typography.body2,
}));

export const FormPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
}));

export const InfoSectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const InfoSectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const SectionDivider = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

export const InfoAlert = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: theme.palette.info.lighter,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export const InfoAlertIcon = styled(Box)(({ theme }) => ({
  color: theme.palette.info.main,
  marginRight: theme.spacing(1),
}));

export const DimIcon = styled(Box)(({ theme }) => ({
  opacity: 0.8,
}));

export const DimIconFaded = styled(Box)(({ theme }) => ({
  opacity: 0.6,
}));
