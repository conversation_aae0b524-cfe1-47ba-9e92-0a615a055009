import type { FC } from 'react';
import type { ddValue } from 'src/shared/types/ddValue';

import { useFormikContext } from 'formik';

import { Grid, Divider, Typography } from '@mui/material';

import { Icons } from 'src/components/common/icons/iconify/Icons';
import CustomSelect from 'src/components/common/forms/select/Select';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  FormPaper,
  InfoAlert,
  InfoAlertIcon,
  SectionDivider,
  InfoSectionIcon,
  InfoSectionHeader,
  ContentFormWrapper,
} from './ContentForm.style';

interface ContentFormValues {
  contentType: string;
  contentDescription: string;
  weight: string;
  weightUnit: string;
  length: string;
  width: string;
  height: string;
  shipmentValue: string;
}

interface ContentFormProps {
  contentTypeOptions: ddValue[];
  weightUnitOptions: ddValue[];
}

const ContentForm: FC<ContentFormProps> = ({ contentTypeOptions, weightUnitOptions }) => {
  const { values, handleChange, handleBlur, errors, touched } =
    useFormikContext<ContentFormValues>();

  const l = parseFloat(values.length) || 0;
  const w = parseFloat(values.width) || 0;
  const h = parseFloat(values.height) || 0;
  const volumetricWeight = l && w && h ? ((l * w * h) / 4750).toFixed(2) : '';

  return (
    <ContentFormWrapper container spacing={3}>
      <Grid item xs={12}>
        <FormPaper elevation={0}>
          <Grid container spacing={2}>
            {/* Content Information Section */}
            <Grid item xs={12}>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.content} width={18} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Content Information
                </Typography>
              </InfoSectionHeader>
            </Grid>

            <Grid item xs={12} md={6}>
              <CustomSelect
                id="contentType"
                name="contentType"
                label="Content Type"
                options={contentTypeOptions}
                value={values.contentType}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.contentType && touched.contentType)}
                helperText={touched.contentType ? errors.contentType : ''}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <CustomTextField
                id="contentDescription"
                name="contentDescription"
                label="Content Description"
                placeholder="Describe what you are shipping in detail"
                variant="outlined"
                type="text"
                value={values.contentDescription}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.contentDescription && touched.contentDescription)}
                helperText={touched.contentDescription ? errors.contentDescription : ''}
                required
                multiline
                minRows={3}
              />
            </Grid>

            <Grid item xs={12}>
              <SectionDivider>
                <Divider />
              </SectionDivider>
            </Grid>

            {/* Weight Information Section */}
            <Grid item xs={12}>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.weight} width={18} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Weight Information
                </Typography>
              </InfoSectionHeader>
            </Grid>

            <Grid item xs={12} md={6}>
              <CustomTextField
                id="weight"
                name="weight"
                label="Weight"
                placeholder="Enter package weight"
                variant="outlined"
                type="number"
                value={values.weight}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.weight && touched.weight)}
                helperText={touched.weight ? errors.weight : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <CustomSelect
                id="weightUnit"
                name="weightUnit"
                label="Weight Unit"
                options={weightUnitOptions}
                value={values.weightUnit}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.weightUnit && touched.weightUnit)}
                helperText={touched.weightUnit ? errors.weightUnit : ''}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <SectionDivider>
                <Divider />
              </SectionDivider>
            </Grid>

            {/* Dimensions Section */}
            <Grid item xs={12}>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.dimensions} width={18} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Package Dimensions
                </Typography>
              </InfoSectionHeader>
            </Grid>

            <Grid item xs={12} md={4}>
              <CustomTextField
                id="length"
                name="length"
                label="Length (cm)"
                variant="outlined"
                type="number"
                value={values.length}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.length && touched.length)}
                helperText={touched.length ? errors.length : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <CustomTextField
                id="width"
                name="width"
                label="Width (cm)"
                variant="outlined"
                type="number"
                value={values.width}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.width && touched.width)}
                helperText={touched.width ? errors.width : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <CustomTextField
                id="height"
                name="height"
                label="Height (cm)"
                variant="outlined"
                type="number"
                value={values.height}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.height && touched.height)}
                helperText={touched.height ? errors.height : ''}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <SectionDivider>
                <Divider />
              </SectionDivider>
            </Grid>

            {/* Value Section */}
            <Grid item xs={12}>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.pricing} width={18} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Shipment Value
                </Typography>
              </InfoSectionHeader>
            </Grid>

            <Grid item xs={12} md={6}>
              <CustomTextField
                id="shipmentValue"
                name="shipmentValue"
                label="Declared Value"
                placeholder="Enter the value of your shipment"
                variant="outlined"
                type="number"
                value={values.shipmentValue}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.shipmentValue && touched.shipmentValue)}
                helperText={touched.shipmentValue ? errors.shipmentValue : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <CustomTextField
                id="volumetricWeight"
                name="volumetricWeight"
                label="Volumetric Weight"
                variant="outlined"
                type="text"
                value={volumetricWeight}
                handleChange={() => {}}
                handleBlur={() => {}}
                error={false}
                disabled
                helperText="Auto-calculated based on dimensions"
              />
            </Grid>

            <Grid item xs={12}>
              <InfoAlert>
                <InfoAlertIcon>
                  <Iconify icon="mdi:information" width={24} />
                </InfoAlertIcon>
                <Typography variant="body2" color="text.secondary">
                  The shipping rate will be calculated based on either the actual weight or
                  volumetric weight, whichever is higher.
                </Typography>
              </InfoAlert>
            </Grid>
          </Grid>
        </FormPaper>
      </Grid>
    </ContentFormWrapper>
  );
};

export default ContentForm;
