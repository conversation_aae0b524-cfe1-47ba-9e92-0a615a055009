import { styled } from '@mui/material/styles';
import { Box, Grid, Paper, Alert, Divider, Typography, type GridProps } from '@mui/material';

export const LocationFormWrapper = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  ...theme.typography.body2,
}));

export const FormPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
}));

export const InfoSectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const InfoSectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const SectionDivider = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

export const InfoAlert = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: theme.palette.info.lighter,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export const InfoAlertIcon = styled(Box)(({ theme }) => ({
  color: theme.palette.info.main,
  marginRight: theme.spacing(1),
}));

export const FormGridRoot = styled(Grid)<GridProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

interface FormGridProps extends GridProps {
  fullWidth?: boolean;
}

export const FormGrid = styled(Grid)<FormGridProps>(({ theme, fullWidth }) => ({
  marginBottom: theme.spacing(2),
  ...(fullWidth && {
    width: '100%',
  }),
}));

export const FormTitle = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const FormTitleIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const StyledDivider = styled(Divider)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));

export const LocationPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.neutral,
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(3),
}));

export const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(1),
}));

export const SectionHeaderIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const SectionDescription = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  color: theme.palette.text.secondary,
}));

export const FieldLabelWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  marginBottom: theme.spacing(1),
}));

export const FieldLabelIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  marginTop: theme.spacing(1),
}));

export const FieldLabelContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
}));

export const FieldCaption = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
}));

export const DeliveryRegionSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
}));

export const DeliveryRegionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  marginBottom: theme.spacing(2),
}));

export const DeliveryRegionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  marginTop: theme.spacing(1),
  color: theme.palette.info.main,
}));

export const DeliveryTypeSection = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(4),
}));

export const DeliveryTypeHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const DeliveryTypeIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.info.main,
}));

export const AlertInfo = styled(Alert)(({ theme }) => ({
  marginBottom: theme.spacing(3),
}));
