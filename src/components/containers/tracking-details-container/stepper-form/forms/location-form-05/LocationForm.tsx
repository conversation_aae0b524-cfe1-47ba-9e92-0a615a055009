import type { FC } from 'react';
import type { ddValue } from 'src/shared/types/ddValue';
import type { LocationFormProps, LocationFormValues } from 'src/shared/types/locationForm.types';

import { useFormikContext } from 'formik';
import { useState, useEffect } from 'react';

import { Grid, Typography } from '@mui/material';

import { getMockDropOffLocations } from 'src/shared/services/mockDropOffLocations';

import CustomSelect from 'src/components/common/forms/select/Select';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';
import GoogleAddressAutocomplete from 'src/components/common/forms/autocomplete/GoogleAddressAutocomplete';

import { Icons, Iconify } from '../../../../../common/icons/iconify';
import {
  FormGrid,
  AlertInfo,
  FormGridRoot,
  SectionHeader,
  DeliveryTypeIcon,
  SectionHeaderIcon,
  SectionDescription,
  DeliveryTypeHeader,
  LocationFormWrapper,
  DeliveryTypeSection,
  DeliveryRegionSection,
} from './LocationForm.style';

const LocationForm: FC<LocationFormProps> = ({
  deliveryCountryTypeOptions = [],
  deliveryTypeOptions = [],
  dropOffOutletsOptions = [],
  shipmentTypeOptions = [],
}) => {
  const { values, handleChange, handleBlur, errors, touched, setFieldValue } =
    useFormikContext<LocationFormValues>();

  const [dropOffLocations, setDropOffLocations] = useState<ddValue[]>([]);

  useEffect(() => {
    getMockDropOffLocations().then(setDropOffLocations);
  }, []);

  console.log(values);
  useEffect(() => {
    if (values.pickupLocation && typeof values.pickupLocation === 'object') {
      const { address_components, formatted_address } = values.pickupLocation;

      if (address_components) {
        let streetNumber = '';
        let route = '';
        let city = '';
        let state = '';
        let zipCode = '';
        let country = '';

        address_components.forEach((component: any) => {
          const { types, long_name, short_name } = component;

          if (types.includes('street_number')) {
            streetNumber = long_name;
          } else if (types.includes('route')) {
            route = long_name;
          } else if (types.includes('locality') || types.includes('sublocality')) {
            city = long_name;
          } else if (types.includes('administrative_area_level_1')) {
            state = short_name;
          } else if (types.includes('postal_code')) {
            zipCode = long_name;
          } else if (types.includes('country')) {
            country = long_name;
          }
        });

        // Update form fields

        if (streetNumber && route) {
          setFieldValue('pickupAddressLine1', `${streetNumber} ${route}`.trim(), true);
        } else if (route) {
          setFieldValue('pickupAddressLine1', route, true);
        }

        if (city) setFieldValue('pickupCity', city, true);
        if (state) setFieldValue('pickupState', state, true);
        if (zipCode) setFieldValue('pickupZipCode', zipCode, true);
        if (country) setFieldValue('pickupCountry', country, true);
      } else if (formatted_address) {
        setFieldValue('pickupAddressLine1', formatted_address, true);
      }
    }
  }, [values.pickupLocation, setFieldValue]);

  useEffect(() => {
    if (values.deliveryLocation && typeof values.deliveryLocation === 'object') {
      const { address_components, formatted_address } = values.deliveryLocation;

      if (address_components) {
        let streetNumber = '';
        let route = '';
        let city = '';
        let state = '';
        let zipCode = '';
        let country = '';

        address_components.forEach((component: any) => {
          const { types, long_name, short_name } = component;

          if (types.includes('street_number')) {
            streetNumber = long_name;
          } else if (types.includes('route')) {
            route = long_name;
          } else if (types.includes('locality') || types.includes('sublocality')) {
            city = long_name;
          } else if (types.includes('administrative_area_level_1')) {
            state = short_name;
          } else if (types.includes('postal_code')) {
            zipCode = long_name;
          } else if (types.includes('country')) {
            country = long_name;
          }
        });

        // Update form fields

        if (streetNumber && route) {
          setFieldValue('deliveryAddressLine1', `${streetNumber} ${route}`.trim(), true);
        } else if (route) {
          setFieldValue('deliveryAddressLine1', route, true);
        }

        if (city) setFieldValue('deliveryCity', city, true);
        if (state) setFieldValue('deliveryState', state, true);
        if (zipCode) setFieldValue('deliveryZipCode', zipCode, true);
        if (country) setFieldValue('deliveryCountry', country, true);
      } else if (formatted_address) {
        setFieldValue('deliveryAddressLine1', formatted_address, true);
      }
    }
  }, [values.deliveryLocation, setFieldValue]);

  return (
    <LocationFormWrapper>
      <FormGridRoot>
        {/* Shipment Type Section */}
        <FormGrid fullWidth>
          <SectionHeader>
            <SectionHeaderIcon>
              <Iconify icon="mdi:truck-delivery-outline" width={20} height={20} />
            </SectionHeaderIcon>
            <Typography variant="subtitle1">Shipment Type</Typography>
          </SectionHeader>
          <SectionDescription variant="body2">
            Select how you want to ship your package
          </SectionDescription>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <CustomSelect
                id="shipmentType"
                name="shipmentType"
                label="Shipment Type"
                options={shipmentTypeOptions}
                value={String(values.shipmentType)}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.shipmentType && touched.shipmentType)}
                helperText={
                  touched.shipmentType && typeof errors.shipmentType === 'string'
                    ? errors.shipmentType
                    : ''
                }
                required
              />
            </Grid>
          </Grid>
        </FormGrid>

        {/* Pickup or Drop-off Location Section */}
        {values.shipmentType === 1 ? (
          <FormGrid fullWidth>
            <SectionHeader>
              <SectionHeaderIcon>
                <Iconify icon="mdi:map-marker-radius" width={20} height={20} />
              </SectionHeaderIcon>
              <Typography variant="subtitle1">Drop-off Location</Typography>
            </SectionHeader>
            <SectionDescription variant="body2">
              Select a drop-off location from the list below
            </SectionDescription>
            <AlertInfo severity="info" sx={{ mb: 3 }}>
              Choose a convenient drop-off center for your shipment
            </AlertInfo>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <CustomSelect
                  id="selectedDropOffLocation"
                  name="selectedDropOffLocation"
                  label="Drop-off Location"
                  options={dropOffLocations.map((loc) => ({
                    id: loc.id,
                    name: loc.name,
                    isDisabled: loc.isDisabled,
                  }))}
                  value={values.selectedDropOffLocation}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.selectedDropOffLocation && touched.selectedDropOffLocation)}
                  helperText={
                    touched.selectedDropOffLocation &&
                    typeof errors.selectedDropOffLocation === 'string'
                      ? errors.selectedDropOffLocation
                      : ''
                  }
                  required
                />
              </Grid>
            </Grid>
          </FormGrid>
        ) : (
          <FormGrid fullWidth>
            {/* Pickup Location Section */}
            <SectionHeader>
              <SectionHeaderIcon>
                <Iconify icon="mdi:map-marker-radius" width={20} height={20} />
              </SectionHeaderIcon>
              <Typography variant="subtitle1">Pickup Location</Typography>
            </SectionHeader>
            <SectionDescription variant="body2">
              Enter the address where your shipment will be picked up from
            </SectionDescription>

            <AlertInfo severity="info" sx={{ mb: 3 }}>
              Use the address search below to automatically fill in the address details
            </AlertInfo>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <GoogleAddressAutocomplete
                  name="pickupLocation"
                  placeholder="Search for pickup address"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <CustomTextField
                  id="pickupAddressLine1"
                  name="pickupAddressLine1"
                  label="Address Line 1"
                  variant="outlined"
                  type="text"
                  value={values.pickupAddressLine1}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.pickupAddressLine1 && touched.pickupAddressLine1)}
                  helperText={
                    touched.pickupAddressLine1 && typeof errors.pickupAddressLine1 === 'string'
                      ? errors.pickupAddressLine1
                      : ''
                  }
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <CustomTextField
                  id="pickupAddressLine2"
                  name="pickupAddressLine2"
                  label="Address Line 2"
                  variant="outlined"
                  type="text"
                  value={values.pickupAddressLine2}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.pickupAddressLine2 && touched.pickupAddressLine2)}
                  helperText={
                    touched.pickupAddressLine2 && typeof errors.pickupAddressLine2 === 'string'
                      ? errors.pickupAddressLine2
                      : ''
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <CustomTextField
                  id="pickupCity"
                  name="pickupCity"
                  label="City"
                  variant="outlined"
                  type="text"
                  value={values.pickupCity}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.pickupCity && touched.pickupCity)}
                  helperText={
                    touched.pickupCity && typeof errors.pickupCity === 'string'
                      ? errors.pickupCity
                      : ''
                  }
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <CustomTextField
                  id="pickupState"
                  name="pickupState"
                  label="State / Province / Region"
                  variant="outlined"
                  type="text"
                  value={values.pickupState}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.pickupState && touched.pickupState)}
                  helperText={
                    touched.pickupState && typeof errors.pickupState === 'string'
                      ? errors.pickupState
                      : ''
                  }
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <CustomTextField
                  id="pickupZipCode"
                  name="pickupZipCode"
                  label="ZIP / Postal Code"
                  variant="outlined"
                  type="text"
                  value={values.pickupZipCode}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.pickupZipCode && touched.pickupZipCode)}
                  helperText={
                    touched.pickupZipCode && typeof errors.pickupZipCode === 'string'
                      ? errors.pickupZipCode
                      : ''
                  }
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <CustomTextField
                  id="pickupCountry"
                  name="pickupCountry"
                  label="Country"
                  variant="outlined"
                  type="text"
                  value={values.pickupCountry}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.pickupCountry && touched.pickupCountry)}
                  helperText={
                    touched.pickupCountry && typeof errors.pickupCountry === 'string'
                      ? errors.pickupCountry
                      : ''
                  }
                  required
                />
              </Grid>
            </Grid>
          </FormGrid>
        )}

        <FormGrid fullWidth>
          <SectionHeader>
            <SectionHeaderIcon>
              <Iconify icon={Icons.tracking.dropoff} width={20} height={20} />
            </SectionHeaderIcon>
            <Typography variant="subtitle1">Delivery Location</Typography>
          </SectionHeader>
          <SectionDescription variant="body2">
            Enter the address where your shipment will be delivered to
          </SectionDescription>

          <AlertInfo severity="info" sx={{ mb: 3 }}>
            Use the address search below to automatically fill in the address details
          </AlertInfo>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <GoogleAddressAutocomplete
                name="deliveryLocation"
                placeholder="Search for delivery address"
                required
              />
            </Grid>

            <Grid item xs={12}>
              <CustomTextField
                id="deliveryAddressLine1"
                name="deliveryAddressLine1"
                label="Address Line 1"
                variant="outlined"
                type="text"
                value={values.deliveryAddressLine1}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.deliveryAddressLine1 && touched.deliveryAddressLine1)}
                helperText={
                  touched.deliveryAddressLine1 && typeof errors.deliveryAddressLine1 === 'string'
                    ? errors.deliveryAddressLine1
                    : ''
                }
                required
              />
            </Grid>
            <Grid item xs={12}>
              <CustomTextField
                id="deliveryAddressLine2"
                name="deliveryAddressLine2"
                label="Address Line 2"
                variant="outlined"
                type="text"
                value={values.deliveryAddressLine2}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.deliveryAddressLine2 && touched.deliveryAddressLine2)}
                helperText={
                  touched.deliveryAddressLine2 && typeof errors.deliveryAddressLine2 === 'string'
                    ? errors.deliveryAddressLine2
                    : ''
                }
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="deliveryCity"
                name="deliveryCity"
                label="City"
                variant="outlined"
                type="text"
                value={values.deliveryCity}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.deliveryCity && touched.deliveryCity)}
                helperText={
                  touched.deliveryCity && typeof errors.deliveryCity === 'string'
                    ? errors.deliveryCity
                    : ''
                }
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="deliveryState"
                name="deliveryState"
                label="State / Province / Region"
                variant="outlined"
                type="text"
                value={values.deliveryState}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.deliveryState && touched.deliveryState)}
                helperText={
                  touched.deliveryState && typeof errors.deliveryState === 'string'
                    ? errors.deliveryState
                    : ''
                }
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="deliveryZipCode"
                name="deliveryZipCode"
                label="ZIP / Postal Code"
                variant="outlined"
                type="text"
                value={values.deliveryZipCode}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.deliveryZipCode && touched.deliveryZipCode)}
                helperText={
                  touched.deliveryZipCode && typeof errors.deliveryZipCode === 'string'
                    ? errors.deliveryZipCode
                    : ''
                }
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="deliveryCountry"
                name="deliveryCountry"
                label="Country"
                variant="outlined"
                type="text"
                value={values.deliveryCountry}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.deliveryCountry && touched.deliveryCountry)}
                helperText={
                  touched.deliveryCountry && typeof errors.deliveryCountry === 'string'
                    ? errors.deliveryCountry
                    : ''
                }
                required
              />
            </Grid>
          </Grid>

          <DeliveryRegionSection>
            <CustomSelect
              id="deliveryCountryType"
              name="deliveryCountryType"
              label="Delivery Country Type"
              options={deliveryCountryTypeOptions}
              value={values.deliveryCountryType}
              handleChange={handleChange}
              handleBlur={handleBlur}
              error={Boolean(errors.deliveryCountryType && touched.deliveryCountryType)}
              helperText={
                touched.deliveryCountryType && typeof errors.deliveryCountryType === 'string'
                  ? errors.deliveryCountryType
                  : ''
              }
              required
            />
          </DeliveryRegionSection>
        </FormGrid>

        <FormGrid fullWidth>
          <SectionHeader>
            <SectionHeaderIcon>
              <Iconify icon="mdi:account" width={20} height={20} />
            </SectionHeaderIcon>
            <Typography variant="subtitle1">Recipient Details</Typography>
          </SectionHeader>
          <SectionDescription variant="body2">
            Enter the name and contact details of the person receiving the shipment
          </SectionDescription>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="recipientName"
                name="recipientName"
                label="Recipient Name"
                variant="outlined"
                type="text"
                value={values.recipientName}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.recipientName && touched.recipientName)}
                helperText={
                  touched.recipientName && typeof errors.recipientName === 'string'
                    ? errors.recipientName
                    : ''
                }
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CustomTextField
                id="recipientPhone"
                name="recipientPhone"
                label="Recipient Phone"
                variant="outlined"
                type="text"
                value={values.recipientPhone}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.recipientPhone && touched.recipientPhone)}
                helperText={
                  touched.recipientPhone && typeof errors.recipientPhone === 'string'
                    ? errors.recipientPhone
                    : ''
                }
              />
            </Grid>
          </Grid>

          <DeliveryTypeSection>
            <DeliveryTypeHeader>
              <DeliveryTypeIcon>
                <Iconify icon="mdi:truck-delivery" width={20} height={20} />
              </DeliveryTypeIcon>
              <Typography variant="subtitle2">Delivery Type</Typography>
            </DeliveryTypeHeader>
            <CustomSelect
              id="deliveryType"
              name="deliveryType"
              label="Delivery Type"
              options={deliveryTypeOptions}
              value={values.deliveryType}
              handleChange={handleChange}
              handleBlur={handleBlur}
              error={Boolean(errors.deliveryType && touched.deliveryType)}
              helperText={
                touched.deliveryType && typeof errors.deliveryType === 'string'
                  ? errors.deliveryType
                  : ''
              }
              required
            />
          </DeliveryTypeSection>

          <DeliveryTypeSection>
            {values.deliveryType === 'door-to-door' && (
              <AlertInfo severity="info">
                The package will be delivered directly to the recipient&apos;s address.
              </AlertInfo>
            )}

            {values.deliveryType === 'port-to-port' && (
              <AlertInfo severity="info">
                The package will be delivered to the nearest port or terminal for pickup.
              </AlertInfo>
            )}
          </DeliveryTypeSection>
        </FormGrid>
      </FormGridRoot>
    </LocationFormWrapper>
  );
};

export default LocationForm;
