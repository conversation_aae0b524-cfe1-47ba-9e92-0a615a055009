import { styled } from '@mui/material/styles';
import {
  Box,
  Grid,
  Paper,
  Radio,
  Alert,
  Typography,
  type GridProps,
  type PaperProps,
} from '@mui/material';

export const ServiceCategoryFormWrapper = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  ...theme.typography.body2,
}));

export const FormPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
}));

export const InfoSectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const InfoSectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const WarningIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.warning.main,
}));

export const SectionDivider = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

export const InfoAlert = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: theme.palette.info.lighter,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export const InfoAlertIcon = styled(Box)(({ theme }) => ({
  color: theme.palette.info.main,
  marginRight: theme.spacing(1),
}));

export const FormGridRoot = styled(Grid)<GridProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

interface FormGridProps extends GridProps {
  fullWidth?: boolean;
}

export const FormGrid = styled(Grid)<FormGridProps>(({ theme, fullWidth }) => ({
  marginBottom: theme.spacing(2),
  ...(fullWidth && {
    width: '100%',
  }),
}));

export const ServiceOptionsPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.neutral,
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(3),
}));

export const ServiceCardDescription = styled(Box)(({ theme }) => ({
  color: theme.palette.text.secondary,
  flexGrow: 1,
}));

export const ServiceCardTitle = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  marginTop: theme.spacing(1),
  textAlign: 'center',
}));

interface ServiceCardProps extends PaperProps {
  selected?: boolean;
}

export const ServiceCard = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'selected',
})<ServiceCardProps>(({ theme, selected }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  backgroundColor: selected ? theme.palette.primary.lighter : theme.palette.background.paper,
  position: 'relative',
  transition: 'all 0.2s ease-in-out',
  cursor: 'pointer',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  '&:hover': {
    boxShadow: '0 0 0 1px rgba(0, 0, 0, 0.05)',
    borderColor: theme.palette.primary.light,
  },
  textAlign: 'center',
}));

export const CardRadio = styled(Radio)(({ theme }) => ({
  position: 'absolute',
  right: 8,
  top: 8,
  '& .MuiSvgIcon-root': {
    fontSize: 20,
  },
}));

export const ProtectionBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
}));

export const ProtectionAlert = styled(Alert)(({ theme }) => ({
  marginTop: theme.spacing(2),
  fontSize: '0.875rem',
}));

export const RiskTypeBox = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(3),
}));

export const ServiceCardIcon = styled(Box)(({ theme }) => ({
  color: theme.palette.primary.main,
  marginBottom: theme.spacing(1),
}));

export const ProtectionDescription = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  color: theme.palette.text.secondary,
}));

export const ServiceTypeBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));
