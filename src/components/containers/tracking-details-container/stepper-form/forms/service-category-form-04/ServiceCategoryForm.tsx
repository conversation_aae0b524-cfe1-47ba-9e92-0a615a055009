import type { FC } from 'react';
import type { ddValue } from 'src/shared/types/ddValue';

import { useFormikContext } from 'formik';

import {
  Grid,
  Checkbox,
  RadioGroup,
  Typography,
  FormControl,
  FormHelperText,
  FormControlLabel,
} from '@mui/material';

import CustomSelect from 'src/components/common/forms/select/Select';

import { Iconify } from '../../../../../common/icons/iconify';
import {
  FormGrid,
  FormPaper,
  CardRadio,
  ServiceCard,
  WarningIcon,
  RiskTypeBox,
  FormGridRoot,
  ProtectionBox,
  InfoSectionIcon,
  ServiceCardIcon,
  ProtectionAlert,
  ServiceCardTitle,
  InfoSectionHeader,
  ProtectionDescription,
  ServiceCardDescription,
  ServiceCategoryFormWrapper,
} from './ServiceCategoryForm.style';

interface ServiceCategoryFormValues {
  serviceCategoryType: string;
  isShipmentAtRisk: boolean;
  riskChargeType: string;
}

interface ServiceCategoryFormProps {
  serviceCategoryTypeOptions: ddValue[];
  riskChargeTypeOptions: ddValue[];
}

const ServiceCategoryForm: FC<ServiceCategoryFormProps> = ({
  serviceCategoryTypeOptions,
  riskChargeTypeOptions,
}) => {
  const { values, handleChange, handleBlur, setFieldValue, errors, touched } =
    useFormikContext<ServiceCategoryFormValues>();

  return (
    <ServiceCategoryFormWrapper container spacing={3}>
      <Grid item xs={12}>
        <FormPaper elevation={0}>
          <FormGridRoot>
            <FormGrid>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon="mdi:package-variant-closed-check" width={24} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="600">
                  Select Shipping Service
                </Typography>
              </InfoSectionHeader>

              <FormControl
                fullWidth
                error={Boolean(errors.serviceCategoryType && touched.serviceCategoryType)}
              >
                <RadioGroup
                  name="serviceCategoryType"
                  value={values.serviceCategoryType}
                  onChange={handleChange}
                  onBlur={handleBlur}
                >
                  <Grid container spacing={2}>
                    {serviceCategoryTypeOptions.map((option) => (
                      <Grid item xs={12} sm={6} md={4} key={option.id}>
                        <ServiceCard
                          elevation={0}
                          selected={values.serviceCategoryType === option.id.toString()}
                          onClick={() => setFieldValue('serviceCategoryType', option.id.toString())}
                        >
                          <CardRadio
                            checked={values.serviceCategoryType === option.id.toString()}
                            value={option.id.toString()}
                            name="serviceCategoryType"
                          />
                          <ServiceCardTitle>
                            <ServiceCardIcon>
                              <Iconify icon={getServiceIcon(option.name)} width={36} height={36} />
                            </ServiceCardIcon>
                            <Typography variant="subtitle2">{option.name}</Typography>
                          </ServiceCardTitle>
                          <ServiceCardDescription>
                            <Typography variant="caption">
                              {getServiceDescription(option.name)}
                            </Typography>
                          </ServiceCardDescription>
                        </ServiceCard>
                      </Grid>
                    ))}
                  </Grid>
                </RadioGroup>
                {Boolean(errors.serviceCategoryType && touched.serviceCategoryType) && (
                  <FormHelperText>{errors.serviceCategoryType}</FormHelperText>
                )}
              </FormControl>
            </FormGrid>

            <FormGrid fullWidth>
              <InfoSectionHeader>
                <WarningIcon>
                  <Iconify icon="mdi:shield-check" width={24} />
                </WarningIcon>
                <Typography variant="subtitle1" fontWeight="600">
                  Shipment Protection
                </Typography>
              </InfoSectionHeader>

              <ProtectionDescription variant="body2">
                Add protection to secure your shipment against loss or damage
              </ProtectionDescription>

              <ProtectionBox>
                <FormControlLabel
                  control={
                    <Checkbox
                      id="isShipmentAtRisk"
                      name="isShipmentAtRisk"
                      checked={values.isShipmentAtRisk}
                      onChange={(e) => setFieldValue('isShipmentAtRisk', e.target.checked)}
                      color="primary"
                    />
                  }
                  label={
                    <Typography variant="body2">
                      Protect this shipment against loss/damage
                    </Typography>
                  }
                />
              </ProtectionBox>

              {!values.isShipmentAtRisk && (
                <ProtectionAlert severity="info">
                  No protection selected. Your shipment won&apos;t be covered in case of loss or
                  damage.
                </ProtectionAlert>
              )}

              {values.isShipmentAtRisk && (
                <RiskTypeBox>
                  <InfoSectionHeader>
                    <WarningIcon>
                      <Iconify icon="mdi:shield-check-outline" width={22} />
                    </WarningIcon>
                    <Typography variant="subtitle2" fontWeight="600">
                      Select Risk Type
                    </Typography>
                  </InfoSectionHeader>

                  <CustomSelect
                    id="riskChargeType"
                    name="riskChargeType"
                    label="Risk Surcharge Type"
                    options={riskChargeTypeOptions}
                    value={values.riskChargeType}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                    error={Boolean(errors.riskChargeType && touched.riskChargeType)}
                    helperText={touched.riskChargeType ? errors.riskChargeType : ''}
                    required={false}
                  />
                </RiskTypeBox>
              )}
            </FormGrid>
          </FormGridRoot>
        </FormPaper>
      </Grid>
    </ServiceCategoryFormWrapper>
  );
};

export default ServiceCategoryForm;

// Helper functions to provide appropriate icons and descriptions

const getServiceIcon = (serviceName: string): string => {
  const name = serviceName.toLowerCase();
  if (name.includes('express')) return 'mdi:truck-fast';
  if (name.includes('standard')) return 'mdi:truck-delivery';
  if (name.includes('economy')) return 'mdi:truck';
  if (name.includes('premium')) return 'mdi:package-variant-plus';
  if (name.includes('next day')) return 'mdi:clock-fast';
  return 'mdi:package-variant';
};

const getServiceDescription = (serviceName: string): string => {
  const name = serviceName.toLowerCase();
  if (name.includes('express')) return 'Fast delivery with priority handling';
  if (name.includes('standard')) return 'Regular delivery with standard handling';
  if (name.includes('economy')) return 'Cost-effective delivery option';
  if (name.includes('premium')) return 'Premium service with guaranteed delivery times';
  if (name.includes('next day')) return 'Delivery by next business day';
  return 'Standard shipping service';
};
