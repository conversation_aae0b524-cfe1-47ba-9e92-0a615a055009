import React from 'react';
import { useFormikContext } from 'formik';

import { Typography } from '@mui/material';

import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';
import ReviewComponent from 'src/components/containers/tracking-details-container/stepper-form/forms/review-component-07/ReviewComponent';

import { Iconify } from '../../../../../common/icons/iconify';
import {
  FormGrid,
  PriceIcon,
  PriceAlert,
  FormWrapper,
  FormGridRoot,
  PricingPaper,
  PriceContent,
  SectionHeader,
  PriceDetailsBox,
  SectionHeaderIcon,
  SectionDescription,
} from './PricingForm.style';

const PricingForm: React.FC = () => {
  const { values, handleChange, handleBlur, errors, touched } = useFormikContext<any>();

  return (
    <FormWrapper>
      <FormGridRoot>
        <FormGrid fullWidth>
          <PricingPaper elevation={0}>
            <SectionHeader>
              <SectionHeaderIcon>
                <Iconify icon="mdi:currency-usd" width={20} height={20} />
              </SectionHeaderIcon>
              <Typography variant="subtitle1">Price Details</Typography>
            </SectionHeader>
            <SectionDescription variant="body2">
              Enter the total price for this shipment
            </SectionDescription>

            <PriceDetailsBox>
              <PriceIcon>
                <Iconify icon="mdi:cash-multiple" width={24} height={24} />
              </PriceIcon>
              <PriceContent>
                <CustomTextField
                  id="shipmentPrice"
                  name="shipmentPrice"
                  label="Shipment Price"
                  variant="outlined"
                  type="number"
                  value={values.shipmentPrice}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  error={Boolean(errors.shipmentPrice && touched.shipmentPrice)}
                  helperText={
                    touched.shipmentPrice && typeof errors.shipmentPrice === 'string'
                      ? errors.shipmentPrice
                      : ''
                  }
                  required
                />

                <PriceAlert severity="info">
                  This is the final price that will be charged for this shipment, including all
                  taxes and fees.
                </PriceAlert>
              </PriceContent>
            </PriceDetailsBox>
          </PricingPaper>
        </FormGrid>

        <FormGrid fullWidth>
          <PricingPaper elevation={0}>
            <ReviewComponent />
          </PricingPaper>
        </FormGrid>
      </FormGridRoot>
    </FormWrapper>
  );
};

export default PricingForm;
