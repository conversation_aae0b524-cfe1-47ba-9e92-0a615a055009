import { styled } from '@mui/material/styles';
import { Box, Grid, Paper, Alert, Typography, type GridProps } from '@mui/material';

export const FormWrapper = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  ...theme.typography.body2,
}));

export const FormPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
}));

export const InfoSectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const InfoSectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const SectionDivider = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

export const InfoAlert = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: theme.palette.info.lighter,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

export const InfoAlertIcon = styled(Box)(({ theme }) => ({
  color: theme.palette.info.main,
  marginRight: theme.spacing(1),
}));

export const FormGridRoot = styled(Grid)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

interface FormGridProps extends GridProps {
  fullWidth?: boolean;
}

export const FormGrid = styled(Grid)<FormGridProps>(({ theme, fullWidth }) => ({
  marginBottom: theme.spacing(2),
  ...(fullWidth && {
    width: '100%',
  }),
}));

export const PricingPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
}));

export const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(1),
}));

export const SectionHeaderIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.success.main,
}));

export const SectionDescription = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  color: theme.palette.text.secondary,
}));

export const PriceDetailsBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
}));

export const PriceIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(2),
  marginTop: theme.spacing(1),
  color: theme.palette.success.light,
}));

export const PriceContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
}));

export const PriceAlert = styled(Alert)(({ theme }) => ({
  marginTop: theme.spacing(2),
  fontSize: '0.875rem',
}));
