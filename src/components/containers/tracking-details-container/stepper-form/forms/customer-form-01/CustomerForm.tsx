import type { FC } from 'react';

import { useFormikContext } from 'formik';

import { Grid, Divider, Typography } from '@mui/material';

import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  FormPaper,
  SectionDivider,
  InfoSectionIcon,
  InfoSectionHeader,
  CustomerFormWrapper,
  AddressSectionHeader,
} from './CustomerForm.style';

interface CustomerFormValues {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddressLine1: string;
  customerAddressLine2: string;
  customerCity: string;
  customerState: string;
  customerCountry: string;
  customerPincode: string;
}

const CustomerForm: FC = () => {
  const { values, handleChange, handleBlur, errors, touched } =
    useFormikContext<CustomerFormValues>();

  return (
    <CustomerFormWrapper container spacing={3}>
      <Grid item xs={12}>
        <FormPaper elevation={0}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <InfoSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.customer} width={24} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Personal Information
                </Typography>
              </InfoSectionHeader>
            </Grid>

            <Grid item xs={12} md={4}>
              <CustomTextField
                id="customerName"
                name="customerName"
                label="Customer Name"
                variant="outlined"
                type="text"
                value={values.customerName}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerName && touched.customerName)}
                helperText={touched.customerName ? errors.customerName : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <CustomTextField
                id="customerEmail"
                name="customerEmail"
                label="Customer Email"
                variant="outlined"
                type="email"
                value={values.customerEmail}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerEmail && touched.customerEmail)}
                helperText={touched.customerEmail ? errors.customerEmail : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <CustomTextField
                id="customerPhone"
                name="customerPhone"
                label="Customer Phone"
                variant="outlined"
                type="text"
                value={values.customerPhone}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerPhone && touched.customerPhone)}
                helperText={touched.customerPhone ? errors.customerPhone : ''}
                maxLength={10}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <SectionDivider>
                <Divider />
              </SectionDivider>
            </Grid>

            <Grid item xs={12}>
              <AddressSectionHeader>
                <InfoSectionIcon>
                  <Iconify icon={Icons.tracking.address} width={18} />
                </InfoSectionIcon>
                <Typography variant="subtitle1" fontWeight="bold" color="text.secondary">
                  Address Information
                </Typography>
              </AddressSectionHeader>
            </Grid>

            <Grid item xs={12}>
              <CustomTextField
                id="customerAddressLine1"
                name="customerAddressLine1"
                label="Address Line 1"
                placeholder="Street address, building number, etc."
                variant="outlined"
                type="text"
                value={values.customerAddressLine1}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerAddressLine1 && touched.customerAddressLine1)}
                helperText={touched.customerAddressLine1 ? errors.customerAddressLine1 : ''}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <CustomTextField
                id="customerAddressLine2"
                name="customerAddressLine2"
                label="Address Line 2"
                placeholder="Apartment, suite, unit, etc. (optional)"
                variant="outlined"
                type="text"
                value={values.customerAddressLine2}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerAddressLine2 && touched.customerAddressLine2)}
                helperText={touched.customerAddressLine2 ? errors.customerAddressLine2 : ''}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <CustomTextField
                id="customerCity"
                name="customerCity"
                label="City"
                variant="outlined"
                type="text"
                value={values.customerCity}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerCity && touched.customerCity)}
                helperText={touched.customerCity ? errors.customerCity : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <CustomTextField
                id="customerState"
                name="customerState"
                label="State/Province"
                variant="outlined"
                type="text"
                value={values.customerState}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerState && touched.customerState)}
                helperText={touched.customerState ? errors.customerState : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <CustomTextField
                id="customerCountry"
                name="customerCountry"
                label="Country"
                variant="outlined"
                type="text"
                value={values.customerCountry}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerCountry && touched.customerCountry)}
                helperText={touched.customerCountry ? errors.customerCountry : ''}
                required
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <CustomTextField
                id="customerPincode"
                name="customerPincode"
                label="Pincode/ZIP"
                variant="outlined"
                type="text"
                value={values.customerPincode}
                handleChange={handleChange}
                handleBlur={handleBlur}
                error={Boolean(errors.customerPincode && touched.customerPincode)}
                helperText={touched.customerPincode ? errors.customerPincode : ''}
                maxLength={6}
                required
              />
            </Grid>
          </Grid>
        </FormPaper>
      </Grid>
    </CustomerFormWrapper>
  );
};

export default CustomerForm;
