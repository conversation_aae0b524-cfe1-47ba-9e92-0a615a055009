import { styled } from '@mui/material/styles';
import { Box, Grid, Paper } from '@mui/material';

export const CustomerFormWrapper = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  ...theme.typography.body2,
}));

export const FormPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[0],
}));

export const FormSectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1.5),
  color: theme.palette.primary.main,
}));

export const InfoSectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

export const InfoSectionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const SectionDivider = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

export const AddressSectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  marginTop: theme.spacing(2),
}));

export const AddressIcon = styled(Box)(({ theme }) => ({
  opacity: 0.6,
}));
