export interface CustomerFormValues {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddressLine1: string;
  customerAddressLine2: string;
  customerCity: string;
  customerState: string;
  customerCountry: string;
  customerPincode: string;
}

export interface PincodeFormValues {
  originPincode: string;
  destinationPincode: string;
}

export interface ContentFormValues {
  contentType: string;
  contentValue: string;
  contentDescription: string;
  weight: string;
  weightUnit: string;
  length: string;
  width: string;
  height: string;
  shipmentValue: string;
}

export interface ServiceCategoryFormValues {
  serviceType: string;
  packageType: string;
}

export interface LocationFormValues {
  pickupAddressLine1: string;
  pickupAddressLine2: string;
  pickupCity: string;
  pickupState: string;
  pickupZipCode: string;
  pickupCountry: string;
  deliveryAddressLine1: string;
  deliveryAddressLine2: string;
  deliveryCity: string;
  deliveryState: string;
  deliveryZipCode: string;
  deliveryCountry: string;
  recipientName: string;
  recipientPhone: string;
  deliveryCountryType: string;
  deliveryType: string;
}

export interface PricingFormValues {
  shippingCost: string;
  tax: string;
  totalCost: string;
}

export type ShipmentFormValues = CustomerFormValues &
  PincodeFormValues &
  ContentFormValues &
  ServiceCategoryFormValues &
  LocationFormValues &
  PricingFormValues;
