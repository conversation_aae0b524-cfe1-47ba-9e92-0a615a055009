import type { ShipmentTrackingFormValues } from 'src/utils/validation/shipment-tracking/shipment-tracking.values';

import { ValidationError } from 'yup';
import React, { useState, useEffect } from 'react';
import { Form, Formik, useFormikContext } from 'formik';

import Button from '@mui/material/Button';
import { Box, Alert, AlertTitle } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { shipmentTrackingInitialValues } from 'src/utils/validation/shipment-tracking/shipment-tracking.values';
import {
  packageDetailsSchema,
  serviceOptionsSchema,
  pickupDeliverySchema,
  paymentDetailsSchema,
  originDestinationSchema,
  customerInformationSchema,
} from 'src/utils/validation/shipment-tracking/shipment-tracking.schema';

import { Iconify } from 'src/components/common/icons/iconify';
import Loader from 'src/components/common/loader/loader/Loader';
import { ConfirmDialog } from 'src/components/common/confirm-dialog/ConfirmDialog';

import PricingForm from './forms/pricing-form-06/PricingForm';
import PincodeForm from './forms/pincode-form-02/PincodeForm';
import ContentForm from './forms/content-form-03/ContentForm';
import CustomerForm from './forms/customer-form-01/CustomerForm';
import LocationForm from './forms/location-form-05/LocationForm';
import CustomStepper from '../../../common/custom-stepper/CustomStepper';
import ServiceCategoryForm from './forms/service-category-form-04/ServiceCategoryForm';

const LOCAL_STORAGE_FORM_DATA_KEY = 'shipmentTrackingFormData';
const LOCAL_STORAGE_ACTIVE_STEP_KEY = 'shipmentTrackingActiveStep';

type StepConfigItem = {
  label: string;
  fields: string[];
  validationSchema: any;
  component: React.ReactNode;
};

type StepperFormContentProps = {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  stepConfig: StepConfigItem[];
  isReadyToPersist: boolean;
};

const StepperFormContent = ({
  activeStep,
  setActiveStep,
  stepConfig,
  isReadyToPersist,
}: StepperFormContentProps) => {
  const formik = useFormikContext<ShipmentTrackingFormValues>();

  useEffect(() => {
    if (isReadyToPersist) {
      localStorage.setItem(LOCAL_STORAGE_FORM_DATA_KEY, JSON.stringify(formik.values));
    }
  }, [formik.values, isReadyToPersist]);

  useEffect(() => {
    if (isReadyToPersist) {
      localStorage.setItem(LOCAL_STORAGE_ACTIVE_STEP_KEY, activeStep.toString());
    }
  }, [activeStep, isReadyToPersist]);

  const handleValidateStep = async (step: number) => {
    try {
      await stepConfig[step].validationSchema.validate(formik.values, { abortEarly: false });
      return { isValid: true, errors: {} };
    } catch (err) {
      if (err instanceof ValidationError) {
        const errors: Record<string, string> = {};
        err.inner.forEach((error) => {
          if (error.path && typeof error.message === 'string') {
            errors[error.path] = error.message;
          }
        });
        return { isValid: false, errors };
      }
      return { isValid: false, errors: { customerName: 'An unexpected error occurred.' } };
    }
  };

  return (
    <CustomStepper
      steps={stepConfig.map((s: any) => s.label)}
      activeStep={activeStep}
      setActiveStep={setActiveStep}
      stepFields={stepConfig.map((s: any) => s.fields)}
      onValidateStep={handleValidateStep}
      onFinish={() => {
        console.log('Form completed successfully');
      }}
      title="Create New Shipment"
      finishLabel="Submit Shipment"
    >
      {stepConfig[activeStep].component}
    </CustomStepper>
  );
};

const StepperForm = () => {
  const [initialValues, setInitialValues] = useState<ShipmentTrackingFormValues>(
    shipmentTrackingInitialValues
  );
  const [activeStep, setActiveStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isReadyToPersist, setIsReadyToPersist] = useState(false);
  const confirmLoad = useBoolean();

  useEffect(() => {
    const savedDataString = localStorage.getItem(LOCAL_STORAGE_FORM_DATA_KEY);
    const savedStepString = localStorage.getItem(LOCAL_STORAGE_ACTIVE_STEP_KEY);

    if (savedDataString && savedStepString) {
      try {
        const savedData = JSON.parse(savedDataString);

        const hasValues = Object.values(savedData).some((value) => {
          if (value === null) return false;
          if (typeof value === 'string' && value.trim() === '') return false;
          if (Array.isArray(value) && value.length === 0) return false;
          if (typeof value === 'object' && Object.keys(value).length === 0) return false;
          return true;
        });

        if (hasValues) {
          confirmLoad.onTrue();
        } else {
          setIsReadyToPersist(true);
        }
      } catch (error) {
        console.error('Error processing saved data:', error);
        localStorage.removeItem(LOCAL_STORAGE_FORM_DATA_KEY);
        localStorage.removeItem(LOCAL_STORAGE_ACTIVE_STEP_KEY);
        setIsReadyToPersist(true);
      }
    } else {
      setIsReadyToPersist(true);
    }
    setIsLoading(false);
  }, [confirmLoad]);

  const handleConfirmLoad = () => {
    try {
      const savedDataString = localStorage.getItem(LOCAL_STORAGE_FORM_DATA_KEY);
      const savedStepString = localStorage.getItem(LOCAL_STORAGE_ACTIVE_STEP_KEY);

      if (savedDataString) {
        const savedData = JSON.parse(savedDataString);
        setInitialValues(savedData);
      }

      if (savedStepString) {
        setActiveStep(parseInt(savedStepString, 10));
      }
    } catch (error) {
      console.error('Failed to load saved form data from localStorage', error);
    }
    confirmLoad.onFalse();
    setIsReadyToPersist(true);
  };

  const handleCancelLoad = () => {
    localStorage.removeItem(LOCAL_STORAGE_FORM_DATA_KEY);
    localStorage.removeItem(LOCAL_STORAGE_ACTIVE_STEP_KEY);
    confirmLoad.onFalse();
    setIsReadyToPersist(true);
  };

  const contentTypeOptions = [
    { id: 1, name: 'Document', isDisabled: false },
    { id: 2, name: 'Non-Document', isDisabled: false },
  ];
  const weightUnitOptions = [
    { id: 1, name: 'Kilograms (kg)', isDisabled: false },
    { id: 3, name: 'Grams (g)', isDisabled: false },
  ];
  const serviceCategoryTypeOptions = [
    { id: 1, name: 'Standard Delivery', isDisabled: false },
    { id: 2, name: 'Express Delivery', isDisabled: false },
    { id: 3, name: 'Same Day Delivery', isDisabled: false },
    { id: 4, name: 'International Shipping', isDisabled: false },
  ];
  const riskChargeTypeOptions = [
    { id: 1, name: 'None', isDisabled: false },
    { id: 2, name: 'Low', isDisabled: false },
    { id: 3, name: 'Medium', isDisabled: false },
    { id: 4, name: 'High', isDisabled: false },
  ];
  const shipmentTypeOptions = [
    { id: 1, name: 'Drop Off', isDisabled: false },
    { id: 2, name: 'Courier Pickup', isDisabled: false },
  ];
  const deliveryTypeOptions = [
    { id: 1, name: 'door-to-door', label: 'Door to Door', isDisabled: false },
    { id: 2, name: 'port-to-port', label: 'Port to Port', isDisabled: false },
  ];
  const deliveryCountryTypeOptions = [
    { id: 1, name: 'domestic', label: 'Domestic', isDisabled: false },
    { id: 2, name: 'international', label: 'International', isDisabled: false },
  ];

  const stepConfig: StepConfigItem[] = [
    {
      label: 'Customer Information',
      fields: [
        'customerName',
        'customerEmail',
        'customerPhone',
        'customerAddressLine1',
        'customerAddressLine2',
        'customerCity',
        'customerState',
        'customerCountry',
        'customerPincode',
      ],
      validationSchema: customerInformationSchema,
      component: <CustomerForm key="customer" />,
    },
    {
      label: 'Origin & Destination',
      fields: ['originPincode', 'destinationPincode'],
      validationSchema: originDestinationSchema,
      component: <PincodeForm key="pincode" />,
    },
    {
      label: 'Package Details',
      fields: ['contentType', 'contentValue', 'contentDescription'],
      validationSchema: packageDetailsSchema,
      component: (
        <ContentForm
          key="content"
          contentTypeOptions={contentTypeOptions}
          weightUnitOptions={weightUnitOptions}
        />
      ),
    },
    {
      label: 'Service Options',
      fields: ['serviceType', 'packageType'],
      validationSchema: serviceOptionsSchema,
      component: (
        <ServiceCategoryForm
          key="service"
          serviceCategoryTypeOptions={serviceCategoryTypeOptions}
          riskChargeTypeOptions={riskChargeTypeOptions}
        />
      ),
    },
    {
      label: 'Pickup & Delivery',
      fields: [
        'shipmentType',
        'pickupLocation',
        'pickupAddressLine1',
        'pickupCity',
        'pickupState',
        'pickupZipCode',
        'pickupCountry',
        'deliveryLocation',
        'deliveryAddressLine1',
        'deliveryCity',
        'deliveryState',
        'deliveryZipCode',
        'deliveryCountry',
        'recipientName',
      ],
      validationSchema: pickupDeliverySchema,
      component: (
        <LocationForm
          key="location"
          shipmentTypeOptions={shipmentTypeOptions}
          deliveryTypeOptions={deliveryTypeOptions}
          deliveryCountryTypeOptions={deliveryCountryTypeOptions}
        />
      ),
    },
    {
      label: 'Payment Details',
      fields: ['shippingCost', 'tax', 'totalCost'],
      validationSchema: paymentDetailsSchema,
      component: <PricingForm key="pricing" />,
    },
  ];

  const handleSubmit = (values: ShipmentTrackingFormValues) => {
    console.log('Form Submitted:', values);
    localStorage.removeItem(LOCAL_STORAGE_FORM_DATA_KEY);
    localStorage.removeItem(LOCAL_STORAGE_ACTIVE_STEP_KEY);
  };

  if (isLoading) {
    return <Loader loading={isLoading} />;
  }

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validateOnMount={false}
        enableReinitialize
      >
        <Form>
          <StepperFormContent
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            stepConfig={stepConfig}
            isReadyToPersist={isReadyToPersist}
          />
        </Form>
      </Formik>
      <ConfirmDialog
        open={confirmLoad.value}
        onClose={handleCancelLoad}
        title="Continue where you left off?"
        content={
          <Box>
            You have unsaved changes from your last session. Would you like to continue?
            <Alert severity="warning" sx={{ mt: 2 }} icon={<Iconify icon="mdi:alert" width={16} />}>
              <AlertTitle>Caution</AlertTitle>
              If you cancel, all your previously entered data will be lost.
            </Alert>
          </Box>
        }
        action={
          <Button variant="contained" color="primary" onClick={handleConfirmLoad}>
            Continue
          </Button>
        }
      />
    </>
  );
};

export default StepperForm;
