import type { Errors } from 'src/shared/types/errorResonse';
import type { TFormEvent, TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { useState } from 'react';
import { Link } from 'react-router-dom';

import { Stack, Button, Divider, Typography } from '@mui/material';

import { paths } from 'src/routes/paths';

import { validate } from 'src/utils/validation/auth/form-validation';

import { useToast } from 'src/providers/ToastProvider';
import { forgotPassword } from 'src/shared/services/auth/auth.service';

import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  ForgotPassForm,
  ForgotPassWrapper,
  ForgotPassFormTitle,
  ForgotPassLoadingButton,
} from './ForgotPasswordContainer.style';

const ForgotPasswordContainer = () => {
  const { showToast } = useToast();
  const [isEmailTriggered, setIsEmailTriggered] = useState<boolean>(false);
  const [email, setEmail] = useState('');
  const [formError, setFormError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleEmailChange = (e: TInputChangeEvent) => {
    setEmail(e.target.value);
    setFormError('');
  };

  const handleEmailBlur = () => {
    const { isValidEmail, emailErrorMsg } = validate.email(email);
    if (!isValidEmail) {
      setFormError(emailErrorMsg);
    }
  };

  const handleSubmit = async (e: TFormEvent) => {
    e.preventDefault();

    const { isValidEmail, emailErrorMsg } = validate.email(email);
    if (!isValidEmail) {
      setFormError(emailErrorMsg);
      return;
    }

    setLoading(true);
    setFormError('');

    try {
      const response = await forgotPassword(email);
      showToast(response as string, 'success');
      setIsEmailTriggered(true);
      setEmail('');
    } catch (error) {
      const apiError = error as Errors;
      const errorMessage =
        typeof apiError.errors === 'string'
          ? apiError.errors
          : Object.values(apiError.errors)[0] || 'An unknown error occurred';

      if (errorMessage) {
        if (typeof errorMessage === 'string') {
          showToast(errorMessage, 'error');
        } else {
          Object.values(errorMessage).forEach((message) => {
            showToast(message as string, 'error');
          });
        }
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <ForgotPassWrapper>
      {!isEmailTriggered ? (
        <ForgotPassForm onSubmit={handleSubmit}>
          <ForgotPassFormTitle variant="subtitle1">Enter your email</ForgotPassFormTitle>

          <Stack direction="column" spacing={2}>
            <CustomTextField
              id="email"
              name="email"
              label="Email Address"
              type="email"
              value={email}
              variant="outlined"
              handleChange={handleEmailChange}
              error={!!formError}
              helperText={formError}
              handleBlur={handleEmailBlur}
            />

            <ForgotPassLoadingButton
              fullWidth
              size="large"
              type="submit"
              variant="contained"
              loading={loading}
              disabled={loading}
            >
              Get Reset Password Link
            </ForgotPassLoadingButton>

            <Divider>
              <Typography variant="caption">OR</Typography>
            </Divider>

            <Link to={paths.auth.login}>
              <Button variant="soft" fullWidth>
                Back to sign-in
              </Button>
            </Link>
          </Stack>
        </ForgotPassForm>
      ) : (
        <Stack spacing={2} alignItems="center">
          <Typography variant="h6">Check your email</Typography>
          <Typography variant="body2" textAlign="center">
            We have sent a password reset link to your email address.
          </Typography>
          <Link to={paths.auth.login}>
            <Button variant="soft">Back to sign-in</Button>
          </Link>
        </Stack>
      )}
    </ForgotPassWrapper>
  );
};

export default ForgotPasswordContainer;
