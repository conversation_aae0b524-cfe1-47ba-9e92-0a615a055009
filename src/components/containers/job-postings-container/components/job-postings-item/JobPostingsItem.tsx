import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';

import { useNavigate } from 'react-router';

import { Tooltip, TableRow, TableCell, IconButton } from '@mui/material';

import { appendZeroes } from 'src/utils/helper';
import { formatDate, formatTime } from 'src/utils/date';

import { IconShare } from 'src/components/common/icons';
import StatusChip from 'src/components/common/status-chip/StatusChip';

import {
  DateTimeRoot,
  DateFormatWrapper,
  StyledTextWrapper,
  TimeFormatWrapper,
  UserNameLinkWrapper,
} from './JobPostingsItem.style';

interface JobPostingsListItemProps {
  jobPosting: JobPosting;
}

const JobPostingsListItem = ({ jobPosting }: JobPostingsListItemProps) => {
  const navigate = useNavigate();

  const formattedDate = formatDate(jobPosting?.createdDate ?? '');
  const formattedTime = formatTime(jobPosting?.createdDate ?? '');

  const handleRowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/careers/postings/${jobPosting.id}`, { replace: true });
  };

  return (
    <TableRow hover onClick={handleRowClick}>
      <TableCell>{appendZeroes(jobPosting.id, 4) ?? '---'}</TableCell>
      <TableCell>
        <UserNameLinkWrapper color="inherit">
          {jobPosting?.jobPostingTitle || jobPosting?.jobPosition?.positionTitle || '---'}
        </UserNameLinkWrapper>
      </TableCell>
      <TableCell>
        <Tooltip
          title={jobPosting.jobPostingDescription ?? jobPosting?.jobPosition?.jobDescription}
          placement="top"
        >
          <StyledTextWrapper>
            {jobPosting.jobPostingDescription ?? jobPosting?.jobPosition?.jobDescription ?? '---'}
          </StyledTextWrapper>
        </Tooltip>
      </TableCell>
      <TableCell>{jobPosting?.jobPosition?.department?.name || '---'}</TableCell>
      <TableCell>
        <DateTimeRoot>
          <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
          <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
        </DateTimeRoot>
      </TableCell>
      <TableCell>
        <StatusChip
          variant="soft"
          label={jobPosting.jobPostingStatus?.name ?? '---'}
          color={jobPosting.jobPostingStatus?.colorCode ?? '#000000'}
        />
      </TableCell>
      <TableCell align="right">
        <IconButton onClick={handleRowClick}>
          <IconShare style={{ width: 24 }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default JobPostingsListItem;
