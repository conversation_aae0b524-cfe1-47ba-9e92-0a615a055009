import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  StyledFields,
  StyledToolbarLeft,
  StyledFiltersView,
  StyledToolbarRight,
  StyledToolbarWrapper,
  StyledToolbarFormControl,
  StyledToolbarSearchWrapper,
} from './JobPostingsToolbar.style';

interface JobPostingsToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onDepartmentChange: (department: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  jobPostingsStatus: ddValue[];
  departmentOptions: ddValue[];
  selectedDepartment: ddValue;
  totalCount?: number;
}

const JobPostingsToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  onDepartmentChange,
  selectedStatus,
  jobPostingsStatus,
  onFilterChange,
  departmentOptions,
  selectedDepartment,
  totalCount = 0,
}: JobPostingsToolbarProps) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = jobPostingsStatus?.find(({ id }) => id.toString() === typeId.toString());

    onStatusChange(status || ({} as ddValue));
    onFilterChange(
      {
        field: 'jobPostingStatusId',
        value: status?.id.toString() || '',
      },
      !status?.id
    );
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    onFilterChange(
      {
        field: 'jobPostingStatusId',
        value: '',
      },
      true
    );
  };

  const handleDepartmentChange = (event: TInputChangeEvent) => {
    const departmentId = event.target.value;
    const department = departmentOptions?.find(
      ({ id }) => id.toString() === departmentId.toString()
    );
    onDepartmentChange(department || ({} as ddValue));
    onFilterChange(
      {
        field: 'departmentId',
        value: department?.id?.toString() || '',
      },
      !department?.id
    );
  };

  const handleClearDepartment = () => {
    onDepartmentChange({} as ddValue);
    onFilterChange(
      {
        field: 'departmentId',
        value: '',
      },
      true
    );
  };

  const canReset = searchKey || selectedStatus?.id || selectedDepartment?.id;

  return (
    <StyledToolbarWrapper>
      <StyledFields>
        <StyledToolbarLeft>
          <StyledToolbarSearchWrapper>
            <SearchBar
              type="text"
              value={searchKey}
              onChange={handleSearch}
              placeholder="Search job postings..."
            />
          </StyledToolbarSearchWrapper>
        </StyledToolbarLeft>

        <StyledToolbarRight>
          <StyledToolbarFormControl>
            <CustomSelect
              value={selectedStatus?.id?.toString() ?? ''}
              handleChange={handleStatusChange}
              id="career-listing-status"
              label=""
              name="status"
              coreLabel="Status"
              options={jobPostingsStatus}
              required={false}
            />
          </StyledToolbarFormControl>
          <StyledToolbarFormControl>
            <CustomSelect
              value={selectedDepartment?.id?.toString() ?? ''}
              handleChange={handleDepartmentChange}
              id="job-posting-department"
              label=""
              name="department"
              coreLabel="Department"
              options={departmentOptions}
              required={false}
            />
          </StyledToolbarFormControl>
        </StyledToolbarRight>
      </StyledFields>

      {canReset && (
        <StyledFiltersView>
          <FiltersResult totalResults={totalCount}>
            <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
              <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
            </FiltersBlock>

            <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
              <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
            </FiltersBlock>

            <FiltersBlock label="Department:" isShow={Boolean(selectedDepartment?.id)}>
              <Chip
                {...chipProps}
                label={selectedDepartment?.name}
                onDelete={handleClearDepartment}
              />
            </FiltersBlock>
          </FiltersResult>
        </StyledFiltersView>
      )}
    </StyledToolbarWrapper>
  );
};

export default JobPostingsToolbar;
