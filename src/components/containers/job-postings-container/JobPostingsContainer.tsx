import type { SelectChangeEvent } from '@mui/material';
import type { ddValue } from 'src/shared/types/ddValue';
import type { JobPosting } from 'src/shared/services/job-postings/job-posting.type';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';

import { useNavigate } from 'react-router-dom';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { useDebounce } from 'src/hooks/use-debounce';

import {
  searchJobPostings,
  getDepartmentOptions,
  getJobPostingsStatusOptions,
} from 'src/shared/services/job-postings/job-postings.service';

import TableView from 'src/components/common/table/view/TableView';
import { IconPlus, IconRefresh } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';
import {
  JobPostingsButton,
  JobPostingWrapper,
  JobPostingsOutlinedIconButton,
} from 'src/components/containers/job-postings-container/JobPostingsContainer.style';

import JobPostingsListItem from './components/job-postings-item/JobPostingsItem';
import JobPostingsToolbar from './components/job-postings-toolbar/JobPostingsToolbar';

const TABLE_HEAD = [
  { id: 'id', label: 'ID' },
  { id: 'jobTitle', label: 'Job Title', width: 400 },
  { id: 'department', label: 'Description', width: 300 },
  { id: 'dep', label: 'Department', width: 250 },
  { id: 'location', label: 'Created Date', width: 200 },
  { id: 'status', label: 'Status', width: 100 },
  { id: 'action', label: '' },
];

const JobPostingsContainer = () => {
  const navigate = useNavigate();

  const [tableData, setTableData] = useState<JobPosting[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [jobPostingsStatus, setJobPostingsStatus] = useState<ddValue[]>([]);
  const [departments, setDepartments] = useState<ddValue[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);
  const [selectedDepartment, setSelectedDepartment] = useState<ddValue>({} as ddValue);

  const debouncedSearchKey = useDebounce(searchKey, 500);

  const getJobPostingsData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchJobPostings(criteria);
      setTableData(searchResult);
      setTotalCount(count);
      setFetchError(null);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch career listings');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getJobPostingsData();
  }, [getJobPostingsData]);

  useEffect(() => {
    let isMounted = true;

    const loadOptions = async () => {
      try {
        const [jobPostingsStatusData, departmentData] = await Promise.all([
          getJobPostingsStatusOptions(),
          getDepartmentOptions(),
        ]);

        if (isMounted) {
          setJobPostingsStatus(jobPostingsStatusData);
          setDepartments(departmentData);
        }
      } catch (error: any) {
        console.error('Failed to load options:', error);
      }
    };

    loadOptions();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleDepartmentUpdate = useCallback((department: ddValue): void => {
    setSelectedDepartment(department);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getJobPostingsData();
  }, [getJobPostingsData]);

  const handleAddNewPostingClick = useCallback(() => {
    navigate(`/careers/postings/new`);
  }, [navigate]);

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <MainContent
      pageTitle="Job Postings"
      buttons={
        <>
          <JobPostingsOutlinedIconButton color="primary" size="large" onClick={handleRefreshData}>
            <IconRefresh style={{ width: 18 }} />
          </JobPostingsOutlinedIconButton>
          <JobPostingsButton
            variant="contained"
            color="primary"
            startIcon={<IconPlus />}
            onClick={handleAddNewPostingClick}
          >
            Add
          </JobPostingsButton>
        </>
      }
    >
      <JobPostingWrapper>
        <JobPostingsToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          jobPostingsStatus={jobPostingsStatus}
          selectedStatus={selectedStatus}
          onStatusChange={handleStatusUpdate}
          onDepartmentChange={handleDepartmentUpdate}
          onFilterChange={handleFilterChange}
          departmentOptions={departments}
          selectedDepartment={selectedDepartment}
          totalCount={totalCount}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              currentPage={currentPage}
              onNext={() => setCurrentPage(currentPage + 1)}
              onPrev={() => setCurrentPage(currentPage - 1)}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
              totalCount={totalCount}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData?.map((row) => <JobPostingsListItem key={row.id} jobPosting={row} />)}
        </TableView>
      </JobPostingWrapper>
    </MainContent>
  );
};

export default JobPostingsContainer;
