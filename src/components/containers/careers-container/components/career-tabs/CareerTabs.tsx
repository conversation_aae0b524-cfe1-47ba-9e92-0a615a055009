import { StyledTab, StyledTabs } from './CareerTabs.style';

interface CareerTabsProps {
  currentTab: number;
  onTabChange: (newTab: number) => void;
}

const CareerTabs = ({ currentTab, onTabChange }: CareerTabsProps) => (
  <StyledTabs value={currentTab} onChange={(_event, value) => onTabChange(value)}>
    <StyledTab label="My Listings" value={0} disableRipple />
    <StyledTab label="Responses" value={1} disableRipple />
  </StyledTabs>
);

export default CareerTabs;
