import type { ddValue } from 'src/shared/types/ddValue';
import type { MarketPlace } from 'src/shared/services/market-place/market-place.type';

import { useFormikContext } from 'formik';
import { useState, useCallback } from 'react';

import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Upload } from 'src/components/common/upload';
import CustomSelect from 'src/components/common/forms/select/Select';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid, AdminSectionTitle } from './MarketPlaceForm.style';

interface MarketPlaceFormProps {
  readMode: boolean;
  marketPlaceStatusOptions: ddValue[];
}

const MarketPlaceForm = ({ readMode, marketPlaceStatusOptions }: MarketPlaceFormProps) => {
  const { values, handleChange, handleBlur, errors, touched, setFieldValue } =
    useFormikContext<MarketPlace>();

  const [preview, setPreview] = useState(true);

  const handleDropImages = useCallback(
    (acceptedFiles: File[]) => {
      setFieldValue('images', [...values.images, ...acceptedFiles]);
    },
    [values.images, setFieldValue]
  );

  const handleRemoveImage = useCallback(
    (inputFile: File | string) => {
      const filesFiltered = values.images.filter((fileFiltered) => fileFiltered !== inputFile);
      setFieldValue('images', filesFiltered);
    },
    [values.images, setFieldValue]
  );

  const handleRemoveAllImages = useCallback(() => {
    setFieldValue('images', []);
  }, [setFieldValue]);

  return (
    <FormGrid container columnSpacing={3} rowSpacing={2}>
      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="name"
          name="name"
          label="Name"
          type="text"
          variant="outlined"
          value={values.name}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.name && touched.name)}
          helperText={touched.name ? errors.name : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="contactNumber"
          name="contactNumber"
          label="Contact Number"
          type="text"
          variant="outlined"
          value={values.contactNumber}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.contactNumber && touched.contactNumber)}
          helperText={touched.contactNumber ? errors.contactNumber : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="email"
          name="email"
          label="Email"
          type="text"
          variant="outlined"
          value={values.email}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.email && touched.email)}
          helperText={touched.email ? errors.email : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="title"
          name="title"
          label="Title"
          type="text"
          variant="outlined"
          value={values.title}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.title && touched.title)}
          helperText={touched.title ? errors.title : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="category"
          name="category"
          label="Category"
          type="text"
          variant="outlined"
          value={values.category}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.category && touched.category)}
          helperText={touched.category ? errors.category : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="location"
          name="location"
          label="Location"
          type="text"
          variant="outlined"
          value={values.location}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.location && touched.location)}
          helperText={touched.location ? errors.location : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <CustomTextField
          id="address"
          name="address"
          label="Address"
          type="text"
          variant="outlined"
          value={values.address}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.address && touched.address)}
          helperText={touched.address ? errors.address : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomTextField
          id="expectingPrice"
          name="expectingPrice"
          label="Expecting Price"
          type="number"
          variant="outlined"
          value={values.expectingPrice}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.expectingPrice && touched.expectingPrice)}
          helperText={touched.expectingPrice ? errors.expectingPrice : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <CustomTextField
          id="description"
          name="description"
          label="Description"
          type="text"
          variant="outlined"
          value={values.description}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.description && touched.description)}
          helperText={touched.description ? errors.description : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <CustomTextField
          id="message"
          name="message"
          label="Message"
          type="text"
          variant="outlined"
          value={values.message}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.message && touched.message)}
          helperText={touched.message ? errors.message : ''}
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <AdminSectionTitle variant="h6">Images</AdminSectionTitle>
        <FormControlLabel
          control={<Switch checked={preview} onChange={(e) => setPreview(e.target.checked)} />}
          label="Show Thumbnail"
          sx={{ mb: 3, width: 1, justifyContent: 'flex-end' }}
        />
        <Upload
          multiple
          thumbnail={preview}
          disabled={readMode}
          value={values.images}
          error={Boolean(errors.images && touched.images)}
          helperText={touched.images ? (errors.images as string) : ''}
          onDrop={handleDropImages}
          onRemove={handleRemoveImage}
          onRemoveAll={handleRemoveAllImages}
          onUpload={() => console.info('ON UPLOAD')}
          accept={{
            'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
          }}
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <AdminSectionTitle variant="h6">Admin Action</AdminSectionTitle>
      </FormGrid>

      <FormGrid item xs={12} md={6}>
        <CustomSelect
          id="status.id"
          name="status.id"
          label="Status"
          options={marketPlaceStatusOptions}
          value={values.status?.id?.toString()}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.status?.id && touched.status?.id)}
          helperText={touched.status?.id ? (errors.status?.id as string) : ''}
          required
        />
      </FormGrid>

      <FormGrid item xs={12}>
        <CustomTextField
          id="action.remark"
          name="action.remark"
          label="Remark"
          type="text"
          variant="outlined"
          value={values.action?.remark}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          error={Boolean(errors.action?.remark && touched.action?.remark)}
          helperText={touched.action?.remark ? (errors.action?.remark as string) : ''}
        />
      </FormGrid>
    </FormGrid>
  );
};

export default MarketPlaceForm;
