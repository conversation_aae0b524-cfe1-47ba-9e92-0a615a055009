import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  MarketPlaceToolbarLeft,
  MarketPlaceFiltersView,
  MarketPlaceToolbarRight,
  MarketPlaceToolbarWrapper,
  MarketPlaceToolbarFormControl,
  MarketPlaceToolbarSearchWrapper,
} from './MarketPlaceToolbar.style';

interface MarketPlaceToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  marketPlaceStatusOptions: ddValue[];
}

const MarketPlaceToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  selectedStatus,
  marketPlaceStatusOptions,
  onFilterChange,
}: MarketPlaceToolbarProps) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = marketPlaceStatusOptions.find(({ id }) => id.toString() === typeId.toString());

    onStatusChange(status || ({} as ddValue));
    onFilterChange(
      {
        field: 'status',
        value: status?.id.toString() || '',
      },
      !status?.id
    );
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    onFilterChange(
      {
        field: 'status',
        value: '',
      },
      true
    );
  };

  const canReset = searchKey || selectedStatus?.id;

  return (
    <MarketPlaceToolbarWrapper>
      <MarketPlaceToolbarLeft>
        {canReset && (
          <MarketPlaceFiltersView>
            <FiltersResult totalResults={0}>
              <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
                <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
              </FiltersBlock>

              <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
                <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
              </FiltersBlock>
            </FiltersResult>
          </MarketPlaceFiltersView>
        )}
      </MarketPlaceToolbarLeft>

      <MarketPlaceToolbarRight>
        <MarketPlaceToolbarSearchWrapper>
          <SearchBar
            type="text"
            value={searchKey}
            onChange={handleSearch}
            placeholder="Search market places..."
          />
        </MarketPlaceToolbarSearchWrapper>
        <MarketPlaceToolbarFormControl>
          <CustomSelect
            value={selectedStatus?.id?.toString() ?? ''}
            handleChange={handleStatusChange}
            id="market-place-status"
            label=""
            name="status"
            coreLabel="Status"
            options={marketPlaceStatusOptions}
            disabled={false}
            required={false}
          />
        </MarketPlaceToolbarFormControl>
      </MarketPlaceToolbarRight>
    </MarketPlaceToolbarWrapper>
  );
};

export default MarketPlaceToolbar;
