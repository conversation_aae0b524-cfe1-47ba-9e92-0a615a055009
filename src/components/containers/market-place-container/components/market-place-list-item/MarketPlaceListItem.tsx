import type { MarketPlace } from 'src/shared/services/market-place/market-place.type';

import { Stack, Avatar, Tooltip, TableRow, TableCell, IconButton } from '@mui/material';

import { IconShare } from 'src/components/common/icons';

import { StatusChip, EmailWrapper, UserNameLinkWrapper } from './MarketPlaceListItem.style';

interface MarketPlaceListItemProps {
  marketPlace: MarketPlace;
  onRowClick: () => void;
  onEditRow: () => void;
}

const getStatusColor = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'new':
      return 'primary';
    case 'assigned':
      return 'success';
    case 'declined':
      return 'error';
    default:
      return 'default';
  }
};

const MarketPlaceListItem = ({ marketPlace, onRowClick, onEditRow }: MarketPlaceListItemProps) => (
  <TableRow hover onClick={onRowClick}>
    <TableCell align="center">{marketPlace.id}</TableCell>
    <TableCell
      onClick={(e) => {
        e.stopPropagation();
        onEditRow();
      }}
    >
      <Stack spacing={2} direction="row" alignItems="center">
        <Avatar alt={marketPlace.name} src="/static/mock-images/avatars/avatar_default.jpg" />
        <UserNameLinkWrapper color="inherit">{marketPlace.name}</UserNameLinkWrapper>
      </Stack>
    </TableCell>
    <TableCell>{marketPlace.contactNumber}</TableCell>
    <EmailWrapper>{marketPlace.email}</EmailWrapper>
    <TableCell>{marketPlace.category}</TableCell>
    <TableCell>{marketPlace.location}</TableCell>
    <TableCell>
      <StatusChip
        variant="soft"
        label={marketPlace.status?.name || 'N/A'}
        size="small"
        color={getStatusColor(marketPlace.status?.name)}
      />
    </TableCell>
    <TableCell>
      <Tooltip title="Edit">
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onEditRow();
          }}
        >
          <IconShare width={20} />
        </IconButton>
      </Tooltip>
    </TableCell>
  </TableRow>
);

export default MarketPlaceListItem;
