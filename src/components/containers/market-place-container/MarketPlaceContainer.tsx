import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { Errors } from 'src/shared/types/errorResonse';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { MarketPlace } from 'src/shared/services/market-place/market-place.type';

import { Formik } from 'formik';
import { useLocation, useNavigate } from 'react-router-dom';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { type SelectChangeEvent } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'src/hooks/use-debounce';

import { initialMarketPlace } from 'src/utils/validation/market-place/MarketPlace.values';
import marketPlaceValidationSchema from 'src/utils/validation/market-place/MarketPlace.schema';

import { useToast } from 'src/providers/ToastProvider';
import {
  createMarketPlace,
  updateMarketPlace,
  deleteMarketPlace,
  searchMarketPlaces,
  getMarketPlaceStatusOptions,
} from 'src/shared/services/market-place/market-place.service';

import TableView from 'src/components/common/table/view/TableView';
import FormDialog from 'src/components/common/forms/form-dialog/FormDialog';
import { IconPlus, IconRefresh, IconDownload } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import MarketPlaceForm from './components/market-place-form/MarketPlaceForm';
import MarketPlaceToolbar from './components/market-place-toolbar/MarketPlaceToolbar';
import MarketPlaceListItem from './components/market-place-list-item/MarketPlaceListItem';
import {
  MarketPlaceButton,
  MarketPlaceWrapper,
  MarketPlaceOutlinedIconButton,
} from './MarketPlaceContainer.style';

const TABLE_HEAD = [
  { id: 'id', label: 'S.N', width: 80 },
  { id: 'name', label: 'Name', width: 200 },
  { id: 'contactNumber', label: 'Contact', width: 150 },
  { id: 'email', label: 'Email', width: 200 },
  { id: 'category', label: 'Category', width: 150 },
  { id: 'location', label: 'Location', width: 150 },
  { id: 'status', label: 'Status', width: 120 },
  { id: 'action', label: '', width: 80 },
];

const MarketPlaceContainer = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const openForm = useBoolean();
  const { showToast } = useToast();

  const [tableData, setTableData] = useState<MarketPlace[]>([]);
  const [edit, setEdit] = useState<EditMode<MarketPlace>>({} as EditMode<MarketPlace>);
  const [isNew, setIsNew] = useState(false);

  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const debouncedSearchKey = useDebounce(searchKey, 500);
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [marketPlaceStatusOptions, setMarketPlaceStatusOptions] = useState<ddValue[]>([]);

  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);

  const getTableData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchMarketPlaces(criteria);
      setTableData(searchResult);
      setTotalCount(count);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch market places');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getTableData();
  }, [getTableData]);

  useEffect(() => {
    const loadMarketPlaceStatusOptions = async () => {
      try {
        setLoading(true);
        const data = await getMarketPlaceStatusOptions();
        setMarketPlaceStatusOptions(data);
      } catch (error) {
        setFetchError(error.message || 'Failed to fetch status options');
      } finally {
        setLoading(false);
      }
    };

    loadMarketPlaceStatusOptions();
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
    setCurrentPage(1);
  }, []);

  const handleModalClose = useCallback(() => {
    openForm.onFalse();
    setEdit({} as EditMode<MarketPlace>);
    setIsNew(false);
  }, [openForm]);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
    setCurrentPage(1);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
    setCurrentPage(1);
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getTableData();
  }, [getTableData]);

  const handleEditRow = useCallback(
    (data: MarketPlace) => {
      setEdit({ isRead: true, data, id: data?.id });
      setIsNew(false);
      openForm.onTrue();
    },
    [openForm]
  );

  const handleOpenDetails = useCallback(
    (marketPlaceId: number) => {
      navigate(`${pathname}/${marketPlaceId}`);
    },
    [navigate, pathname]
  );

  const handleCreateNew = useCallback(() => {
    setIsNew(true);
    setEdit({} as EditMode<MarketPlace>);
    openForm.onTrue();
  }, [openForm]);

  const handleDelete = useCallback(
    async (marketPlace: MarketPlace) => {
      if (!marketPlace?.id) {
        return;
      }

      try {
        setLoading(true);
        await deleteMarketPlace(marketPlace.id);
        await getTableData();
        showToast('Market place deleted successfully.', 'success');
        handleModalClose();
      } catch (error) {
        showToast('Failed to delete market place.', 'error');
      } finally {
        setLoading(false);
      }
    },
    [getTableData, showToast, handleModalClose]
  );

  const handleSubmit = useCallback(
    async (values: MarketPlace): Promise<void> => {
      setLoading(true);

      try {
        if (isNew) {
          await createMarketPlace(values);
          showToast('Market place created successfully.', 'success');
        } else {
          await updateMarketPlace(values.id, values);
          showToast('Market place updated successfully.', 'success');
        }
        await getTableData();
        handleModalClose();
      } catch (error) {
        const errorResponse = error as Errors;
        const errorMessage =
          typeof errorResponse.errors === 'string'
            ? errorResponse.errors
            : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

        if (typeof errorMessage === 'string') {
          showToast(errorMessage, 'error');
        } else {
          Object.values(errorMessage).forEach((message) => showToast(message as string, 'error'));
        }
      } finally {
        setLoading(false);
      }
    },
    [isNew, getTableData, showToast, handleModalClose]
  );

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <>
      <MainContent
        pageTitle="Market Places"
        buttons={
          <>
            <MarketPlaceOutlinedIconButton size="large" onClick={handleRefreshData}>
              <IconRefresh />
            </MarketPlaceOutlinedIconButton>
            <MarketPlaceOutlinedIconButton size="large">
              <IconDownload />
            </MarketPlaceOutlinedIconButton>
            <MarketPlaceButton
              variant="contained"
              color="primary"
              startIcon={<IconPlus />}
              onClick={handleCreateNew}
            >
              Add
            </MarketPlaceButton>
          </>
        }
      >
        <MarketPlaceWrapper>
          <MarketPlaceToolbar
            onSearchKey={handleSearchKey}
            searchKey={searchKey}
            marketPlaceStatusOptions={marketPlaceStatusOptions}
            selectedStatus={selectedStatus}
            onStatusChange={handleStatusUpdate}
            onFilterChange={handleFilterChange}
          />

          <TableView
            pagination={
              <TablePagination
                count={Math.ceil(totalCount / rowsPerPage)}
                currentPage={currentPage}
                onNext={() => setCurrentPage(currentPage + 1)}
                onPrev={() => setCurrentPage(currentPage - 1)}
                value={rowsPerPage.toString()}
                handleChange={handleRowsPerPageChange}
                totalCount={totalCount}
              />
            }
            tableHead={TABLE_HEAD}
            error={fetchError}
            loading={loading}
            tableData={memoizedTableData ?? []}
          >
            {memoizedTableData?.map((row) => (
              <MarketPlaceListItem
                key={row?.id}
                marketPlace={row}
                onRowClick={() => handleOpenDetails(row.id)}
                onEditRow={() => handleEditRow(row)}
              />
            ))}
          </TableView>
        </MarketPlaceWrapper>
      </MainContent>

      <Formik<MarketPlace>
        initialValues={initialMarketPlace(edit?.data as MarketPlace)}
        onSubmit={handleSubmit}
        validationSchema={marketPlaceValidationSchema}
        enableReinitialize
      >
        {() => (
          <FormDialog
            maxWidth="md"
            open={openForm.value}
            name={edit?.data?.name || ''}
            readMode={false}
            isNew={isNew}
            title={isNew ? 'Create Market Place' : `Market Place - ${edit?.data?.name || ''}`}
            formLoading={loading}
            handleDeleteAction={() => handleDelete(edit.data as MarketPlace)}
            onClose={handleModalClose}
            handleCancel={handleModalClose}
            handleEditAction={() => {
              console.log('edit');
            }}
            content={
              <MarketPlaceForm
                readMode={loading}
                marketPlaceStatusOptions={marketPlaceStatusOptions}
              />
            }
          />
        )}
      </Formik>
    </>
  );
};

export default MarketPlaceContainer;
