import type { EditMode } from 'src/shared/types/editMode';
import type { Gallery } from 'src/shared/services/gallery/gallery.type';

import { Formik } from 'formik';
import { useState } from 'react';

import { Button } from '@mui/material';

import { gallerySchema } from 'src/utils/validation/gallery/gallery.schema';
import { initialGallery } from 'src/utils/validation/gallery/gallery.values';

import ImageUpload from 'src/components/common/cards/image-upload/ImageUpload';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import { ContainerWrapper } from './GalleryContainer.style';

const GalleryContainer = () => {
  const [edit, setEdit] = useState<EditMode<Gallery>>({} as EditMode<Gallery>);

  console.log('GALLERY');

  const handleSubmit = async () => {
    setEdit({} as EditMode<Gallery>);
  };

  return (
    <MainContent pageTitle="Gallery" buttons={<Button variant="contained">Publish</Button>}>
      <ContainerWrapper>
        <Formik
          initialValues={initialGallery(edit?.data as Gallery)}
          onSubmit={handleSubmit}
          validationSchema={gallerySchema}
          enableReinitialize
        >
          <ImageUpload
            title="Hero Section"
            readMode={edit?.isRead as boolean}
            accept={{
              'video/mp4': ['.mp4'],
              'image/*': ['.jpeg', '.jpg', '.gif', '.webp'],
            }}
          />
        </Formik>
      </ContainerWrapper>
    </MainContent>
  );
};

export default GalleryContainer;
