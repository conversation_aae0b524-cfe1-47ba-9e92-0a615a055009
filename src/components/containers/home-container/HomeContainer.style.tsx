import { Box, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';

export const HomeCardWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
  flexWrap: 'wrap',
  gap: '1.5rem',
  paddingTop: '5rem',
  height: '100%',
  paddingBottom: '2rem',
}));

export const HomeCard = styled(Paper)(({ theme }) => ({
  padding: '2rem',
  borderRadius: '0.8rem',
  width: '200px',
  boxShadow: `0px 0px 50px ${theme.palette.grey[300]}`,
  display: 'grid',
  gap: '.4rem',
  textAlign: 'center',
  '&:hover': {
    transform: 'scale(1.03)',
    boxShadow: theme.shadows[0],
  },
  '&:active': {
    transform: 'scale(1.01)',
  },
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
    easing: theme.transitions.easing.easeInOut,
  }),
}));

export const HomeGraphicsWrapper = styled(Box)(() => ({

  width: '110px',
  height: '90px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

export const HomeGraphics = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
  marginInline: 'auto',
  paddingInline: '1rem',
  paddingTop: '1rem',
}));

export const HomeCardLabel = styled('span')(({ theme }) => ({
  fontWeight: '600',
  textAlign: 'center',
}));
