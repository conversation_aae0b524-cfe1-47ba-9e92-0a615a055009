import { useNavigate } from 'react-router-dom';

import { Container } from '@mui/material';

import { APP_DATA } from 'src/utils/contants';

import NoDataView from 'src/components/common/error/no-data-view/NoDataView';

import {
  HomeCard,
  HomeGraphics,
  HomeCardLabel,
  HomeCardWrapper,
  HomeGraphicsWrapper,
} from './HomeContainer.style';

const HomeContainer = () => {
  const navigate = useNavigate();
  return (
    <Container>
      <HomeCardWrapper>
        {APP_DATA.length > 0 ? (
          <>
            {APP_DATA?.map((item, index) => (
              <HomeCard key={index} sx={{ cursor: 'pointer' }} onClick={() => navigate(item.path)}>
                <HomeGraphicsWrapper>
                  <HomeGraphics src={item.image} />
                </HomeGraphicsWrapper>
                <HomeCardLabel>{item.name}</HomeCardLabel>
              </HomeCard>
            ))}
          </>
        ) : (
          <NoDataView />
        )}
      </HomeCardWrapper>
    </Container>
  );
};

export default HomeContainer;
