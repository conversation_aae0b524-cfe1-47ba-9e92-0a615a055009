import { styled } from '@mui/material/styles';

export const TestimonialToolbarWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2.5),
  borderRadius: theme.shape.borderRadius,
}));

export const TestimonialToolbarLeft = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  flex: 1,
}));

export const TestimonialToolbarRight = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
  minWidth: '500px',
}));

export const TestimonialToolbarFormControl = styled('div')(({ theme }) => ({
  flexShrink: 0,
  width: 200,
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

export const TestimonialToolbarSearchWrapper = styled('div')(() => ({
  width: '400px',
}));

export const TestimonialFiltersView = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
}));
