import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  TestimonialToolbarLeft,
  TestimonialFiltersView,
  TestimonialToolbarRight,
  TestimonialToolbarWrapper,
  TestimonialToolbarFormControl,
  TestimonialToolbarSearchWrapper,
} from './TestimonialToolbar.style';

interface TestimonialToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onTypeChange: (type: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedType: ddValue;
  testimonialTypeOptions: ddValue[];
  currentTab: number;
  onTabChange: (tab: number) => void;
  totalCount?: number;
}

const TestimonialToolbar = ({
  onSearchKey,
  searchKey,
  onTypeChange,
  selectedType,
  testimonialTypeOptions,
  onFilterChange,
  totalCount = 0,
}: TestimonialToolbarProps) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearType = () => {
    onTypeChange({} as ddValue);
    onFilterChange(
      {
        field: 'type',
        value: '',
      },
      true
    );
  };

  const handleTypeSelectChange = (event: TInputChangeEvent) => {
    const selectedId = Number(event.target.value);
    const selectedOption = testimonialTypeOptions.find((option) => option.id === selectedId);

    if (selectedOption) {
      onTypeChange(selectedOption);
      onFilterChange({
        field: 'type',
        value: selectedId.toString(),
      });
    } else {
      handleClearType();
    }
  };

  const canReset = searchKey || selectedType?.id;

  return (
    <TestimonialToolbarWrapper>
      <TestimonialToolbarLeft>
        {canReset && (
          <TestimonialFiltersView>
            <FiltersResult totalResults={totalCount}>
              <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
                <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
              </FiltersBlock>

              <FiltersBlock label="Type:" isShow={Boolean(selectedType?.id)}>
                <Chip {...chipProps} label={selectedType?.name} onDelete={handleClearType} />
              </FiltersBlock>
            </FiltersResult>
          </TestimonialFiltersView>
        )}
      </TestimonialToolbarLeft>

      <TestimonialToolbarRight>
        <TestimonialToolbarSearchWrapper>
          <SearchBar
            type="text"
            value={searchKey}
            onChange={handleSearch}
            placeholder="Search..."
          />
        </TestimonialToolbarSearchWrapper>
        <TestimonialToolbarFormControl>
          <CustomSelect
            value={selectedType?.id?.toString() ?? ''}
            handleChange={handleTypeSelectChange}
            id="testimonial-type-filter"
            label=""
            name="type"
            coreLabel="Type"
            options={testimonialTypeOptions}
            disabled={false}
            required={false}
          />
        </TestimonialToolbarFormControl>
      </TestimonialToolbarRight>
    </TestimonialToolbarWrapper>
  );
};

export default TestimonialToolbar;
