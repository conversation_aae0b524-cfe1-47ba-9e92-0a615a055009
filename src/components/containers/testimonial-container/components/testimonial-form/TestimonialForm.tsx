import type { ddValue } from 'src/shared/types/ddValue';
import type { Testimonial } from 'src/shared/services/testimonial/testimonial.type';

import { memo } from 'react';
import { useFormikContext } from 'formik';

import { Grid } from '@mui/material';

import CustomSelect from 'src/components/common/forms/select/Select';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid } from './TestimonialForm.style';

interface TestimonialFormProps {
  readMode: boolean;
  testimonialTypeOptions: ddValue[];
}

const TestimonialForm = ({ readMode, testimonialTypeOptions }: TestimonialFormProps) => {
  const { values, errors, touched, handleChange, handleBlur, setFieldValue } =
    useFormikContext<Testimonial>();

  return (
    <FormGrid container spacing={2}>
      <Grid item xs={12} md={6}>
        <CustomTextField
          id="name"
          name="name"
          label="Name"
          placeholder="Enter name"
          variant="outlined"
          type="text"
          value={values.name}
          error={Boolean(touched.name && errors.name)}
          helperText={touched.name && errors.name ? String(errors.name) : undefined}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          required
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <CustomTextField
          id="company"
          name="company"
          label="Company"
          placeholder="Enter company"
          variant="outlined"
          type="text"
          value={values.company}
          error={Boolean(touched.company && errors.company)}
          helperText={touched.company && errors.company ? String(errors.company) : undefined}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          required
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <CustomSelect
          id="type"
          name="type"
          label="Testimonial Type"
          placeholder="Select testimonial type"
          variant="outlined"
          options={testimonialTypeOptions}
          value={values.type?.id?.toString() || ''}
          error={Boolean(touched.type && errors.type)}
          helperText={
            touched.type && errors.type
              ? String((errors.type as any)?.id || errors.type)
              : undefined
          }
          handleChange={(e) => {
            const selectedType = testimonialTypeOptions.find(
              (option) => option.id.toString() === e.target.value
            );
            setFieldValue('type', selectedType);
          }}
          disabled={readMode}
          required
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <CustomTextField
          id="rating"
          name="rating"
          label="Rating (1-5)"
          placeholder="Enter rating"
          variant="outlined"
          type="number"
          value={values.rating}
          error={Boolean(touched.rating && errors.rating)}
          helperText={touched.rating && errors.rating ? String(errors.rating) : undefined}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          required
        />
      </Grid>

      <Grid item xs={12}>
        <CustomTextField
          id="text"
          name="text"
          label="Testimonial Text"
          placeholder="Enter testimonial text"
          variant="outlined"
          type="text"
          value={values.text}
          error={Boolean(touched.text && errors.text)}
          helperText={touched.text && errors.text ? String(errors.text) : undefined}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={readMode}
          multiline
          minRows={4}
          maxRows={8}
          required
        />
      </Grid>
    </FormGrid>
  );
};

export default memo(TestimonialForm);
