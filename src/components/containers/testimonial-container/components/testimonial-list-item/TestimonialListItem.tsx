import type { Testimonial } from 'src/shared/services/testimonial/testimonial.type';

import { memo } from 'react';

import { Rating, TableRow, TableCell, IconButton } from '@mui/material';

import { IconEdit } from 'src/components/common/icons';

interface TestimonialListItemProps {
  testimonial: Testimonial;
  onRowClick: () => void;
  onEditRow: () => void;
}

const TestimonialListItem = ({ testimonial, onRowClick, onEditRow }: TestimonialListItemProps) => {
  const handleEditClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    onEditRow();
  };

  return (
    <TableRow hover onClick={onRowClick} sx={{ cursor: 'pointer' }}>
      <TableCell>{testimonial.id}</TableCell>
      <TableCell>{testimonial.name}</TableCell>
      <TableCell>{testimonial.company}</TableCell>
      <TableCell>
        <Rating value={testimonial.rating} readOnly size="small" />
      </TableCell>
      <TableCell>{testimonial.type.name}</TableCell>
      <TableCell align="right">
        <IconButton onClick={handleEditClick} size="small">
          <IconEdit width={20} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default memo(TestimonialListItem);
