import { StyledTab, StyledTabs } from './TestimonialTabs.style';

interface TestimonialTabsProps {
  currentTab: number;
  onTabChange: (newTab: number) => void;
  testimonialTypes: { id: number; name: string }[];
}

const TestimonialTabs = ({ currentTab, onTabChange, testimonialTypes }: TestimonialTabsProps) => (
  <StyledTabs value={currentTab} onChange={(_event, value) => onTabChange(value)}>
    <StyledTab label="All" value={0} disableRipple />
    {testimonialTypes.map((type) => (
      <StyledTab key={type.id} label={type.name} value={type.id} disableRipple />
    ))}
  </StyledTabs>
);

export default TestimonialTabs;
