import { Tab, Tabs } from '@mui/material';
import { styled } from '@mui/material/styles';

export const StyledTabs = styled(Tabs)(({ theme }) => ({
  minHeight: 40,
  backgroundColor: theme.palette.grey[200],
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(0.5),
  '& .MuiTabs-indicator': {
    display: 'none',
  },
  '& .MuiTabs-flexContainer': {
    gap: theme.spacing(1),
  },
}));

export const StyledTab = styled(Tab)(({ theme }) => ({
  minHeight: 32,
  minWidth: 100,
  padding: theme.spacing(0, 2),
  borderRadius: theme.shape.borderRadius,
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
  fontWeight: theme.typography.fontWeightMedium,
  textTransform: 'none',
  '&.Mui-selected': {
    color: theme.palette.text.primary,
    backgroundColor: theme.palette.common.white,
    boxShadow: `0 1px 3px 0 rgb(0 0 0 / 0.1)`,
  },
}));
