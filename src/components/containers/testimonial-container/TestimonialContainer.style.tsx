import { styled } from '@mui/material/styles';
import { Tab, Box, Tabs, Button, IconButton } from '@mui/material';

export const TestimonialButton = styled(Button)(({ theme }) => ({
  height: 40,
  fontWeight: 600,
  borderRadius: theme.shape.borderRadius,
}));

export const TestimonialOutlinedIconButton = styled(IconButton)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  marginRight: theme.spacing(1),
}));

export const TestimonialFilledIconButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  borderRadius: theme.shape.borderRadius,
  marginRight: theme.spacing(1),
  color: theme.palette.common.white,
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
  },
}));

export const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '& .MuiTabs-indicator': {
    backgroundColor: theme.palette.primary.main,
  },
}));

export const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  '&.Mui-selected': {
    color: theme.palette.primary.main,
  },
}));

export const ContainerWrapper = styled(Box)(({ theme }) => ({
  marginInline: theme.spacing(4),
}));
