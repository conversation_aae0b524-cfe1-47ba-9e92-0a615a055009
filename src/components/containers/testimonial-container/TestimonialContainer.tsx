import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { Errors } from 'src/shared/types/errorResonse';
import type { Testimonial } from 'src/shared/services/testimonial/testimonial.type';
import type {
  FilterOption,
  SearchCriteria,
  PaginationOptions,
} from 'src/shared/types/searchCriteria';

import { Formik } from 'formik';
import { useLocation, useNavigate } from 'react-router-dom';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'src/hooks/use-debounce';

import { testimonialSchema } from 'src/utils/validation/testimonial/Testimonial.schema';
import { initialTestimonial } from 'src/utils/validation/testimonial/Testimonial.values';

import { useToast } from 'src/providers/ToastProvider';
import {
  updateTestimonial,
  createTestimonial,
  deleteTestimonial,
  searchTestimonials,
  getTestimonialById,
  getTestimonialTypeOptions,
} from 'src/shared/services/testimonial/testimonial.service';

import TableView from 'src/components/common/table/view/TableView';
import { IconPlus, IconRefresh } from 'src/components/common/icons';
import FormDialog from 'src/components/common/forms/form-dialog/FormDialog';
import NoDataView from 'src/components/common/error/no-data-view/NoDataView';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import TestimonialForm from './components/testimonial-form/TestimonialForm';
import TestimonialToolbar from './components/testimonial-toolbar/TestimonialToolbar';
import TestimonialListItem from './components/testimonial-list-item/TestimonialListItem';
import {
  ContainerWrapper,
  TestimonialButton,
  TestimonialOutlinedIconButton,
} from './TestimonialContainer.style';

const TABLE_HEAD = [
  { id: 'id', label: 'ID', width: 80 },
  { id: 'name', label: 'Name', width: 200 },
  { id: 'company', label: 'Company', width: 150 },
  { id: 'rating', label: 'Rating', width: 120 },
  { id: 'type', label: 'Type', width: 120 },
  { id: 'action', label: '', width: 80 },
];

const TestimonialContainer = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { showToast } = useToast();
  const openForm = useBoolean();

  const [isNew, setIsNew] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [testimonialTypeOptions, setTestimonialTypeOptions] = useState<ddValue[]>([]);
  const [selectedType, setSelectedType] = useState<ddValue>({} as ddValue);
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [searchKey, setSearchKey] = useState('');
  const [edit, setEdit] = useState<EditMode<Testimonial>>({} as EditMode<Testimonial>);
  const [tableData, setTableData] = useState<Testimonial[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const debouncedSearchKey = useDebounce(searchKey, 500);

  const getTestimonialTypes = useCallback(async () => {
    try {
      const options = await getTestimonialTypeOptions();
      setTestimonialTypeOptions(options);
    } catch (error) {
      console.error('Failed to fetch testimonial type options:', error);
    }
  }, []);

  const getTableData = useCallback(async () => {
    try {
      setLoading(true);
      setFetchError(null);

      const filterOptions: FilterOption[] = [...filters];

      if (selectedType?.id) {
        filterOptions.push({ field: 'type', value: selectedType.id.toString() });
      }

      const pagination: PaginationOptions = {
        pageNumber: currentPage,
        pageSize: rowsPerPage,
      };

      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters: filterOptions,
        sortOptions: [],
        pagination,
      };

      const response = await searchTestimonials(criteria);
      setTableData(response.searchResult);
      setTotalCount(response.count);
    } catch (error) {
      setFetchError('Failed to fetch testimonials. Please try again.');
      console.error('Error fetching testimonials:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, selectedType, filters]);

  useEffect(() => {
    getTestimonialTypes();
  }, [getTestimonialTypes]);

  useEffect(() => {
    getTableData();
  }, [getTableData]);

  const handleSearchKey = useCallback((value: string) => {
    setSearchKey(value);
    setCurrentPage(1);
  }, []);

  const handleTypeChange = useCallback((type: ddValue): void => {
    setSelectedType(type);
    setCurrentPage(1);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
  }, []);

  const handleRowsPerPageChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setCurrentPage(1);
  }, []);

  const handleRefreshData = useCallback(() => {
    getTableData();
  }, [getTableData]);

  const handleOpenDetails = useCallback(
    (testimonialId: number) => {
      navigate(`${pathname}/${testimonialId}`);
    },
    [navigate, pathname]
  );

  const handleCreateNew = useCallback(() => {
    setIsNew(true);
    setEdit({} as EditMode<Testimonial>);
    openForm.onTrue();
  }, [openForm]);

  const handleEditRow = useCallback(
    async (testimonial: Testimonial) => {
      try {
        setFormLoading(true);
        const testimonialData = await getTestimonialById(testimonial.id);
        setEdit({ data: testimonialData, id: testimonialData.id });
        setIsNew(false);
        openForm.onTrue();
      } catch (error) {
        showToast('Failed to fetch testimonial details.', 'error');
      } finally {
        setFormLoading(false);
      }
    },
    [openForm, showToast]
  );

  const handleModalClose = useCallback(() => {
    openForm.onFalse();
    setEdit({} as EditMode<Testimonial>);
    setIsNew(false);
  }, [openForm]);

  const handleDelete = useCallback(
    async (testimonial: Testimonial) => {
      if (!testimonial?.id) {
        return;
      }

      try {
        setFormLoading(true);
        await deleteTestimonial(testimonial.id);
        await getTableData();
        showToast('Testimonial deleted successfully.', 'success');
        handleModalClose();
      } catch (error) {
        showToast('Failed to delete testimonial.', 'error');
      } finally {
        setFormLoading(false);
      }
    },
    [getTableData, showToast, handleModalClose]
  );

  const handleSubmit = useCallback(
    async (values: Testimonial): Promise<void> => {
      setFormLoading(true);

      try {
        if (isNew) {
          await createTestimonial(values);
          showToast('Testimonial created successfully.', 'success');
        } else {
          await updateTestimonial(values.id, values);
          showToast('Testimonial updated successfully.', 'success');
        }
        await getTableData();
        handleModalClose();
      } catch (error) {
        const errorResponse = error as Errors;
        const errorMessage =
          typeof errorResponse.errors === 'string'
            ? errorResponse.errors
            : Object.values(errorResponse.errors || {})[0] || 'An unknown error occurred';
        showToast(errorMessage, 'error');
      } finally {
        setFormLoading(false);
      }
    },
    [isNew, getTableData, handleModalClose, showToast]
  );

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <>
      <MainContent
        pageTitle="Testimonials"
        buttons={
          <>
            <TestimonialOutlinedIconButton color="primary" size="large" onClick={handleRefreshData}>
              <IconRefresh style={{ width: 18 }} />
            </TestimonialOutlinedIconButton>
            <TestimonialButton
              variant="contained"
              color="primary"
              startIcon={<IconPlus />}
              onClick={handleCreateNew}
            >
              Add
            </TestimonialButton>
          </>
        }
      >
        <ContainerWrapper>
          <TestimonialToolbar
            searchKey={searchKey}
            onSearchKey={handleSearchKey}
            testimonialTypeOptions={testimonialTypeOptions}
            selectedType={selectedType}
            onTypeChange={handleTypeChange}
            currentTab={0}
            onTabChange={() => {}}
            onFilterChange={handleFilterChange}
            totalCount={totalCount}
          />

          <TableView
            pagination={
              <TablePagination
                count={Math.ceil(totalCount / rowsPerPage)}
                currentPage={currentPage}
                onNext={() => setCurrentPage(currentPage + 1)}
                onPrev={() => setCurrentPage(currentPage - 1)}
                value={rowsPerPage.toString()}
                handleChange={(e) => {
                  const syntheticEvent = {
                    target: {
                      value: e.target.value,
                    },
                  } as React.ChangeEvent<HTMLInputElement>;
                  handleRowsPerPageChange(syntheticEvent);
                }}
                totalCount={totalCount}
              />
            }
            tableHead={TABLE_HEAD}
            error={fetchError}
            loading={loading}
            tableData={memoizedTableData}
            selected={[]}
            setSelected={() => {}}
          >
            {memoizedTableData.length > 0 ? (
              memoizedTableData.map((row) => (
                <TestimonialListItem
                  key={row.id}
                  testimonial={row}
                  onRowClick={() => handleOpenDetails(row.id)}
                  onEditRow={() => handleEditRow(row)}
                />
              ))
            ) : (
              <NoDataView />
            )}
          </TableView>
        </ContainerWrapper>
      </MainContent>

      <Formik<Testimonial>
        initialValues={initialTestimonial(edit?.data as Testimonial)}
        validationSchema={testimonialSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {() => (
          <FormDialog
            maxWidth="md"
            open={openForm.value}
            name={edit?.data?.name || ''}
            readMode={false}
            isNew={isNew}
            title={`${isNew ? 'Add New Testimonial' : `Edit Testimonial - ${edit?.data?.name || ''}`}`}
            formLoading={formLoading}
            handleDeleteAction={() => handleDelete(edit.data as Testimonial)}
            handleEditAction={() => {}}
            onClose={handleModalClose}
            handleCancel={handleModalClose}
            content={
              <TestimonialForm readMode={loading} testimonialTypeOptions={testimonialTypeOptions} />
            }
          />
        )}
      </Formik>
    </>
  );
};

export default TestimonialContainer;
