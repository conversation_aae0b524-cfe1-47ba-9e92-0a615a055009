import type { FC } from 'react';
import type { ddValue } from 'src/shared/types/ddValue';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

import { useFormikContext } from 'formik';

import CustomSelect from 'src/components/common/forms/select/Select';
import { RichTextEditor } from 'src/components/common/forms/rich-text-editor';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid, FormWrapper, FormGridRoot } from './JobPositionForm.style';

interface JobPositionFormProps {
  readMode?: boolean;
  departmentOptions: ddValue[];
  jobPositionTypeOptions: ddValue[];
  jobPositionStatusOptions: ddValue[];
}

const JobPositionForm: FC<JobPositionFormProps> = ({
  readMode = false,
  departmentOptions,
  jobPositionTypeOptions,
  jobPositionStatusOptions,
}) => {
  const { values, handleChange, handleBlur, errors, touched } = useFormikContext<JobPosition>();

  return (
    <FormWrapper>
      <FormGridRoot>
        <FormGrid>
          <CustomTextField
            id="positionTitle"
            name="positionTitle"
            label="Position Title"
            variant="outlined"
            type="text"
            value={values.positionTitle}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.positionTitle && touched.positionTitle)}
            helperText={touched.positionTitle ? errors.positionTitle : ''}
            required
          />
        </FormGrid>
        <FormGrid>
          <CustomSelect
            id="departmentId"
            name="departmentId"
            label="Department"
            options={departmentOptions}
            value={
              departmentOptions
                .find((option) => option.id === values.departmentId)
                ?.id?.toString() ?? ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.departmentId && touched.departmentId)}
            helperText={touched.departmentId ? errors.departmentId : ''}
            required
          />
        </FormGrid>
        <FormGrid>
          <CustomSelect
            id="jobPositionTypeId"
            name="jobPositionTypeId"
            label="Position Type"
            options={jobPositionTypeOptions}
            value={
              jobPositionTypeOptions
                .find((option) => option.id === values.jobPositionTypeId)
                ?.id?.toString() ?? ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.jobPositionTypeId && touched.jobPositionTypeId)}
            helperText={touched.jobPositionTypeId ? errors.jobPositionTypeId : ''}
            required
          />
        </FormGrid>
        <FormGrid>
          <CustomTextField
            id="jobCode"
            name="jobCode"
            label="Job Code"
            variant="outlined"
            type="text"
            value={values.jobCode}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.jobCode && touched.jobCode)}
            helperText={touched.jobCode ? errors.jobCode : ''}
            required
          />
        </FormGrid>

        <FormGrid fullWidth>
          <RichTextEditor
            id="jobDescription"
            name="jobDescription"
            label="Job Description"
            placeholder="This field is required (min 10 characters)..."
            value={values.jobDescription}
            error={touched.jobDescription && !!errors.jobDescription}
            handleChange={handleChange}
            handleBlur={handleBlur}
            helperText={touched.jobDescription ? errors.jobDescription : ''}
            required
          />
        </FormGrid>
        <FormGrid fullWidth>
          <RichTextEditor
            id="jobRequirements"
            name="jobRequirements"
            label="Job Requirements"
            placeholder="This field is required (min 10 characters)..."
            value={values.jobRequirements}
            error={Boolean(errors.jobRequirements && touched.jobRequirements)}
            helperText={touched.jobRequirements ? errors.jobRequirements : ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            required
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="jobPositionStatusId"
            name="jobPositionStatusId"
            label="Status"
            options={jobPositionStatusOptions}
            value={
              jobPositionStatusOptions
                .find((option) => option.id === values.jobPositionStatusId)
                ?.id?.toString() ?? ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode}
            error={Boolean(errors.jobPositionStatusId && touched.jobPositionStatusId)}
            helperText={touched.jobPositionStatusId ? errors.jobPositionStatusId : ''}
            required
          />
        </FormGrid>
      </FormGridRoot>
    </FormWrapper>
  );
};

export default JobPositionForm;
