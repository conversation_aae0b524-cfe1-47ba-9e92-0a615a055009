import type { ddValue } from 'src/shared/types/ddValue';
import type { EditMode } from 'src/shared/types/editMode';
import type { Errors } from 'src/shared/types/errorResonse';
import type { JobPosition } from 'src/shared/services/job-positions/job-postions.types';

import { Formik } from 'formik';
import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

import { jobPositionSchema } from 'src/utils/validation/careers/job-position/job-position.schema';
import { initialJobPosition } from 'src/utils/validation/careers/job-position/job-position.values';

import { useToast } from 'src/providers/ToastProvider';
import {
  updateJobPosition,
  createJobPosition,
  deleteJobPosition,
  getJobPositionById,
  getDepartmentOptions,
  getJobPositionTypeOptions,
  getJobPositionStatusOptions,
} from 'src/shared/services/job-positions/job-positions.service';

import Loader from 'src/components/common/loader/loader/Loader';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import FormAction from './components/form-actions/FormAction';
import { CardWrapper } from './JobPositionDetailsContainer.style';
import JobPositionForm from './components/job-position-form/JobPositionForm';

export const JobPositionDetailsContainer = () => {
  const { positionId } = useParams<{ positionId: string }>();
  const navigate = useNavigate();
  const { showToast } = useToast();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [jobPositionStatusOptions, setJobPositionStatusOptions] = useState<ddValue[]>([]);
  const [jobPositionTypeOptions, setJobPositionTypeOptions] = useState<ddValue[]>([]);
  const [departmentOptions, setDepartmentOptions] = useState<ddValue[]>([]);
  const [edit, setEdit] = useState<EditMode<JobPosition>>({} as EditMode<JobPosition>);
  const [notFound, setNotFound] = useState<boolean>(false);

  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true);
      let jobPosition: JobPosition | undefined;
      let isRead = false;

      if (positionId === 'new') {
        jobPosition = {} as JobPosition;
      } else if (Number.isInteger(Number(positionId))) {
        jobPosition = await getJobPositionById(Number(positionId));
        isRead = true;
        if (!jobPosition) {
          setNotFound(true);
          return;
        }
      } else {
        setNotFound(true);
        return;
      }

      setEdit({
        data: jobPosition as JobPosition,
        isEdit: false,
        isRead,
      });

      const [jobPositionStatuses, jobPositionTypes, departmentData] = await Promise.all([
        getJobPositionStatusOptions(),
        getJobPositionTypeOptions(),
        getDepartmentOptions(),
      ]);

      setJobPositionStatusOptions(jobPositionStatuses);
      setJobPositionTypeOptions(jobPositionTypes);
      setDepartmentOptions(departmentData);
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [positionId]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  const handleEditMode = () => {
    setEdit((prev) => ({ ...prev, isEdit: true, isRead: false }));
  };

  const handleCancel = () => {
    setEdit((prev) => ({ ...prev, isEdit: false, isRead: true }));
  };

  const handleDelete = async () => {
    if (!edit?.data?.id) return;
    setIsLoading(true);
    try {
      await deleteJobPosition(edit.data.id);
      showToast('Job Position Successfully Deleted', 'success');
      navigate('/careers/positions', { replace: true });
    } catch (error) {
      const errorResponse = error as Errors;
      const errorMessage =
        typeof errorResponse.errors === 'string'
          ? errorResponse.errors
          : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSubmit = async (values: JobPosition) => {
    setIsLoading(true);
    try {
      const postData: Partial<JobPosition> = {
        id: positionId === 'new' ? 0 : Number(positionId),
        departmentId: Number(values.departmentId),
        positionTitle: values.positionTitle,
        jobPositionTypeId: Number(values.jobPositionTypeId),
        jobPositionStatusId: Number(values.jobPositionStatusId),
        jobDescription: values.jobDescription,
        jobRequirements: values.jobRequirements,
        jobCode: values.jobCode,
        createdById: 2,
        lastModifiedById: 2,
      };

      if (positionId === 'new') {
        const created = await createJobPosition(postData);
        if (created && created?.id) {
          navigate(`/careers/positions/${created.id}`, { replace: true });
        }

        showToast('Job Position Successfully Created', 'success');
      } else {
        await updateJobPosition(postData);
        showToast('Job Position Successfully Updated', 'success');
      }
      await fetchInitialData();
      setEdit((prev) => ({ ...prev, isEdit: false, isRead: true }));
    } catch (error) {
      const errorResponse = error as Errors;

      const errorMessage =
        typeof errorResponse.errors === 'string'
          ? errorResponse.errors
          : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainContent
      pageTitle={`Job Position ${!notFound ? `| ${edit?.data?.id || 'New'}` : 'Invalid ID'}`}
    >
      <Loader loading={isLoading} />
      <Formik<JobPosition>
        initialValues={initialJobPosition(edit?.data as JobPosition)}
        onSubmit={handleFormSubmit}
        validationSchema={jobPositionSchema}
        enableReinitialize
      >
        {() => (
          <>
            <CardWrapper>
              <JobPositionForm
                readMode={Boolean(edit?.isRead)}
                departmentOptions={departmentOptions}
                jobPositionTypeOptions={jobPositionTypeOptions}
                jobPositionStatusOptions={jobPositionStatusOptions}
              />
            </CardWrapper>
            <FormAction
              handleCancel={handleCancel}
              handleEditAction={handleEditMode}
              readMode={Boolean(edit?.isRead)}
              isLoading={isLoading}
              handleDeleteAction={handleDelete}
            />
          </>
        )}
      </Formik>
    </MainContent>
  );
};
