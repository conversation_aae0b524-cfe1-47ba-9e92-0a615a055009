import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';

import { useFormikContext } from 'formik';

import { Button } from '@mui/material';

import { ButtonRoot, FormActionRoot, ViewHistoryLink, FormActionWrapper } from './FormAction.style';

export interface FormActionProps {
  handleOpen: () => void;
}

const FormAction = ({ handleOpen }: FormActionProps) => {
  const { resetForm, dirty, handleSubmit } = useFormikContext<QuoteRequest>();

  const handleResetForm = () => {
    resetForm();
  };

  return (
    <FormActionRoot>
      <FormActionWrapper alignItems="center" justifyContent="space-between" direction="row">
        <ViewHistoryLink color="primary" onClick={handleOpen}>
          View Activity History
        </ViewHistoryLink>
        <ButtonRoot>
          <Button variant="contained" color="error" onClick={handleResetForm} disabled={!dirty}>
            Clear
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleSubmit()}
            disabled={!dirty}
          >
            Update
          </Button>
        </ButtonRoot>
      </FormActionWrapper>
    </FormActionRoot>
  );
};

export default FormAction;
