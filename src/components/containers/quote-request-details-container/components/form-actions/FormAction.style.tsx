import { styled } from '@mui/material/styles';
import { Card, Link, Stack } from '@mui/material';

export const FormActionRoot = styled(Card)(({ theme }) => ({
  position: 'sticky',
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
  width: '100%',
  boxShadow: 'none',
  zIndex: theme.zIndex.drawer + 99,
  borderRadius: 0,
}));

export const FormActionWrapper = styled(Stack)(({ theme }) => ({
  padding: theme.spacing(2, 4),
  width: '100%',
}));

export const ViewHistoryLink = styled(Link)(() => ({
  cursor: 'pointer',
}));

export const ButtonRoot = styled('div')(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(2),
  marginTop: theme.spacing(1),
}));
