import type { EditMode } from 'src/shared/types/editMode';
import type { Errors } from 'src/shared/types/errorResonse';
import type { ddValue, ServiceDDValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';

import { Formik } from 'formik';
import { useParams } from 'react-router-dom';
import { useState, useEffect, useCallback } from 'react';

import { appendZeroes } from 'src/utils/helper';
import { initialQuoteRequest } from 'src/utils/validation/quote-request/QuoteRequest.values';

import { useToast } from 'src/providers/ToastProvider';
import { getUsers } from 'src/shared/services/admin/users/users.service';
import { LogTableName, LogColumnName } from 'src/shared/enums/logs.enums';
import {
  updateQuoteRequest,
  getQuoteRequestById,
  getPreferredContactMethods,
  getQuoteRequestStatusOptions,
  getQuoteRequestServiceOptions,
} from 'src/shared/services/quote-request/quote-request.service';

import LogDialog from 'src/components/common/log-dialog';
import Loader from 'src/components/common/loader/loader/Loader';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';
import { quoteRequestSchema } from 'src/components/containers/quote-request-container/schema/quote-request.schema';

import FormAction from './components/form-actions/FormAction';
import { CardWrapper } from './QuoteRequestDetailsContainer.style';
import QuoteRequestForm from '../quote-request-container/components/quote-request-form/QuoteRequestForm';

const QuoteRequestDetailsContainer = () => {
  const { quoteId } = useParams<{ quoteId: string }>();
  const { showToast } = useToast();

  const [edit, setEdit] = useState<EditMode<QuoteRequest>>({} as EditMode<QuoteRequest>);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [openLogsModal, setOpenLogsModal] = useState<boolean>(false);
  const [quoteRequestStatusOptions, setQuoteRequestStatusOptions] = useState<ddValue[]>([]);
  const [quoteRequestServiceOptions, setQuoteRequestServiceOptions] = useState<ServiceDDValue[]>(
    []
  );

  const [preferredContactMethodOptions, setPreferredContactMethodOptions] = useState<ddValue[]>([]);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);
  const [adminOptions, setAdminOptions] = useState<AdminUser[]>([]);

  const selectAdminOptions: ddValue[] = adminOptions.map((admin) => ({
    id: admin.id,
    name: admin.fullName || `${admin.firstName} ${admin.lastName}`,
    isDisabled: false,
  }));

  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true);

      const [users, quoteRequest, statusData, serviceOptions, preferredContactMethodData] =
        await Promise.all([
          getUsers(),
          getQuoteRequestById(Number(quoteId)),
          getQuoteRequestStatusOptions(),
          getQuoteRequestServiceOptions(),
          getPreferredContactMethods(),
        ]);

      if (Array.isArray(users)) {
        setAdminOptions(users);
      }

      setEdit((prev) => ({ ...prev, data: quoteRequest }));
      setSelectedAdmin(quoteRequest?.assignedAdmin || null);

      setQuoteRequestStatusOptions(statusData);
      setQuoteRequestServiceOptions(serviceOptions);
      setPreferredContactMethodOptions(preferredContactMethodData);

      if (quoteRequest.assignedAdminId && Array.isArray(users)) {
        const admin = users.find((user) => user?.id === quoteRequest.assignedAdminId);
        if (admin) {
          setSelectedAdmin(admin);
        }
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [quoteId]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  const handleOpenLogs = () => {
    setOpenLogsModal(true);
  };

  const handleCloseLogs = () => {
    setOpenLogsModal(false);
  };

  const handleFormSubmit = async (values: QuoteRequest) => {
    setIsLoading(true);

    try {
      const postData: QuoteRequest = {
        ...values,
        adminActionRemark: values.adminActionRemark,
        preferredContactMethodId: values.preferredContactMethodId,
        assignedAdminId: values.assignedAdminId || selectedAdmin?.id,
        statusId: values.statusId,
        serviceId: values.serviceId,
        serviceSubCategoryId: values.serviceSubCategoryId,
      };
      await updateQuoteRequest(postData);

      await fetchInitialData();
    } catch (error: any) {
      const errorResponse = error as Errors;

      const errorMessage =
        typeof errorResponse.errors === 'string'
          ? errorResponse.errors
          : Object.values(errorResponse.errors)[0] || 'An unknown error occurred';

      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainContent pageTitle={`Quote Request | ${appendZeroes(Number(quoteId), 4)}`}>
      <Loader loading={isLoading} />
      <Formik<QuoteRequest>
        initialValues={initialQuoteRequest(edit?.data as QuoteRequest)}
        onSubmit={handleFormSubmit}
        validationSchema={quoteRequestSchema}
        enableReinitialize
      >
        {() => (
          <>
            <CardWrapper>
              <QuoteRequestForm
                readMode={isLoading}
                quoteRequestStatusOptions={quoteRequestStatusOptions}
                quoteRequestServiceOptions={quoteRequestServiceOptions}
                preferredContactMethodOptions={preferredContactMethodOptions}
                selectedAdmin={selectedAdmin}
                adminOptions={selectAdminOptions}
              />

              <LogDialog
                open={openLogsModal}
                onClose={handleCloseLogs}
                id={Number(quoteId)}
                tableName={LogTableName.QUOTE_REQUEST_TABLE}
                columnName={LogColumnName.QUOTE_REQUEST_COLUMN}
              />
            </CardWrapper>
            <FormAction handleOpen={handleOpenLogs} />
          </>
        )}
      </Formik>
    </MainContent>
  );
};

export default QuoteRequestDetailsContainer;
