import { LoadingButton } from '@mui/lab';
import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

export const ResetPasswordWrapper = styled('div')(({ theme }) => ({
  width: '100%',
  height: '100%',
}));

export const ResetFormTitle = styled(Typography)(({ theme }) => ({
  fontWeight: theme.typography.fontWeightBold,
  marginBottom: '1.5rem',
}));

export const ResetPasswordForm = styled('form')(() => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  alignContent: 'flex-start',
  gap: '10px',
  width: '100%',
  marginInline: 'auto',
}));

export const ResetPassLoadingButton = styled(LoadingButton)(({ theme }) => ({
  background: '#B92F12',
  marginTop: '1rem',
  '&:hover': {
    background: '#c93618',
  },
}));
