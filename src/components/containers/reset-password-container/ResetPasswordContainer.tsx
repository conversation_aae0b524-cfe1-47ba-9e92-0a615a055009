import type { Errors } from 'src/shared/types/errorResonse';
import type { TFormEvent } from 'src/components/common/forms/types/event.types';

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { Stack, IconButton } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { validate } from 'src/utils/validation/auth/form-validation';

import { useToast } from 'src/providers/ToastProvider';
import { createPassword } from 'src/shared/services/auth/auth.service';

import Loader from 'src/components/common/loader/loader/Loader';
import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import {
  ResetFormTitle,
  ResetPasswordForm,
  ResetPasswordWrapper,
  ResetPassLoadingButton,
} from './ResetPasswordContainer.style';

interface ResetPasswordProps {
  token: string | null;
}

interface PasswordButtonActionProps {
  handleClickShowPassword: () => void;
  showPassword: boolean;
}

type ResetPasswordStateProps = {
  password: string;
  confirmPassword: string;
};
type ResetPasswordErrStateProps = {
  password: string;
  confirmPassword: string;
};

const ResetPasswordContainer = ({ token }: ResetPasswordProps) => {
  const { showToast } = useToast();

  const navigate = useNavigate();
  const showPassword = useBoolean();
  const showConfirmPassword = useBoolean();
  const [resetPassForm, setResetPassForm] = useState({} as ResetPasswordStateProps);
  const [errors, setErrors] = useState({} as ResetPasswordErrStateProps);
  const [loading, setLoading] = useState(false);

  const url = window.location.href;

  console.log(url);

  const checkToken = () => {
    if (!token) {
      navigate(paths.auth.login);
    }
  };

  useEffect(() => {
    checkToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setResetPassForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: TFormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Ensure token is valid

    checkToken();

    // Validate password and confirm password

    const { isValidPass, passErrorMsg, confirmPassErrorMsg, globalErrorMsg } =
      validate.isSamePassword({
        password: resetPassForm.password,
        confirmPassword: resetPassForm.confirmPassword,
      });

    if (isValidPass) {
      try {
        const { confirmPassword, password } = resetPassForm;
        await createPassword({ confirmPassword, password, url });
        showToast('Password updated successfully', 'success');

        navigate(paths.auth.login, { replace: true });
      } catch (error) {
        const apiError = error as Errors;
        const errorMessage =
          typeof apiError.errors === 'string'
            ? apiError.errors
            : Object.values(apiError.errors)[0] || 'An unknown error occurred';

        Object.values(errorMessage).forEach((message) => {
          showToast(message, 'error');
        });
      } finally {
        setResetPassForm({ confirmPassword: '', password: '' });
        setLoading(false);
      }
    } else {
      setErrors({
        password: passErrorMsg || globalErrorMsg,
        confirmPassword: confirmPassErrorMsg || globalErrorMsg,
      });
    }

    setLoading(false);
  };

  return (
    <ResetPasswordWrapper>
      <Loader loading={loading} />

      <ResetPasswordForm onSubmit={handleSubmit}>
        <ResetFormTitle variant="subtitle1">Reset your Password</ResetFormTitle>
        <Stack direction="column" spacing={2}>
          <CustomTextField
            id="password"
            name="password"
            label="New Password"
            type={showPassword.value ? 'text' : 'password'}
            value={resetPassForm.password ?? ''}
            variant="outlined"
            handleChange={handleInputChange}
            handleBlur={() => {}}
            error={!!errors.password}
            helperText={errors.password}
            endAdornment={
              <PasswordButtonAction
                handleClickShowPassword={() => showPassword.onToggle()}
                showPassword={showPassword.value}
              />
            }
          />
          <CustomTextField
            id="confirmPassword"
            name="confirmPassword"
            label="Confirm Password"
            type={showConfirmPassword.value ? 'text' : 'password'}
            value={resetPassForm.confirmPassword ?? ''}
            variant="outlined"
            handleChange={handleInputChange}
            handleBlur={() => {}}
            error={!!errors.confirmPassword}
            helperText={errors.confirmPassword}
            endAdornment={
              <PasswordButtonAction
                handleClickShowPassword={() => showConfirmPassword.onToggle()}
                showPassword={showConfirmPassword.value}
              />
            }
          />
          <ResetPassLoadingButton
            fullWidth
            size="large"
            type="submit"
            variant="contained"
            loading={loading}
            disabled={loading}
            onClick={handleSubmit}
          >
            Reset Password
          </ResetPassLoadingButton>
        </Stack>
      </ResetPasswordForm>
    </ResetPasswordWrapper>
  );
};

const PasswordButtonAction = ({
  handleClickShowPassword,
  showPassword,
}: PasswordButtonActionProps) => (
  <IconButton
    edge="start"
    onClick={handleClickShowPassword}
    aria-label={showPassword ? 'Hide password' : 'Show password'}
  >
    {showPassword ? (
      <Iconify icon={Icons.password.eyeOpen} />
    ) : (
      <Iconify icon={Icons.password.eyeClose} />
    )}
  </IconButton>
);

export default ResetPasswordContainer;
