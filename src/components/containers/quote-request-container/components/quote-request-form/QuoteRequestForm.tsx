import type { ddValue, ServiceDDValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';

import { memo } from 'react';
import { useFormikContext } from 'formik';

import { Box } from '@mui/material';

import CustomSelect from 'src/components/common/forms/select/Select';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid, FormGridRoot, AdminSectionTitle } from './QuoteRequestForm.style';

interface QuoteRequestFormProps {
  readMode: boolean;
  quoteRequestStatusOptions: ddValue[];
  quoteRequestServiceOptions: ServiceDDValue[];
  preferredContactMethodOptions: ddValue[];
  selectedAdmin: AdminUser | null;
  adminOptions: ddValue[];
}

const QuoteRequestForm = ({
  readMode,
  quoteRequestStatusOptions,
  quoteRequestServiceOptions,
  preferredContactMethodOptions,
  selectedAdmin,
  adminOptions,
}: QuoteRequestFormProps) => {
  const { values, handleChange, handleBlur, errors, touched } = useFormikContext<QuoteRequest>();

  const serviceOptions = quoteRequestServiceOptions.map((service) => ({
    id: service.id,
    name: service.name,
    isDisabled: false,
  })) as ddValue[];

  const serviceSubCategoryOptions = quoteRequestServiceOptions.flatMap((service) =>
    service.subCategories.map((subCategory) => ({
      id: subCategory.id,
      name: subCategory.name,
      isDisabled: false,
    }))
  ) as ddValue[];

  return (
    <Box role="presentation">
      <FormGridRoot>
        <FormGrid>
          <CustomTextField
            id="name"
            name="name"
            label="Name"
            value={values.name}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.name && touched.name)}
            helperText={touched.name ? errors.name : ''}
            variant="outlined"
            type="text"
          />
        </FormGrid>

        <FormGrid>
          <CustomTextField
            id="emailId"
            name="emailId"
            label="Email"
            type="email"
            variant="outlined"
            value={values.emailId}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.emailId && touched.emailId)}
            helperText={touched.emailId ? errors.emailId : ''}
          />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot columns="repeat(4, 1fr)">
        <FormGrid>
          <CustomTextField
            id="phone"
            name="phone"
            label="Contact Number"
            type="text"
            variant="outlined"
            value={values.phone}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.phone && touched.phone)}
            helperText={touched.phone ? errors.phone : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="preferredContactMethodId"
            name="preferredContactMethodId"
            label="Preferred Contact Method"
            options={preferredContactMethodOptions}
            value={
              preferredContactMethodOptions
                ?.find((option) => option.id === values.preferredContactMethodId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.preferredContactMethodId && touched.preferredContactMethodId)}
            helperText={
              touched.preferredContactMethodId ? (errors.preferredContactMethodId as string) : ''
            }
            required
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="serviceId"
            name="serviceId"
            label="Service Category"
            options={serviceOptions}
            value={
              serviceOptions?.find((option) => option.id === values.serviceId)?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.service && touched.service)}
            helperText={touched.service ? (errors.service as string) : ''}
            required
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="serviceSubCategoryId"
            name="serviceSubCategoryId"
            label="Sub Category"
            options={serviceSubCategoryOptions}
            value={
              serviceSubCategoryOptions
                ?.find((option) => option.id === values.serviceSubCategoryId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.serviceSubCategory && touched.serviceSubCategory)}
            helperText={touched.serviceSubCategory ? (errors.serviceSubCategory as string) : ''}
            required
          />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid>
          <CustomTextField
            id="origin"
            name="origin"
            label="Origin"
            type="text"
            variant="outlined"
            value={values.origin}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.origin && touched.origin)}
            helperText={touched.origin ? errors.origin : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomTextField
            id="destination"
            name="destination"
            label="Destination"
            type="text"
            variant="outlined"
            value={values.destination}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.destination && touched.destination)}
            helperText={touched.destination ? errors.destination : ''}
          />
        </FormGrid>

        <FormGrid fullWidth>
          <CustomTextField
            id="message"
            name="message"
            label="Message"
            type="text"
            variant="outlined"
            value={values.message}
            handleChange={handleChange}
            handleBlur={handleBlur}
            multiline
            minRows={5}
            maxRows={5}
            disabled
            error={Boolean(errors.message && touched.message)}
            helperText={touched.message ? errors.message : ''}
          />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid fullWidth>
          <AdminSectionTitle variant="subtitle1">Admin Action</AdminSectionTitle>
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="assignedAdminId"
            name="assignedAdminId"
            label="Assign Admin"
            placeholder="Select admin"
            options={adminOptions}
            value={
              (adminOptions
                ?.find((option) => option.id === values.assignedAdminId)
                ?.id?.toString() ||
                selectedAdmin?.id.toString()) ??
              ''
            }
            handleChange={handleChange}
            error={Boolean(errors.assignedAdminId && touched.assignedAdminId)}
            helperText={touched.assignedAdminId ? (errors.assignedAdminId as string) : ''}
            disabled={readMode}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="statusId"
            name="statusId"
            label="Status"
            options={quoteRequestStatusOptions}
            value={
              quoteRequestStatusOptions
                ?.find((option) => option?.id === values.statusId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={readMode || !values?.assignedAdminId}
            error={Boolean(errors.statusId && touched.statusId)}
            helperText={touched.statusId ? errors.statusId : ''}
          />
        </FormGrid>

        <FormGrid fullWidth>
          <CustomTextField
            id="adminActionRemark"
            name="adminActionRemark"
            label="Remark"
            type="text"
            variant="outlined"
            multiline
            minRows={5}
            maxRows={5}
            value={values.adminActionRemark ?? ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            error={Boolean(errors.adminActionRemark && touched.adminActionRemark)}
            helperText={touched.adminActionRemark ? errors.adminActionRemark : ''}
          />
        </FormGrid>
      </FormGridRoot>
    </Box>
  );
};

export default memo(QuoteRequestForm);
