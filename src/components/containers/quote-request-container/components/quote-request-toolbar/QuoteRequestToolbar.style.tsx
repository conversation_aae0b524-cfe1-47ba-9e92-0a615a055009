import { styled } from '@mui/material/styles';

export const QuoteRequestToolbarWrapper = styled('div')(({ theme }) => ({}));

export const QuoteRequestFields = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingBlock: theme.spacing(2.5),
}));

export const QuoteRequestToolbarLeft = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  gap: '1rem',
}));

export const QuoteRequestToolbarRight = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
  minWidth: '420px',
}));

export const QuoteRequestToolbarFormControl = styled('div')(({ theme }) => ({
  flexShrink: 0,
  width: 200,
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

export const QuoteRequestToolbarSearchWrapper = styled('div')(() => ({
  width: '60%',
  minWidth: '200px',
}));

export const QuoteRequestFiltersView = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  marginLeft: '1rem',
}));
