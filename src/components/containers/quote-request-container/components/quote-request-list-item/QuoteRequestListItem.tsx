import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';

import { useNavigate } from 'react-router';

import { Stack, TableRow, TableCell, IconButton } from '@mui/material';

import { appendZeroes } from 'src/utils/helper';
import { formatDate, formatTime } from 'src/utils/date';
import { copyToClipboard } from 'src/utils/copyToClipboard';

import { useToast } from 'src/providers/ToastProvider';

import { IconShare } from 'src/components/common/icons';
import IconCopy from 'src/components/common/icons/IconCopy';
import StatusChip from 'src/components/common/status-chip/StatusChip';

import {
  CopyWrapper,
  DateTimeRoot,
  EmailWrapper,
  DateFormatWrapper,
  TimeFormatWrapper,
  ServiceNameWrapper,
  UserNameLinkWrapper,
} from './QuoteRequestListItem.style';

interface QuoteRequestListItemProps {
  request: QuoteRequest;
}

const QuoteRequestListItem = ({ request }: QuoteRequestListItemProps) => {
  const navigate = useNavigate();
  const { showToast } = useToast();

  const formattedDate = formatDate(request?.createdDate ?? '');
  const formattedTime = formatTime(request?.createdDate ?? '');

  const handleRowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/quote-requests/${request.id}`, { replace: true });
  };

  const handleCopyClick = (e: React.MouseEvent, text: string) => {
    e.stopPropagation();
    copyToClipboard(text);

    showToast(`Copied to Clipboard`, 'success');
  };

  return (
    <TableRow hover onClick={handleRowClick} style={{ cursor: 'pointer' }}>
      <TableCell>{appendZeroes(request.id, 4) ?? '---'}</TableCell>
      <TableCell>
        <Stack spacing={2} direction="row" alignItems="center">
          <UserNameLinkWrapper color="inherit">{request.name ?? '---'}</UserNameLinkWrapper>
        </Stack>
      </TableCell>
      <TableCell>
        <CopyWrapper onClick={(e) => handleCopyClick(e, request?.phone?.toString())}>
          <span>{request?.phone ?? '---'}</span>
          <IconCopy style={{ width: 18 }} />
        </CopyWrapper>
      </TableCell>
      <EmailWrapper>
        <CopyWrapper onClick={(e) => handleCopyClick(e, request?.emailId?.toString())}>
          <span>{request?.emailId ?? '---'}</span>
          <IconCopy style={{ width: 18 }} />
        </CopyWrapper>
      </EmailWrapper>
      <TableCell>
        <DateTimeRoot>
          <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
          <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
        </DateTimeRoot>
      </TableCell>
      <TableCell>
        <ServiceNameWrapper>{request.service?.name ?? '---'}</ServiceNameWrapper>
      </TableCell>
      <TableCell>
        <StatusChip
          variant="soft"
          label={request.status?.name ?? '---'}
          color={request.status?.colorCode ?? '#000000'}
        />
      </TableCell>
      <TableCell align="right">
        <IconButton onClick={handleRowClick}>
          <IconShare style={{ width: 24 }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default QuoteRequestListItem;
