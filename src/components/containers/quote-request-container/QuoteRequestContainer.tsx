import type { SelectChangeEvent } from '@mui/material';
import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { QuoteRequest } from 'src/shared/services/quote-request/quote-request.type';

import { useMemo, useState, useEffect, useCallback } from 'react';

import { useDebounce } from 'src/hooks/use-debounce';

import { exportToExcel } from 'src/utils/exportToExcel';

import { getUsers } from 'src/shared/services/admin/users/users.service';
import {
  searchQuoteRequests,
  getQuoteRequestStatusOptions,
} from 'src/shared/services/quote-request/quote-request.service';

import TableView from 'src/components/common/table/view/TableView';
import { IconRefresh, IconDownload } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import QuoteRequestToolbar from './components/quote-request-toolbar/QuoteRequestToolbar';
import QuoteRequestListItem from './components/quote-request-list-item/QuoteRequestListItem';
import {
  QuoteRequestWrapper,
  QuoteRequestFilledIconButton,
  QuoteRequestOutlinedIconButton,
} from './QuoteRequestContainer.style';

const TABLE_HEAD = [
  { id: 'id', label: 'ID' },
  { id: 'name', label: 'Name', width: 300 },
  { id: 'contactNumber', label: 'Contact', width: 120 },
  { id: 'email', label: 'Email', width: 300 },
  { id: 'date', label: 'Date', width: 200 },
  { id: 'service', label: 'Service', width: 250 },
  { id: 'status', label: 'Status', width: 100 },
  { id: 'action', label: '' },
];

const QuoteRequestContainer = () => {
  const [tableData, setTableData] = useState<QuoteRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  const [searchKey, setSearchKey] = useState('');
  const debouncedSearchKey = useDebounce(searchKey, 500);
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);

  const [selectedAssignedAdmin, setSelectedAssignedAdmin] = useState<ddValue>({} as ddValue);

  const [quoteRequestStatusOptions, setQuoteRequestStatusOptions] = useState<ddValue[]>([]);
  const [assignedAdminOptions, setAssignedAdminOptions] = useState<ddValue[]>([]);

  const getQuoteRequestData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };

      const { searchResult, count } = await searchQuoteRequests(criteria);
      setTableData(searchResult);
      setTotalCount(count);
      setFetchError(null);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch quote requests');
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchKey, filters, currentPage, rowsPerPage]);

  useEffect(() => {
    getQuoteRequestData();
  }, [getQuoteRequestData]);

  useEffect(() => {
    let isMounted = true;

    const loadOptions = async () => {
      try {
        const [statusData, adminUsers] = await Promise.all([
          getQuoteRequestStatusOptions(),
          getUsers(),
        ]);

        if (isMounted) {
          const adminData = (adminUsers as AdminUser[]).map((user) => ({
            id: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}`,
            isDisabled: false,
          }));

          setQuoteRequestStatusOptions(statusData);
          setAssignedAdminOptions(adminData);
        }
      } catch (error: any) {
        setFetchError(error.message || 'Failed to fetch options');
      }
    };

    loadOptions();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, remove = false) => {
    setFilters((prev) =>
      remove
        ? prev.filter((f) => f.field !== filter.field)
        : [...prev.filter((f) => f.field !== filter.field), filter]
    );
    setCurrentPage(1);
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(Number(event.target.value));
    setCurrentPage(1);
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
    setCurrentPage(1);
  }, []);

  const handleAssignedAdminChange = useCallback((admin: ddValue): void => {
    setSelectedAssignedAdmin(admin);
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getQuoteRequestData();
  }, [getQuoteRequestData]);

  const handleExcelDownload = () => {
    exportToExcel(
      tableData,
      [
        'id',
        'name',
        'phone',
        'emailId',
        'message',
        'preferredContactMethod.name',
        'origin',
        'destination',
        'distanceInKm',
        'service.name',
        'serviceSubCategory.name',
        'status.name',
      ],
      'quote-request.xlsx'
    );
  };

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <MainContent
      pageTitle="Quote Requests"
      buttons={
        <>
          <QuoteRequestOutlinedIconButton onClick={handleRefreshData}>
            <IconRefresh style={{ width: 18 }} />
          </QuoteRequestOutlinedIconButton>
          <QuoteRequestFilledIconButton onClick={handleExcelDownload}>
            <IconDownload style={{ width: 18 }} />
          </QuoteRequestFilledIconButton>
        </>
      }
    >
      <QuoteRequestWrapper>
        <QuoteRequestToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          quoteRequestStatusOptions={quoteRequestStatusOptions}
          assignedAdminOptions={assignedAdminOptions}
          selectedStatus={selectedStatus}
          selectedAssignedAdmin={selectedAssignedAdmin}
          onStatusChange={handleStatusUpdate}
          onAssignedAdminChange={handleAssignedAdminChange}
          onFilterChange={handleFilterChange}
          totalCount={totalCount}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              currentPage={currentPage}
              onNext={() => setCurrentPage((prev) => prev + 1)}
              onPrev={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
              totalCount={totalCount}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData?.map((row) => <QuoteRequestListItem key={row.id} request={row} />)}
        </TableView>
      </QuoteRequestWrapper>
    </MainContent>
  );
};

export default QuoteRequestContainer;
