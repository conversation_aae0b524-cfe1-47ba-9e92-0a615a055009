import * as Yup from 'yup';

export const quoteRequestSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  emailId: Yup.string().required('Email is required').email('Invalid email format'),
  phone: Yup.string()
    .required('Contact number is required')
    .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit Indian mobile number'),
  preferredContactMethodId: Yup.string().required('Preferred Contact Method is Required'),
  serviceId: Yup.string().required('Service is Required'),
  serviceSubCategoryId: Yup.string().required('Sub Category is required'),
  origin: Yup.string().required('Origin is required'),
  destination: Yup.string().required('Destination is required'),
  message: Yup.string().required('Message is required'),
  statusId: Yup.string().required('Status is required'),
  assignedAdminId: Yup.string().required('Admin selection is required'),
  adminActionRemark: Yup.string().nullable(),
});

export type QuoteRequestValidationType = Yup.InferType<typeof quoteRequestSchema>;
