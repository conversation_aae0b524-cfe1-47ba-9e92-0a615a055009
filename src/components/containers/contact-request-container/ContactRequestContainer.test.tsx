import { render, screen, waitFor } from '@testing-library/react';

import ContactRequestList from './ContactRequestContainer';

jest.mock('src/shared/services/contact-request/contact-request.service', () => ({
  searchContactQuoteRequests: jest.fn(() =>
    Promise.resolve({ count: 1, searchResult: [{ id: 1, name: '<PERSON>' }] })
  ),
  getContactRequestStatusOptions: jest.fn(() => Promise.resolve([])),
}));
jest.mock('src/shared/services/admin/users/users.service', () => ({
  getUsers: jest.fn(() => Promise.resolve([])),
}));
jest.mock('src/utils/exportToExcel', () => ({ exportToExcel: jest.fn() }));
jest.mock('src/hooks/use-debounce', () => ({
  __esModule: true,
  default: (v: any) => v,
  useDebounce: (v: any) => v,
}));
jest.mock('src/components/common/table/view/TableView', () => ({ children }: any) => (
  <div data-testid="table-view">{children}</div>
));
jest.mock('src/components/common/table/pagination/TablePagination', () => () => (
  <div data-testid="table-pagination" />
));
jest.mock(
  'src/components/common/main-content-wrapper/MainContentWrapper',
  () =>
    ({ children }: any) => <div>{children}</div>
);
jest.mock('./components/contact-request-toolbar/ContactRequestToolbar', () => () => (
  <div data-testid="toolbar" />
));
jest.mock('./components/contact-request-list-item/ContactRequestListItem', () => () => (
  <div data-testid="list-item" />
));

describe('ContactRequestList', () => {
  it('renders table and toolbar', async () => {
    render(<ContactRequestList />);
    expect(screen.getByTestId('toolbar')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('table-view')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByTestId('list-item')).toBeInTheDocument());
  });
});
