import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';
import type { ContactRequest } from 'src/shared/services/contact-request/contact-request.type';

import { useMemo, useState, useEffect, useCallback } from 'react';

import { type SelectChangeEvent } from '@mui/material';

import { useDebounce } from 'src/hooks/use-debounce';

import { exportToExcel } from 'src/utils/exportToExcel';

import { getUsers } from 'src/shared/services/admin/users/users.service';
import {
  searchContactQuoteRequests,
  getContactRequestStatusOptions,
} from 'src/shared/services/contact-request/contact-request.service';

import TableView from 'src/components/common/table/view/TableView';
import { IconRefresh, IconDownload } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import ContactRequestToolbar from './components/contact-request-toolbar/ContactRequestToolbar';
import ContactRequestListItem from './components/contact-request-list-item/ContactRequestListItem';
import {
  ContactRequestWrapper,
  ContactRequestFilledIconButton,
  ContactRequestOutlinedIconButton,
} from './ContactRequestContainer.style';

const TABLE_HEAD = [
  { id: 'id', label: 'ID' },
  { id: 'name', label: 'Name', width: 400 },
  { id: 'email', label: 'Email', width: 400 },
  { id: 'contactNumber', label: 'Contact', width: 150 },
  { id: 'date', label: 'Created Date', width: 80 },
  { id: 'reference', label: 'Reference', width: 200 },
  { id: 'subject', label: 'Subject', width: 200 },
  { id: 'status', label: 'Status', width: 100 },
  { id: 'action', label: '', width: 5, align: 'right' },
];

const ContactRequestList = () => {
  const [tableData, setTableData] = useState<ContactRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [contactRequestStatusOptions, setContactRequestStatusOptions] = useState<ddValue[]>([]);
  const [assignedAdminOptions, setAssignedAdminOptions] = useState<ddValue[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);
  const [selectedAssignedAdmin, setSelectedAssignedAdmin] = useState<ddValue>({} as ddValue);

  const debouncedSearchKey = useDebounce(searchKey, 500);

  const getContactRequestData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchContactQuoteRequests(criteria);
      setTotalCount(count);
      setTableData(searchResult);
      setFetchError(null);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch contact requests');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getContactRequestData();
  }, [getContactRequestData]);

  useEffect(() => {
    let isMounted = true;

    const loadOptions = async () => {
      try {
        const [statusData, adminUsers] = await Promise.all([
          getContactRequestStatusOptions(),
          getUsers(),
        ]);

        if (isMounted) {
          const adminData = (adminUsers as AdminUser[]).map((user) => ({
            id: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}`,
            isDisabled: false,
          }));

          setContactRequestStatusOptions(statusData);
          setAssignedAdminOptions(adminData);
        }
      } catch (error: any) {
        console.error('Failed to load options:', error);
      }
    };

    loadOptions();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleAssignedAdminChange = useCallback((admin: ddValue): void => {
    setSelectedAssignedAdmin(admin);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getContactRequestData();
  }, [getContactRequestData]);

  const handleExcelDownload = () => {
    exportToExcel(
      tableData,
      [
        'id',
        'name',
        'phone',
        'emailId',
        'message',
        'subject',
        'reference',
        'preferredContactMethod.name',
        'status.name',
      ],
      'contact-request.xlsx'
    );
  };

  const memoizedTableData = useMemo(() => tableData, [tableData]);

  return (
    <MainContent
      pageTitle="Contact Requests"
      buttons={
        <>
          <ContactRequestOutlinedIconButton
            color="primary"
            size="large"
            onClick={handleRefreshData}
          >
            <IconRefresh style={{ width: 18 }} />
          </ContactRequestOutlinedIconButton>
          <ContactRequestFilledIconButton onClick={handleExcelDownload}>
            <IconDownload style={{ width: 18 }} />
          </ContactRequestFilledIconButton>
        </>
      }
    >
      <ContactRequestWrapper>
        <ContactRequestToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          contactRequestStatusOptions={contactRequestStatusOptions}
          assignedAdminOptions={assignedAdminOptions}
          selectedStatus={selectedStatus}
          selectedAssignedAdmin={selectedAssignedAdmin}
          onStatusChange={handleStatusUpdate}
          onAssignedAdminChange={handleAssignedAdminChange}
          onFilterChange={handleFilterChange}
          totalCount={totalCount}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              totalCount={totalCount}
              currentPage={currentPage}
              onNext={() => setCurrentPage(currentPage + 1)}
              onPrev={() => setCurrentPage(currentPage - 1)}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData?.map((row) => <ContactRequestListItem key={row?.id} request={row} />)}
        </TableView>
      </ContactRequestWrapper>
    </MainContent>
  );
};

export default ContactRequestList;
