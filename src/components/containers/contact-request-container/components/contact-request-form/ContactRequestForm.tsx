import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { ContactRequest } from 'src/shared/services/contact-request/contact-request.type';

import React from 'react';
import { useFormikContext } from 'formik';

import CustomSelect from 'src/components/common/forms/select/Select';
import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

import { FormGrid, FormWrapper, FormGridRoot, AdminSectionTitle } from './ContactRequestForm.style';

interface ContactRequestFormProps {
  readMode: boolean;
  contactRequestStatusOptions: ddValue[];
  subjectOptions: ddValue[];
  referenceOptions: ddValue[];
  contactMethodData: ddValue[];
  selectedAdmin: AdminUser | null;
  adminOptions: ddValue[];
}

const ContactRequestForm = ({
  readMode,
  contactRequestStatusOptions,
  subjectOptions,
  referenceOptions,
  contactMethodData,
  selectedAdmin,
  adminOptions,
}: ContactRequestFormProps) => {
  const { values, handleChange, handleBlur, errors, touched } = useFormikContext<ContactRequest>();

  return (
    <FormWrapper role="presentation">
      <FormGridRoot>
        <FormGrid>
          <CustomTextField
            id="name"
            name="name"
            label="Name"
            type="text"
            variant="outlined"
            value={values.name}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.name && touched.name)}
            helperText={touched.name ? errors.name : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomTextField
            id="emailId"
            name="emailId"
            label="Email"
            type="email"
            variant="outlined"
            value={values.emailId}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.emailId && touched.emailId)}
            helperText={touched.emailId ? errors.emailId : ''}
          />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot columns="repeat(4, 1fr)">
        <FormGrid>
          <CustomTextField
            id="phone"
            name="phone"
            label="Contact Number"
            type="text"
            variant="outlined"
            value={values.phone}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.phone && touched.phone)}
            helperText={touched.phone ? errors.phone : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="reference"
            name="reference"
            label="Reference"
            options={referenceOptions}
            value={
              referenceOptions.find((option) => option.name === values.reference)?.id?.toString() ||
              values.reference
            }
            handleChange={(event) => {
              const selectedId = Number(event.target.value);
              const selectedReference = referenceOptions.find((option) => option.id === selectedId);

              handleChange({
                target: {
                  name: 'reference',
                  value: selectedReference?.name || '',
                },
              });
            }}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.reference && touched.reference)}
            helperText={touched.reference ? (errors.reference as string) : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="subject"
            name="subject"
            label="Subject"
            options={subjectOptions}
            value={
              subjectOptions.find((option) => option.name === values.subject)?.id?.toString() ||
              values.subject
            }
            handleChange={(event) => {
              const selectedId = Number(event.target.value);
              const selectedSubject = subjectOptions.find((option) => option.id === selectedId);

              handleChange({
                target: {
                  name: 'subject',
                  value: selectedSubject?.name || '',
                },
              });
            }}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.subject && touched.subject)}
            helperText={touched.subject ? (errors.subject as string) : ''}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="preferredContactMethodId"
            name="preferredContactMethodId"
            label="Preferred Contact Method"
            variant="outlined"
            options={contactMethodData}
            value={
              contactMethodData
                .find((option) => option.id === values.preferredContactMethodId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.preferredContactMethodId && touched.preferredContactMethodId)}
            helperText={
              touched.preferredContactMethodId ? (errors.preferredContactMethodId as string) : ''
            }
          />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid fullWidth>
          <CustomTextField
            id="message"
            name="message"
            label="Message"
            type="text"
            variant="outlined"
            multiline
            minRows={3}
            maxRows={3}
            value={values.message ?? ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled
            error={Boolean(errors.message && touched.message)}
            helperText={touched.message ? errors.message : ''}
          />
        </FormGrid>
      </FormGridRoot>

      <FormGridRoot>
        <FormGrid fullWidth>
          <AdminSectionTitle variant="subtitle1">Admin Action</AdminSectionTitle>
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="assignedAdminId"
            name="assignedAdminId"
            label="Assign Admin"
            placeholder="Select admin"
            options={adminOptions}
            value={
              (adminOptions
                .find((option) => option.id === values.assignedAdminId)
                ?.id?.toString() ||
                selectedAdmin?.id.toString()) ??
              ''
            }
            handleChange={handleChange}
            error={Boolean(errors.assignedAdminId && touched.assignedAdminId)}
            helperText={touched.assignedAdminId ? (errors.assignedAdminId as string) : ''}
            disabled={readMode}
          />
        </FormGrid>

        <FormGrid>
          <CustomSelect
            id="statusId"
            name="statusId"
            label="Status"
            options={contactRequestStatusOptions}
            value={
              contactRequestStatusOptions
                .find((option) => option.id === values.statusId)
                ?.id?.toString() || ''
            }
            handleChange={handleChange}
            handleBlur={handleBlur}
            disabled={!values?.assignedAdminId}
            error={Boolean(errors.statusId && touched.statusId)}
            helperText={touched.statusId ? errors.statusId : ''}
          />
        </FormGrid>

        <FormGrid fullWidth>
          <CustomTextField
            id="adminActionRemark"
            name="adminActionRemark"
            label="Remark"
            type="text"
            variant="outlined"
            multiline
            minRows={5}
            maxRows={5}
            value={values.adminActionRemark ?? ''}
            handleChange={handleChange}
            handleBlur={handleBlur}
            error={Boolean(errors.adminActionRemark && touched.adminActionRemark)}
            helperText={touched.adminActionRemark ? errors.adminActionRemark : ''}
          />
        </FormGrid>
      </FormGridRoot>
    </FormWrapper>
  );
};

export default ContactRequestForm;
