import { styled } from '@mui/material/styles';
import { Box, Grid, Typography } from '@mui/material';

export const FormWrapper = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  gridColumn: '1 / -1',
}));

export const FormGridRoot = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'columns',
})<{ columns?: string }>(({ theme, columns }) => ({
  display: 'grid',
  gridTemplateColumns: columns || 'repeat(2, 1fr)',
  gap: theme.spacing(2),
  columnGap: theme.spacing(4),
  width: '100%',

  '&:not(:last-child)': {
    marginBottom: theme.spacing(4),
  },

  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
  },

  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: '1fr',
  },
}));

export const FormGrid = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'fullWidth',
})<{ fullWidth?: boolean }>(({ fullWidth, theme }) => ({
  gridColumn: fullWidth ? '1 / -1' : undefined,
}));

export const AdminSectionTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightSemiBold,
  paddingBlock: theme.spacing(1),
}));

export const FormSection = styled(Grid)(() => ({
  width: '100%',
}));

export const FieldWrapper = styled(Grid)(() => ({
  width: '100%',
}));
