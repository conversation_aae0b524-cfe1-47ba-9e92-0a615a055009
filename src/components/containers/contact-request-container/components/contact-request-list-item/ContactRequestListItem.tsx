import type { ContactRequest } from 'src/shared/services/contact-request/contact-request.type';

import { useNavigate } from 'react-router';

import { Stack, TableRow, TableCell, IconButton } from '@mui/material';

import { appendZeroes } from 'src/utils/helper';
import { formatTime, formatDate } from 'src/utils/date';
import { copyToClipboard } from 'src/utils/copyToClipboard';

import { useToast } from 'src/providers/ToastProvider';

import { IconShare } from 'src/components/common/icons';
import IconCopy from 'src/components/common/icons/IconCopy';
import StatusChip from 'src/components/common/status-chip/StatusChip';

import {
  CopyWrapper,
  EmailWrapper,
  DateTimeRoot,
  DateFormatWrapper,
  TimeFormatWrapper,
  UserNameLinkWrapper,
} from './ContactRequestListItem.style';

interface ContactRequestListItemProps {
  request: ContactRequest;
}

const ContactRequestListItem = ({ request }: ContactRequestListItemProps) => {
  const navigate = useNavigate();
  const { showToast } = useToast();

  const formattedDate = formatDate(request?.createdDate ?? '');
  const formattedTime = formatTime(request?.createdDate ?? '');

  const handleRowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/contact-requests/${request.id}`, { replace: true });
  };

  const handleCopyClick = (e: React.MouseEvent, text: string) => {
    e.stopPropagation();
    copyToClipboard(text);

    showToast(`Copied to Clipboard`, 'success');
  };

  return (
    <TableRow hover onClick={handleRowClick}>
      <TableCell>{appendZeroes(request.id, 4) ?? '---'}</TableCell>
      <TableCell>
        <Stack spacing={2} direction="row" alignItems="center">
          <UserNameLinkWrapper color="inherit">{request.name ?? '---'}</UserNameLinkWrapper>
        </Stack>
      </TableCell>

      <EmailWrapper>
        <CopyWrapper onClick={(e) => handleCopyClick(e, request?.emailId?.toString())}>
          <span>{request?.emailId ?? '---'}</span>
          <IconCopy style={{ width: 18 }} />
        </CopyWrapper>
      </EmailWrapper>

      <TableCell>
        <CopyWrapper onClick={(e) => handleCopyClick(e, request?.phone?.toString())}>
          <span>{request?.phone ?? '---'}</span>
          <IconCopy style={{ width: 18 }} />
        </CopyWrapper>
      </TableCell>
      <TableCell>
        <DateTimeRoot>
          <DateFormatWrapper>{formattedDate}</DateFormatWrapper>
          <TimeFormatWrapper>{formattedTime}</TimeFormatWrapper>
        </DateTimeRoot>
      </TableCell>

      <TableCell>{request?.reference ?? '---'}</TableCell>
      <TableCell>{request?.subject ?? '---'}</TableCell>

      <TableCell>
        <StatusChip
          variant="soft"
          label={request.status?.name ?? '---'}
          color={request.status?.colorCode ?? '#000000'}
        />
      </TableCell>

      <TableCell align="right">
        <IconButton onClick={handleRowClick}>
          <IconShare style={{ width: 24 }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default ContactRequestListItem;
