import type { ddValue } from 'src/shared/types/ddValue';
import type { AdminUser } from 'src/shared/services/admin/users/users.types';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { memo } from 'react';

import CustomSelect from 'src/components/common/forms/select/Select';

interface AdminSearchProps {
  readMode: boolean;
  selectedAdmin: AdminUser | null;
  onAdminChange: (admin: AdminUser | null) => void;
  adminOptions: AdminUser[];
  error?: boolean;
  helperText?: string;
}

const AdminSearch = ({
  readMode,
  selectedAdmin,
  onAdminChange,
  adminOptions,
  error,
  helperText,
}: AdminSearchProps) => {
  const selectOptions: ddValue[] = adminOptions.map((admin) => ({
    id: admin.id,
    name: admin.fullName || `${admin.firstName} ${admin.lastName}`,
    isDisabled: false,
  }));

  const handleChange = (event: TInputChangeEvent) => {
    const selectedId = Number(event.target.value);
    const selectedAdminUser = adminOptions.find((admin) => admin.id === selectedId) || null;
    onAdminChange(selectedAdminUser);
  };

  return (
    <CustomSelect
      id="action.adminId"
      name="action.adminId"
      label="Assign Admin"
      placeholder="Select admin"
      options={selectOptions}
      value={selectedAdmin?.id?.toString() || ''}
      handleChange={handleChange}
      error={error}
      helperText={helperText}
      disabled={readMode}
    />
  );
};

export default memo(AdminSearch);
