import { styled } from '@mui/material/styles';

export const ContactRequestToolbarWrapper = styled('div')(({ theme }) => ({}));

export const ContactRequestFields = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingBlock: theme.spacing(2.5),
}));

export const ContactRequestToolbarLeft = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  gap: '1rem',
}));

export const ContactRequestToolbarRight = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
  minWidth: '420px',
}));

export const ContactRequestToolbarFormControl = styled('div')(({ theme }) => ({
  flexShrink: 0,
  width: 200,
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

export const ContactRequestToolbarSearchWrapper = styled('div')(() => ({
  width: '60%',
  minWidth: '200px',
}));

export const ContactRequestFiltersView = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));
