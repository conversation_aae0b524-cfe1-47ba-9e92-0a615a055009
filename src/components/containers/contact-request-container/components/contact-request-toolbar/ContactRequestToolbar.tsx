import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { useMemo } from 'react';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';
import { useAuth } from 'src/providers/AuthProvider';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  ContactRequestFields,
  ContactRequestToolbarLeft,
  ContactRequestFiltersView,
  ContactRequestToolbarRight,
  ContactRequestToolbarWrapper,
  ContactRequestToolbarFormControl,
  ContactRequestToolbarSearchWrapper,
} from './ContactRequestToolbar.style';

interface ContactRequestToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onAssignedAdminChange: (admin: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  selectedAssignedAdmin: ddValue;
  contactRequestStatusOptions: ddValue[];
  assignedAdminOptions: ddValue[];
  totalCount?: number;
}

const ContactRequestToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  onAssignedAdminChange,
  contactRequestStatusOptions,
  assignedAdminOptions,
  selectedStatus,
  selectedAssignedAdmin,
  onFilterChange,
  totalCount = 0,
}: ContactRequestToolbarProps) => {
  const { userName } = useAuth();

  const preparedAdminOptions = useMemo(
    () =>
      assignedAdminOptions
        .map((admin) => {
          if (admin.name === userName) {
            return { ...admin, name: 'Assigned to Me' };
          }
          return admin;
        })
        .sort((a, b) => {
          if (a.name === 'Assigned to Me') return -1;
          if (b.name === 'Assigned to Me') return 1;
          return 0;
        }),
    [assignedAdminOptions, userName]
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = contactRequestStatusOptions.find(
      ({ id }) => id.toString() === typeId.toString()
    );

    onStatusChange(status ?? ({} as ddValue));
    const filterOption: FilterOption = {
      field: 'statusId',
      value: status?.id.toString(),
    };

    onFilterChange(filterOption);
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    const filterOption: FilterOption = {
      field: 'statusId',
      value: '',
    };

    onFilterChange(filterOption, true);
  };

  const handleAssignedAdminChange = (event: TInputChangeEvent) => {
    const adminId = event.target.value;
    const admin = assignedAdminOptions.find(({ id }) => id.toString() === adminId.toString());

    onAssignedAdminChange(admin || ({} as ddValue));
    onFilterChange(
      {
        field: 'assignedAdminId',
        value: admin?.id.toString() || '',
      },
      !admin?.id
    );
  };

  const handleClearAssignedAdmin = () => {
    onAssignedAdminChange({} as ddValue);
    onFilterChange(
      {
        field: 'assignedAdminId',
        value: '',
      },
      true
    );
  };

  const canReset = searchKey || selectedStatus?.id || selectedAssignedAdmin?.id;

  return (
    <ContactRequestToolbarWrapper>
      <ContactRequestFields>
        <ContactRequestToolbarLeft>
          <ContactRequestToolbarSearchWrapper>
            <SearchBar
              type="text"
              value={searchKey}
              onChange={handleSearch}
              placeholder="Search..."
            />
          </ContactRequestToolbarSearchWrapper>
        </ContactRequestToolbarLeft>

        <ContactRequestToolbarRight>
          <ContactRequestToolbarFormControl>
            <CustomSelect
              value={selectedStatus?.id?.toString() ?? ''}
              handleChange={handleStatusChange}
              id="contact-request-status"
              label=""
              name="status"
              coreLabel="Status"
              options={contactRequestStatusOptions}
              disabled={contactRequestStatusOptions.length === 0}
              required={false}
            />
          </ContactRequestToolbarFormControl>
          <ContactRequestToolbarFormControl>
            <CustomSelect
              value={selectedAssignedAdmin?.id?.toString() ?? ''}
              handleChange={handleAssignedAdminChange}
              id="contact-request-assigned-admin"
              label=""
              name="assignedAdmin"
              coreLabel="Assigned to"
              options={preparedAdminOptions}
              disabled={preparedAdminOptions.length === 0}
              required={false}
            />
          </ContactRequestToolbarFormControl>
        </ContactRequestToolbarRight>
      </ContactRequestFields>

      {canReset && (
        <ContactRequestFiltersView>
          <FiltersResult totalResults={totalCount}>
            <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
              <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
            </FiltersBlock>

            <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
              <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
            </FiltersBlock>

            <FiltersBlock label="Assigned to:" isShow={Boolean(selectedAssignedAdmin?.id)}>
              <Chip
                {...chipProps}
                label={selectedAssignedAdmin?.name}
                onDelete={handleClearAssignedAdmin}
              />
            </FiltersBlock>
          </FiltersResult>
        </ContactRequestFiltersView>
      )}
    </ContactRequestToolbarWrapper>
  );
};

export default ContactRequestToolbar;
