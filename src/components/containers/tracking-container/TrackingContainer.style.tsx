import { styled } from '@mui/material/styles';
import { Button, IconButton } from '@mui/material';

export const TrackingWrapper = styled('div')(({ theme }) => ({
  marginInline: theme.spacing(4),
}));

export const TrackingButton = styled(Button)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2.5),
  paddingInline: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.primary.main,
  '& svg': {
    width: '18px',
    height: '18px',
    color: theme.palette.common.white,
  },
  ':hover': {
    backgroundColor: theme.palette.primary.dark,
  },
}));

export const TrackingOutlinedIconButton = styled(IconButton)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(1.5),
  paddingInline: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.grey[600]}`,
  '& svg': {
    width: '18px',
    height: '18px',
  },
}));
