import type { ddValue } from 'src/shared/types/ddValue';
import type { TrackingShipment } from 'src/shared/services/tracking/tracking.type';
import type { FilterOption, SearchCriteria } from 'src/shared/types/searchCriteria';

import { useLocation, useNavigate } from 'react-router-dom';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { type SelectChangeEvent } from '@mui/material';

import { useDebounce } from 'src/hooks/use-debounce';

import {
  searchTracking,
  getTrackingStatusOptions,
  getTrackingServiceCategoryOptions,
} from 'src/shared/services/tracking/tracking.service';

import TableView from 'src/components/common/table/view/TableView';
import NoDataView from 'src/components/common/error/no-data-view/NoDataView';
import { IconPlus, IconRefresh, IconDownload } from 'src/components/common/icons';
import TablePagination from 'src/components/common/table/pagination/TablePagination';
import MainContent from 'src/components/common/main-content-wrapper/MainContentWrapper';

import TrackingToolbar from './components/tracking-toolbar/TrackingToolbar';
import TrackingListItem from './components/tracking-list-item/TrackingListItem';
import {
  TrackingButton,
  TrackingWrapper,
  TrackingOutlinedIconButton,
} from './TrackingContainer.style';

const TABLE_HEAD = [
  { id: 'id', label: 'S.N', width: 80 },
  { id: 'customerName', label: 'Name', width: 200 },
  { id: 'customerEmailId', label: 'Email', width: 200 },
  { id: 'customerPhoneNumber', label: 'Phone', width: 150 },
  { id: 'serviceCategory', label: 'Service Type', width: 150 },
  { id: 'originPincode', label: 'Origin Pincode', width: 120 },
  { id: 'destinationPincode', label: 'Destination Pincode', width: 150 },
  { id: 'status', label: 'Status', width: 120 },
  { id: 'action', label: '', width: 80 },
];

const TrackingContainer = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const [tableData, setTableData] = useState<TrackingShipment[]>([]);

  const [loading, setLoading] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const debouncedSearchKey = useDebounce(searchKey, 500);
  const [filters, setFilters] = useState<FilterOption[]>([]);
  const [trackingStatusOptions, setTrackingStatusOptions] = useState<ddValue[]>([]);
  const [trackingServiceCategoryOptions, setTrackingServiceCategoryOptions] = useState<ddValue[]>(
    []
  );

  const [selectedStatus, setSelectedStatus] = useState<ddValue>({} as ddValue);
  const [selectedServiceCategory, setSelectedServiceCategory] = useState<ddValue>({} as ddValue);

  const getTableData = useCallback(async () => {
    try {
      setLoading(true);
      const criteria: SearchCriteria = {
        searchKey: debouncedSearchKey,
        filters,
        sortOptions: [],
        pagination: {
          pageNumber: currentPage,
          pageSize: rowsPerPage,
        },
      };
      const { count, searchResult } = await searchTracking(criteria);
      setTableData(searchResult);
      setTotalCount(count);
    } catch (error: any) {
      setFetchError(error.message || 'Failed to fetch tracking data');
    } finally {
      setLoading(false);
    }
  }, [currentPage, rowsPerPage, debouncedSearchKey, filters]);

  useEffect(() => {
    getTableData();
  }, [getTableData]);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        setLoading(true);
        const [statusOptions, serviceCategoryOptions] = await Promise.all([
          getTrackingStatusOptions(),
          getTrackingServiceCategoryOptions(),
        ]);
        setTrackingStatusOptions(statusOptions);
        setTrackingServiceCategoryOptions(serviceCategoryOptions);
      } catch (error: any) {
        setFetchError(error.message || 'Failed to fetch options');
      } finally {
        setLoading(false);
      }
    };

    loadOptions();
  }, []);

  const handleRowsPerPageChange = useCallback((event: SelectChangeEvent<string>) => {
    setRowsPerPage(+event.target.value);
  }, []);

  const handleStatusUpdate = useCallback((status: ddValue): void => {
    setSelectedStatus(status);
  }, []);

  const handleServiceCategoryUpdate = useCallback((serviceCategory: ddValue): void => {
    setSelectedServiceCategory(serviceCategory);
  }, []);

  const handleSearchKey = useCallback((key: string): void => {
    setSearchKey(key);
  }, []);

  const handleFilterChange = useCallback((filter: FilterOption, shouldRemove: boolean = false) => {
    setFilters((prevFilters) =>
      shouldRemove
        ? prevFilters.filter((item) => item.field !== filter.field)
        : [...prevFilters.filter((item) => item.field !== filter.field), filter]
    );
  }, []);

  const handleRefreshData = useCallback(async () => {
    await getTableData();
  }, [getTableData]);

  const handleEditRow = useCallback(
    (data: TrackingShipment) => {
      navigate(`${pathname}/${data.id}`);
    },
    [navigate, pathname]
  );

  const handleOpenDetails = useCallback(
    (trackingId: number) => {
      navigate(`${pathname}/${trackingId}`);
    },
    [navigate, pathname]
  );

  const handleAddNewShipmentClick = useCallback(() => {
    navigate(`${pathname}/new`);
  }, [navigate, pathname]);

  const memoizedTableData = useMemo(
    () => tableData.slice(0, rowsPerPage),
    [tableData, rowsPerPage]
  );

  return (
    <MainContent
      pageTitle="Tracking"
      buttons={
        <>
          <TrackingOutlinedIconButton size="large" onClick={handleRefreshData}>
            <IconRefresh />
          </TrackingOutlinedIconButton>
          <TrackingOutlinedIconButton size="large" color="primary">
            <IconDownload />
          </TrackingOutlinedIconButton>
          <TrackingButton
            variant="contained"
            color="primary"
            startIcon={<IconPlus />}
            onClick={handleAddNewShipmentClick}
          >
            Add Shipment
          </TrackingButton>
        </>
      }
    >
      <TrackingWrapper>
        <TrackingToolbar
          onSearchKey={handleSearchKey}
          searchKey={searchKey}
          trackingStatusOptions={trackingStatusOptions}
          trackingServiceCategoryOptions={trackingServiceCategoryOptions}
          selectedStatus={selectedStatus}
          selectedServiceCategory={selectedServiceCategory}
          onStatusChange={handleStatusUpdate}
          onServiceCategoryChange={handleServiceCategoryUpdate}
          onFilterChange={handleFilterChange}
        />

        <TableView
          pagination={
            <TablePagination
              count={Math.ceil(totalCount / rowsPerPage)}
              currentPage={currentPage}
              onNext={() => setCurrentPage(currentPage + 1)}
              onPrev={() => setCurrentPage(currentPage - 1)}
              value={rowsPerPage.toString()}
              handleChange={handleRowsPerPageChange}
              totalCount={totalCount}
            />
          }
          tableHead={TABLE_HEAD}
          error={fetchError}
          loading={loading}
          tableData={memoizedTableData ?? []}
        >
          {memoizedTableData.length > 0 ? (
            memoizedTableData.map((row) => (
              <TrackingListItem
                key={row.id}
                tracking={row}
                onRowClick={() => handleOpenDetails(row.id)}
                onEditRow={() => handleEditRow(row)}
              />
            ))
          ) : (
            <NoDataView />
          )}
        </TableView>
      </TrackingWrapper>
    </MainContent>
  );
};

export default TrackingContainer;
