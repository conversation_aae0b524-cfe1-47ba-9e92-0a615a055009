import type { ddValue } from 'src/shared/types/ddValue';
import type { FilterOption } from 'src/shared/types/searchCriteria';
import type { TInputChangeEvent } from 'src/components/common/forms/types/event.types';

import { Chip } from '@mui/material';

import { chipProps } from 'src/shared/toolbar';

import CustomSelect from 'src/components/common/forms/select/Select';
import SearchBar from 'src/components/common/forms/search-bar/SearchBar';
import FiltersBlock from 'src/components/common/filters/filter-blocks/FilterBlocks';
import { FiltersResult } from 'src/components/common/filters/filter-results/FIlterResults';

import {
  TrackingToolbarLeft,
  TrackingFiltersView,
  TrackingToolbarRight,
  TrackingToolbarWrapper,
  TrackingToolbarFormControl,
  TrackingToolbarSearchWrapper,
} from './TrackingToolbar.style';

interface TrackingToolbarProps {
  onSearchKey: (key: string) => void;
  searchKey: string;
  onStatusChange: (status: ddValue) => void;
  onServiceCategoryChange: (serviceCategory: ddValue) => void;
  onFilterChange: (filter: FilterOption, shouldRemove?: boolean) => void;
  selectedStatus: ddValue;
  selectedServiceCategory: ddValue;
  trackingStatusOptions: ddValue[];
  trackingServiceCategoryOptions: ddValue[];
}

const TrackingToolbar = ({
  onSearchKey,
  searchKey,
  onStatusChange,
  onServiceCategoryChange,
  selectedStatus,
  selectedServiceCategory,
  trackingStatusOptions,
  trackingServiceCategoryOptions,
  onFilterChange,
}: TrackingToolbarProps) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchKey(e.target.value);
  };

  const handleStatusChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const status = trackingStatusOptions.find(({ id }) => id.toString() === typeId.toString());

    onStatusChange(status || ({} as ddValue));
    onFilterChange(
      {
        field: 'status',
        value: status?.id.toString() || '',
      },
      !status?.id
    );
  };

  const handleServiceCategoryChange = (event: TInputChangeEvent) => {
    const typeId = event.target.value;
    const serviceCategory = trackingServiceCategoryOptions.find(
      ({ id }) => id.toString() === typeId.toString()
    );

    onServiceCategoryChange(serviceCategory || ({} as ddValue));
    onFilterChange(
      {
        field: 'serviceCategory',
        value: serviceCategory?.id.toString() || '',
      },
      !serviceCategory?.id
    );
  };

  const handleClearSearch = () => {
    onSearchKey('');
  };

  const handleClearStatus = () => {
    onStatusChange({} as ddValue);
    onFilterChange(
      {
        field: 'status',
        value: '',
      },
      true
    );
  };

  const handleClearServiceCategory = () => {
    onServiceCategoryChange({} as ddValue);
    onFilterChange(
      {
        field: 'serviceCategory',
        value: '',
      },
      true
    );
  };

  const canReset = searchKey || selectedStatus?.id || selectedServiceCategory?.id;

  return (
    <TrackingToolbarWrapper>
      <TrackingToolbarLeft>
        {canReset && (
          <TrackingFiltersView>
            <FiltersResult totalResults={0}>
              <FiltersBlock label="Keyword: " isShow={Boolean(searchKey)}>
                <Chip {...chipProps} label={searchKey} onDelete={handleClearSearch} />
              </FiltersBlock>

              <FiltersBlock label="Status:" isShow={Boolean(selectedStatus?.id)}>
                <Chip {...chipProps} label={selectedStatus?.name} onDelete={handleClearStatus} />
              </FiltersBlock>

              <FiltersBlock label="Service Category:" isShow={Boolean(selectedServiceCategory?.id)}>
                <Chip
                  {...chipProps}
                  label={selectedServiceCategory?.name}
                  onDelete={handleClearServiceCategory}
                />
              </FiltersBlock>
            </FiltersResult>
          </TrackingFiltersView>
        )}
      </TrackingToolbarLeft>

      <TrackingToolbarRight>
        <TrackingToolbarSearchWrapper>
          <SearchBar
            type="text"
            value={searchKey}
            onChange={handleSearch}
            placeholder="Search tracking data..."
          />
        </TrackingToolbarSearchWrapper>
        <TrackingToolbarFormControl>
          <CustomSelect
            value={selectedStatus?.id?.toString() ?? ''}
            handleChange={handleStatusChange}
            id="tracking-status"
            label=""
            name="status"
            coreLabel="Status"
            options={trackingStatusOptions}
            disabled={false}
            required={false}
          />
        </TrackingToolbarFormControl>
        <TrackingToolbarFormControl>
          <CustomSelect
            value={selectedServiceCategory?.id?.toString() ?? ''}
            handleChange={handleServiceCategoryChange}
            id="tracking-service-category"
            label=""
            name="serviceCategory"
            coreLabel="Service Category"
            options={trackingServiceCategoryOptions}
            disabled={false}
            required={false}
          />
        </TrackingToolbarFormControl>
      </TrackingToolbarRight>
    </TrackingToolbarWrapper>
  );
};

export default TrackingToolbar;
