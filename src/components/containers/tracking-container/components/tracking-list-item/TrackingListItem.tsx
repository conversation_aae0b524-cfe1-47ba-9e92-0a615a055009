import type { TrackingShipment } from 'src/shared/services/tracking/tracking.type';

import { Tooltip, TableRow, TableCell, IconButton } from '@mui/material';

import { IconShare } from 'src/components/common/icons';

import { StatusChip, UserNameLinkWrapper } from './TrackingListItem.style';

interface TrackingListItemProps {
  tracking: TrackingShipment;
  onRowClick: () => void;
  onEditRow: () => void;
}

const getStatusColor = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'delivered':
      return 'success';
    case 'in transit':
      return 'primary';
    case 'out for delivery':
      return 'warning';
    case 'pending':
      return 'default';
    case 'processing':
      return 'info';
    case 'picked up':
      return 'secondary';
    default:
      return 'default';
  }
};

const TrackingListItem = ({ tracking, onRowClick, onEditRow }: TrackingListItemProps) => (
  <TableRow hover onClick={onRowClick}>
    <TableCell align="center">{tracking.id}</TableCell>
    <TableCell
      onClick={(e) => {
        e.stopPropagation();
        onEditRow();
      }}
    >
      <UserNameLinkWrapper color="inherit">{tracking.customerName}</UserNameLinkWrapper>
    </TableCell>
    <TableCell>{tracking.customerEmailId}</TableCell>
    <TableCell>{tracking.customerPhoneNumber}</TableCell>
    <TableCell>{tracking.serviceCategory?.[0]?.name || 'N/A'}</TableCell>
    <TableCell>{tracking.originPincode}</TableCell>
    <TableCell>{tracking.destinationPincode}</TableCell>
    <TableCell>
      <StatusChip
        variant="soft"
        label={tracking.status?.[0]?.name || 'N/A'}
        size="small"
        color={getStatusColor(tracking.status?.[0]?.name)}
      />
    </TableCell>
    <TableCell>
      <Tooltip title="Edit">
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onEditRow();
          }}
        >
          <IconShare width={20} />
        </IconButton>
      </Tooltip>
    </TableCell>
  </TableRow>
);

export default TrackingListItem;
