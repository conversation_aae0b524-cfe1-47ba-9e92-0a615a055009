import type { Vehicle } from 'src/shared/services/instant-quote/instant-quote.type';

import { useState } from 'react';

import { Box, Typography } from '@mui/material';

import { IconChevronLeft, IconChevronRight } from '../icons';
import {
  StyledImage,
  StyledInfoGrid,
  StyledInfoItem,
  StyledDotButton,
  StyledDotWrapper,
  StyledArrowButton,
  StyledImageWrapper,
  StyledDetailsContainer,
  StyledCarouselContainer,
} from './VehicleDetails.style';

export interface VehicleDetailsProps {
  vehicle: Vehicle;
}

export interface SimpleImageCarouselProps {
  images: string[];
  vehicleName: string;
}

const SimpleImageCarousel: React.FC<SimpleImageCarouselProps> = ({
  images,
  vehicleName,
}: SimpleImageCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const fallbackImage = '/images/cargo.webp';

  if (!images || images.length === 0) {
    return (
      <StyledImageWrapper>
        <StyledImage src={fallbackImage} alt={`${vehicleName} - Default Image`} />
      </StyledImageWrapper>
    );
  }

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? images.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === images.length - 1 ? 0 : currentIndex + 1);
  };

  return (
    <StyledCarouselContainer>
      <StyledImageWrapper>
        <StyledImage
          src={images[currentIndex]}
          alt={`${vehicleName} - Image ${currentIndex + 1}`}
        />
      </StyledImageWrapper>
      {images.length > 1 && (
        <>
          <StyledArrowButton left onClick={goToPrevious} aria-label="Previous">
            <IconChevronLeft style={{ width: 18, height: 18 }} />
          </StyledArrowButton>
          <StyledArrowButton right onClick={goToNext} aria-label="Next">
            <IconChevronRight style={{ width: 18, height: 18 }} />
          </StyledArrowButton>
          <StyledDotWrapper>
            {images?.map((_, index) => (
              <StyledDotButton
                key={index}
                active={index === currentIndex}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </StyledDotWrapper>
        </>
      )}
    </StyledCarouselContainer>
  );
};

const VehicleDetails: React.FC<VehicleDetailsProps> = ({ vehicle }: VehicleDetailsProps) => {
  if (!vehicle) return null;

  return (
    <StyledDetailsContainer>
      <Box display="flex" flexDirection={{ xs: 'column', md: 'row' }} gap={4}>
        <Box flex={1}>
          <SimpleImageCarousel images={vehicle?.imageUrls} vehicleName={vehicle?.name} />
        </Box>
        <Box
          flex={1}
          display="flex"
          flexDirection="column"
          justifyContent="flex-start"
          alignItems="flex-start"
        >
          <Typography variant="h6" fontWeight={600} mb={2}>
            {vehicle?.name}
          </Typography>
          <StyledInfoGrid>
            <StyledInfoItem>
              <Typography variant="body2" color="text.secondary">
                Dimensions <span style={{ fontSize: 12, color: '#9ca3af' }}>(L x W x H)</span>
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                ({vehicle?.length} ft x {vehicle?.width} ft x {vehicle?.height} ft)
              </Typography>
            </StyledInfoItem>
            <StyledInfoItem>
              <Typography variant="body2" color="text.secondary">
                Capacity
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {vehicle?.cft} CFT
              </Typography>
            </StyledInfoItem>
          </StyledInfoGrid>
        </Box>
      </Box>
    </StyledDetailsContainer>
  );
};

export default VehicleDetails;
