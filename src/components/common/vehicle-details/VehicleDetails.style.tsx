import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';

export const StyledCarouselContainer = styled(Box)({
  position: 'relative',
  width: '100%',
  height: 144,
});

export const StyledImageWrapper = styled(Box)({
  width: '100%',
  height: '100%',
  background: '#f3f4f6',
  borderRadius: 8,
  overflow: 'hidden',
  maxHeight: 144,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});

export const StyledImage = styled('img')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
});

interface ArrowButtonProps {
  left?: boolean;
  right?: boolean;
}

export const StyledArrowButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'left' && prop !== 'right',
})<ArrowButtonProps>(({ left, right }) => ({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  left: left ? 8 : 'auto',
  right: right ? 8 : 'auto',
  background: 'rgba(255,255,255,0.8)',
  color: '#1f2937',
  boxShadow: '0 2px 6px rgba(0,0,0,0.08)',
  '&:hover': { background: '#fff' },
}));

interface DotButtonProps {
  active?: boolean;
}

export const StyledDotWrapper = styled('div')(() => ({
  position: 'absolute',
  bottom: 8,
  left: 0,
  right: 0,
  display: 'flex',
  justifyContent: 'center',
  gap: 1,
}));

export const StyledDotButton = styled('button', {
  shouldForwardProp: (prop) => prop !== 'active',
})<DotButtonProps>(({ active, theme }) => ({
  width: 8,
  height: 8,
  borderRadius: '50%',
  margin: '0 2px',
  border: 'none',
  background: active ? theme.palette.primary.main : '#d1d5db',
  cursor: 'pointer',
  transition: 'background 0.2s',
}));

export const StyledDetailsContainer = styled(Box)({
  background: '#fff',
  borderRadius: 8,
  marginBottom: 16,
});

export const StyledInfoGrid = styled(Box)({
  display: 'grid',
  gridTemplateColumns: '1fr',
  gap: 8,
  width: '100%',
});

export const StyledInfoItem = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
});
