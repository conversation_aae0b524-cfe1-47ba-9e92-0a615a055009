import { Box, Card, Table, styled, TableBody } from '@mui/material';

export const TableCardWrapper = styled(Card)(({ theme }) => ({
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  boxShadow: 'none',
  border: `solid 2px ${theme.palette.grey[300]}`,
}));

export const StyledTableWrapper = styled(Box)(() => ({
  position: 'relative',
  width: '100%',
  height: 'calc(100vh - 15.5rem)',
}));

export const TableRoot = styled(Table)(() => ({
  width: '100%',
}));

export const TableBodyWrapper = styled(TableBody)(() => ({
  width: '100%',
  position: 'relative',
}));

export const TableRowWrapper = styled('div')(() => ({
  minWidth: 960,
}));

export const TablePaginationWrapper = styled('div')(() => ({}));

export const TableLoaderWrapper = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: 'calc(100vh - 50vh)',
  marginInline: 'auto',
  '& > td': {
    height: '100%',
    width: '100%',
    textAlign: 'center',
    verticalAlign: 'middle',
  },
}));
