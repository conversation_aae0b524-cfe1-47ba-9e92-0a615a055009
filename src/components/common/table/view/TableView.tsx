import { memo, useMemo, useCallback } from 'react';

import { Typography } from '@mui/material';

import TableSelectedAction from 'src/components/common/table/selected-action';
import ProgressLoader from 'src/components/common/loader/LinearProgressLoader';
import { ConditionalRenderer } from 'src/components/common/ConditionalRenderer';

import SpanFullTable from '../FullWidthTableRow';
import TableHeadCustom from '../head/TableHeadCustom';
import ScrollbarArea from '../../scrollbar/ScrollbarArea';
import NoDataView from '../../error/no-data-view/NoDataView';
import {
  TableRoot,
  TableBodyWrapper,
  TableCardWrapper,
  StyledTableWrapper,
  TableLoaderWrapper,
  TablePaginationWrapper,
} from './TableView.style';

interface TableViewProps<T extends Record<string, any>> {
  loading: boolean;
  tableHead: T[];
  tableData: T[];
  error: string | null;
  children: React.ReactNode;
  pagination: React.ReactNode;
  showCheckbox?: boolean;
  selected?: string[];
  setSelected?: (inputValue: string[]) => void;
  action?: React.ReactNode;
}

const TableView = <T extends Record<string, any>>({
  children,
  tableHead,
  tableData,
  loading,
  error,
  pagination,
  showCheckbox = false,
  selected = [],
  setSelected,
  action,
}: TableViewProps<T>) => {
  const mappedTableDataIds = useMemo(
    () => tableData?.map((row) => row?.id?.toString()),
    [tableData]
  );

  const onSelectAllRows = useCallback(
    (checked: boolean) => {
      if (setSelected) {
        setSelected(checked ? mappedTableDataIds : []);
      }
    },
    [setSelected, mappedTableDataIds]
  );

  const memoizedTableHead = useMemo(() => tableHead, [tableHead]);

  const fallbacks = useMemo(
    () => ({
      loading: (
        <SpanFullTable tableHeadLength={tableHead?.length}>
          <TableLoaderWrapper>
            <ProgressLoader variant="circular" size={40} />
          </TableLoaderWrapper>
        </SpanFullTable>
      ),
      empty: (
        <SpanFullTable tableHeadLength={tableHead?.length}>
          <NoDataView />
        </SpanFullTable>
      ),
      error: (
        <SpanFullTable tableHeadLength={tableHead.length}>
          <Typography color="error" textAlign="center">
            {error}
          </Typography>
        </SpanFullTable>
      ),
    }),
    [tableHead?.length, error]
  );

  const memoizedSelectedAction = useMemo(
    () => (
      <TableSelectedAction
        dense
        numSelected={selected?.length}
        rowCount={tableData?.length}
        onSelectAllRows={onSelectAllRows}
        action={action}
      />
    ),
    [selected?.length, tableData?.length, onSelectAllRows, action]
  );

  const memoizedTableHeader = useMemo(
    () => (
      <TableHeadCustom
        headLabel={memoizedTableHead}
        showCheckbox={showCheckbox}
        numSelected={selected?.length}
        rowCount={tableData?.length}
        onSelectAllRows={onSelectAllRows}
      />
    ),
    [memoizedTableHead, showCheckbox, selected?.length, tableData?.length, onSelectAllRows]
  );

  return (
    <TableCardWrapper>
      <StyledTableWrapper>
        {memoizedSelectedAction}
        <ScrollbarArea>
          <TableRoot>
            {memoizedTableHeader}
            <TableBodyWrapper>
              <ConditionalRenderer
                isLoading={loading}
                loadingFallback={fallbacks.loading}
                isDataEmpty={tableData?.length === 0}
                emptyDataFallback={fallbacks.empty}
                hasError={Boolean(error)}
                errorFallback={fallbacks.error}
                data={children}
              />
            </TableBodyWrapper>
          </TableRoot>
        </ScrollbarArea>
      </StyledTableWrapper>
      {pagination && <TablePaginationWrapper>{pagination}</TablePaginationWrapper>}
    </TableCardWrapper>
  );
};

export default memo(
  TableView,
  (prevProps, nextProps) =>
    prevProps.loading === nextProps.loading &&
    prevProps.error === nextProps.error &&
    prevProps.tableData === nextProps.tableData &&
    prevProps.selected === nextProps.selected &&
    prevProps.children === nextProps.children &&
    prevProps.pagination === nextProps.pagination
);
