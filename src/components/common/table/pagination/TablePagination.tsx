import type { SelectChangeEvent } from '@mui/material';

import React from 'react';

import { Button, Select, MenuItem, OutlinedInput } from '@mui/material';

import {
  StyledInputLabel,
  TablePaginationView,
  TablePaginationArrows,
  TablePaginationWrapper,
  TablePaginationLeftView,
  TablePaginationRowsView,
} from './TablePagination.style';

interface TablePaginationProps {
  count: number;
  currentPage: number;
  onNext: () => void;
  onPrev: () => void;
  value?: string;
  handleChange?: (event: SelectChangeEvent<string>, child: React.ReactNode) => void;
  showRowsPerPage?: boolean;
  options?: number[];
  totalCount: number;
}

const TablePagination = ({
  count,
  currentPage,
  value,
  handleChange,
  onNext,
  onPrev,
  showRowsPerPage = false,
  options = [3, 10, 25, 50],
  totalCount,
}: TablePaginationProps) => (
  <TablePaginationWrapper>
    <TablePaginationLeftView>
      <TablePaginationArrows>
        <Button variant="outlined" disabled={currentPage === 1} onClick={onPrev} size="small">
          Prev
        </Button>
        <Button variant="outlined" disabled={currentPage === count} onClick={onNext} size="small">
          Next
        </Button>
      </TablePaginationArrows>
    </TablePaginationLeftView>

    {showRowsPerPage && handleChange && (
      <TablePaginationRowsView>
        <StyledInputLabel htmlFor="rows-per-page-select-label">Rows per page</StyledInputLabel>
        <Select
          value={value?.toString()}
          onChange={handleChange}
          size="small"
          input={<OutlinedInput />}
          inputProps={{ id: 'rows-per-page-select-label' }}
        >
          {options.map((option) => (
            <MenuItem key={option} value={option.toString()}>
              {option}
            </MenuItem>
          ))}
        </Select>
      </TablePaginationRowsView>
    )}

    <TablePaginationView>
      Page <strong>{currentPage}</strong> of {count} ({totalCount} Records)
    </TablePaginationView>
  </TablePaginationWrapper>
);

export default TablePagination;
