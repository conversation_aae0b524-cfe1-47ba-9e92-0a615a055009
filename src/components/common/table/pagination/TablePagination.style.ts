import { styled } from '@mui/material/styles';
import { Select, InputLabel } from '@mui/material';

export const TablePaginationWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  padding: theme.spacing(2),
  justifyContent: 'space-between',
  alignItems: 'center',
  borderTop: `1px solid ${theme.palette.grey[300]}`,
}));

export const TablePaginationLeftView = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(3),
}));

export const TablePaginationRowsView = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

export const TablePaginationArrows = styled('div')(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(1),
  '& .MuiButton-root': {
    minWidth: 80,
    height: 32,
    textTransform: 'none',
    fontWeight: 500,
  },
}));

export const TablePaginationView = styled('div')(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  whiteSpace: 'nowrap',
}));

export const StyledInputLabel = styled(InputLabel)(({ theme }) => ({
  fontSize: '0.875rem',
  marginRight: theme.spacing(1),
}));

export const StyledSelect = styled(Select)(({ theme }) => ({
  minWidth: 80,
  height: 32,
  '& .MuiPaper-root': {
    maxHeight: 240,
    marginTop: theme.spacing(1),
  },
}));
