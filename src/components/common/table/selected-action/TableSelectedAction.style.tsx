import { styled } from '@mui/material/styles';
import { Stack, Typography } from '@mui/material';

export const SelectedActionWrapper = styled(Stack)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  zIndex: 9,
  height: 58,
  backgroundColor: theme.palette.primary.lighter,
  padding: theme.spacing(0, 2, 0, 1),
}));

export const SelectedCount = styled(Typography)(({ theme }) => ({
  marginLeft: theme.spacing(2),
  flexGrow: 1,
  color: theme.palette.primary.main,
}));

export const DenseWrapper = styled(SelectedActionWrapper)(({ theme }) => ({
  height: 38,
  '& .MuiTypography-root': {
    marginLeft: theme.spacing(3),
  },
}));
