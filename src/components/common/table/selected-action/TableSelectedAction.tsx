import { Checkbox } from '@mui/material';

import { <PERSON><PERSON><PERSON>rap<PERSON>, SelectedCount, SelectedActionWrapper } from './TableSelectedAction.style';

import type { TableSelectedActionProps } from './types';

function TableSelectedAction({
  dense,
  action,
  rowCount,
  numSelected,
  onSelectAllRows,
  ...other
}: TableSelectedActionProps) {
  if (!numSelected) {
    return null;
  }

  const Wrapper = dense ? DenseWrapper : SelectedActionWrapper;

  return (
    <Wrapper direction="row" alignItems="center" {...other}>
      <Checkbox
        indeterminate={!!numSelected && numSelected < rowCount}
        checked={!!rowCount && numSelected === rowCount}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
          onSelectAllRows(event.target.checked)
        }
      />

      <SelectedCount variant="subtitle2">{numSelected} selected</SelectedCount>

      {action && action}
    </Wrapper>
  );
}

export default TableSelectedAction;
