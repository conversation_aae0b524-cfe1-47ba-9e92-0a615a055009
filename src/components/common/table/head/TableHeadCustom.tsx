import { Checkbox, TableRow, TableCell, TableSortLabel } from '@mui/material';

import { TableHeadLabel, TableHeadWrapper } from './TableHeadCustom.style';

interface TableHeadProps {
  headLabel: Record<string, any>[];
  onSort?: (id: string) => void;
  onSelectAllRows?: (checked: boolean) => void;
  numSelected?: number;
  rowCount?: number;
  showCheckbox: boolean;
}

const TableHeadCustom = ({
  onSelectAllRows,
  headLabel,
  numSelected = 0,
  rowCount = 0,
  showCheckbox,
  onSort,
}: TableHeadProps) => (
  <TableHeadWrapper>
    <TableRow>
      {showCheckbox && onSelectAllRows && (
        <TableCell padding="checkbox">
          <Checkbox
            indeterminate={!!numSelected && numSelected < (rowCount as number)}
            checked={!!rowCount && numSelected === rowCount}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              onSelectAllRows(event.target.checked)
            }
            inputProps={{
              name: 'select-all-rows',
              'aria-label': 'select all rows',
            }}
          />
        </TableCell>
      )}

      {headLabel.map((headCell: Record<string, any>) => (
        <TableCell
          key={headCell.id}
          align={headCell.align || 'left'}
          sx={{ width: headCell.width, minWidth: headCell.minWidth }}
        >
          {onSort ? (
            <TableSortLabel hideSortIcon onClick={() => onSort(headCell.id)}>
              <TableHeadLabel>{headCell.label}</TableHeadLabel>
            </TableSortLabel>
          ) : (
            <TableHeadLabel>{headCell.label}</TableHeadLabel>
          )}
        </TableCell>
      ))}
    </TableRow>
  </TableHeadWrapper>
);

export default TableHeadCustom;
