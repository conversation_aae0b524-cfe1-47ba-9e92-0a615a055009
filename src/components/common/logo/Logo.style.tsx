import { styled } from '@mui/material/styles';

export const LogoWrapper = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

export const LogoImageWrapper = styled('div')(() => ({
  width: '100%',
  height: '40px',
}));

export const LogoImage = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
}));

export const LogoCompanyName = styled('span')(({ theme }) => ({
  fontSize: theme.typography.body2.fontSize,
  color: theme.palette.grey[500],
}));
