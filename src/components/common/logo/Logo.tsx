import { Link } from 'react-router-dom';

import { paths } from 'src/routes/paths';

import { LogoImage, LogoWrapper, LogoCompanyName, LogoImageWrapper } from './Logo.style';

export interface LogoProps {
  showLabel?: boolean;
}

const Logo = ({ showLabel }: LogoProps) => (
  <LogoWrapper>
    <Link to={paths.home}>
      <LogoImageWrapper>
        <LogoImage src="/assets/nexmove-logo.png" alt="********" />
      </LogoImageWrapper>
    </Link>
    {showLabel && <LogoCompanyName>Nex Mov</LogoCompanyName>}
  </LogoWrapper>
);
export default Logo;
