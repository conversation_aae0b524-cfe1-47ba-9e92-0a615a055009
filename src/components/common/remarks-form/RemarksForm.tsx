import type { ChangeEvent } from 'react';

import { Button, Dialog, DialogTitle, DialogActions, DialogContent } from '@mui/material';

import CustomTextField from 'src/components/common/forms/textfield/CustomTextField';

type RemarksFormProps = {
  open: boolean;
  onClose: () => void;
  remarks: string;
  onChange: (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onSubmit: () => void;
};

const RemarksForm = ({ open, onClose, remarks, onChange, onSubmit }: RemarksFormProps) => (
  <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
    <DialogTitle>Update Contact Request</DialogTitle>
    <DialogContent>
      <CustomTextField
        id="remarks"
        name="remarks"
        label="Remarks"
        type="text"
        variant="outlined"
        value={remarks ?? ''}
        handleChange={onChange}
        handleBlur={() => {}}
        disabled={false}
        multiline
        minRows={5}
        maxRows={5}
      />
    </DialogContent>
    <DialogActions>
      <Button variant="outlined" onClick={onClose} color="error">
        Cancel
      </Button>
      <Button
        type="submit"
        variant="contained"
        color="primary"
        onClick={(e) => {
          e.preventDefault();
          onSubmit();
        }}
      >
        Continue to Update
      </Button>
    </DialogActions>
  </Dialog>
);

export default RemarksForm;
