import type {
  AuditLogEntry,
  SearchLogCriteria,
} from 'src/shared/services/activity-logs/activity-logs.type';

import { useState, useEffect, useCallback } from 'react';

import { searchActivityLogs } from 'src/shared/services/activity-logs/activity-logs.service';

import IconX from 'src/components/common/icons/IconClose';
import Logs from 'src/components/common/log-list/LogList';
import Loader from 'src/components/common/loader/loader/Loader';
import { LogCount } from 'src/components/common/log-list/LogList.style';

import {
  DialogRoot,
  TitleWrapper,
  LogIconButton,
  DialogTitleWrapper,
  DialogContentWrapper,
} from './LogDialog.style';

interface LogDialogProps {
  open: boolean;
  onClose: () => void;
  id: number;
  tableName: string;
  columnName: string;
}

const LogDialog = ({ open, onClose, id, columnName, tableName }: LogDialogProps) => {
  const [logData, setLogData] = useState<AuditLogEntry[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const searchLogs = useCallback(async () => {
    setIsLoading(true);
    try {
      const searchCriteria: SearchLogCriteria = {
        tableName,
        columnName,
        primaryKey: String(id),
      };
      const response = await searchActivityLogs(searchCriteria);

      setLogData(response);
    } catch (error) {
      console.error('Error fetching logs:', error);
    } finally {
      setIsLoading(false);
    }
  }, [id, columnName, tableName]);

  useEffect(() => {
    if (open) {
      searchLogs();
    }
  }, [open, searchLogs]);

  return (
    <DialogRoot open={open} onClose={onClose} maxWidth="md" fullWidth>
      <Loader loading={isLoading} />
      <DialogTitleWrapper>
        <TitleWrapper>
          Activity History
          <LogCount>{logData?.length}</LogCount>
        </TitleWrapper>

        <LogIconButton onClick={onClose}>
          <IconX />
        </LogIconButton>
      </DialogTitleWrapper>
      <DialogContentWrapper>
        <Logs title="" data={logData} />
      </DialogContentWrapper>
    </DialogRoot>
  );
};

export default LogDialog;
