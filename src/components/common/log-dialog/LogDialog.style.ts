import { styled } from '@mui/material/styles';
import { Box, Dialog, IconButton, DialogTitle, DialogContent } from '@mui/material';

export const DialogRoot = styled(Dialog)(() => ({
  maxHeight: '100%',
}));

export const DialogContentWrapper = styled(DialogContent)(({ theme }) => ({
  padding: theme.spacing(2),
}));

export const DialogTitleWrapper = styled(DialogTitle)(({ theme }) => ({
  padding: theme.spacing(2),
  paddingBottom: theme.spacing(1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

export const TitleWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
}));

export const LogIconButton = styled(IconButton)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(1.5),
  paddingInline: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  '& svg': {
    width: '18px',
    height: '18px',
  },
}));
