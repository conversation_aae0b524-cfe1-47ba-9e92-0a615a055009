import { styled } from '@mui/material/styles';
import { Box, Avatar, Typography } from '@mui/material';

type DenseProps = {
  dense?: boolean;
};

export const ProfileWrapper = styled(Box)(({ theme }) => ({
  width: '50px',
  height: '50px',
  borderRadius: '50%',
  overflow: 'hidden',
  marginRight: theme.spacing(1),
}));

export const ProfileImageGraphics = styled(Avatar)(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
}));

export const ProfileUserName = styled(Typography, {
  shouldForwardProp: (props) => props !== 'dense',
})<DenseProps>(({ theme, dense }) => ({
  fontWeight: theme.typography.fontWeightBold,
  fontSize: !dense ? theme.typography.body1.fontSize : '12px',
  textAlign: 'left',
}));

export const ProfileUserRole = styled(Typography)(({ theme }) => ({
  fontSize: theme.typography.body2.fontSize,
  color: theme.palette.grey[500],
  lineHeight: '12px',
}));

export const ProfileComponentName = styled('div')(() => ({}));

export const UserDetails = styled('div')(({ theme }) => ({}));
