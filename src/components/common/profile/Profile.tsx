import { Stack } from '@mui/material';

import {
  UserDetails,
  ProfileWrapper,
  ProfileUserName,
  ProfileUserRole,
  ProfileImageGraphics,
} from './Profile.style';

export interface ProfileProps {
  name: string;
  adminRole: string;
  profileSrc: string;
  dense?: boolean;
}

const Profile = ({ name, profileSrc, adminRole, dense }: ProfileProps) => (
  <Stack direction="row" spacing={4} alignItems="center" justifyContent="center" mt={4}>
    <ProfileWrapper>
      <ProfileImageGraphics src={profileSrc} />
    </ProfileWrapper>
    <UserDetails>
      <ProfileUserName dense={dense}>{name}</ProfileUserName>
      {!dense && <ProfileUserRole>{adminRole}</ProfileUserRole>}
    </UserDetails>
  </Stack>
);

export default Profile;
