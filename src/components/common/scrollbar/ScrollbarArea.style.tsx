import * as ScrollArea from '@radix-ui/react-scroll-area';

import { styled } from '@mui/material/styles';

const SCROLLBAR_SIZE = 11;

export const ScrollAreaRoot = styled(ScrollArea.Root)(() => ({
  width: '100%',
  height: '100%',
  overflow: 'hidden',
}));

export const ScrollAreaViewport = styled(ScrollArea.Viewport)(() => ({
  width: '100%',
  height: '100%',
  borderRadius: 'inherit',
}));

export const ScrollAreaScrollbar = styled(ScrollArea.Scrollbar)(() => ({
  display: 'flex',
  userSelect: 'none',
  touchAction: 'none',
  padding: 2,
  transition: 'background 160ms ease-out',
  '&[data-orientation="vertical"]': { width: SCROLLBAR_SIZE },
  '&[data-orientation="horizontal"]': {
    flexDirection: 'column',
    height: SCROLLBAR_SIZE,
  },
}));

export const ScrollAreaThumb = styled(ScrollArea.Thumb)(() => ({
  flex: 1,
  background: '#cbd1d7cc',
  borderRadius: SCROLLBAR_SIZE,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    zIndex: 999,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '100%',
    height: '100%',
    minWidth: 44,
    minHeight: 44,
  },
}));
