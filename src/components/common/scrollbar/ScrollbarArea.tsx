import type { ReactNode, ComponentProps } from 'react';
import type * as ScrollArea from '@radix-ui/react-scroll-area';

import {
  ScrollAreaRoot,
  ScrollAreaThumb,
  ScrollAreaViewport,
  ScrollAreaScrollbar,
} from './ScrollbarArea.style';

type Props = {
  children?: ReactNode;
} & ComponentProps<typeof ScrollArea.Root>;

const ScrollbarArea = ({ children }: Props) => (
  <ScrollAreaRoot>
    <ScrollAreaViewport>{children}</ScrollAreaViewport>
    <ScrollAreaScrollbar orientation="vertical">
      <ScrollAreaThumb />
    </ScrollAreaScrollbar>
    <ScrollAreaScrollbar orientation="horizontal">
      <ScrollAreaThumb />
    </ScrollAreaScrollbar>
  </ScrollAreaRoot>
);

export default ScrollbarArea;
