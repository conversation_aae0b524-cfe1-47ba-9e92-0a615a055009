import { styled } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';

export const NoDataViewWrapper = styled(Box)(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

export const NoDataViewBox = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.grey[300]}`,
}));

export const NoDataViewContent = styled(Typography)(({ theme }) => ({
  color: theme.palette.grey[500],
}));
