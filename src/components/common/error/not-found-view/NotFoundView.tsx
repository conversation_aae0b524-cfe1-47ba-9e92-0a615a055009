import { Button, Typography } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { Title, Illustration, NotFoundContainer, IllustrationWrapper } from './NotFoundView.style';

export interface NotFoundViewProps {
  title?: string;
  description?: string;
}

const notFoundDefaultProps: NotFoundViewProps = {
  title: 'Page Not Found',
  description: 'The page you are looking for does not exist or has been moved.',
};

const NotFoundView = ({
  title = notFoundDefaultProps.title,
  description = notFoundDefaultProps.description,
}: NotFoundViewProps) => {
  const { back } = useRouter();

  const handleBack = () => {
    back();
  };

  return (
    <NotFoundContainer>
      <Title variant="h3" gutterBottom>
        {title}
      </Title>

      <Typography sx={{ color: 'text.secondary' }}>{description}</Typography>

      <IllustrationWrapper>
        <Illustration src="/assets/not-found.svg" />
      </IllustrationWrapper>

      <Button size="large" variant="contained" onClick={handleBack}>
        Go Back
      </Button>
    </NotFoundContainer>
  );
};

export default NotFoundView;
