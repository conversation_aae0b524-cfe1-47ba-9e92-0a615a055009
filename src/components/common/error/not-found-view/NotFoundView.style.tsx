import { styled } from '@mui/material/styles';
import { Container, Typography } from '@mui/material';

export const NotFoundContainer = styled(Container)(() => ({
  height: '80vh',
  display: 'grid',
  placeContent: 'center',
}));

export const Title = styled(Typography)(() => ({
  textAlign: 'center',
}));

export const IllustrationWrapper = styled('div')(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

export const Illustration = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
  marginInline: 'auto',
  paddingInline: '1rem',
  paddingTop: '1rem',
}));
