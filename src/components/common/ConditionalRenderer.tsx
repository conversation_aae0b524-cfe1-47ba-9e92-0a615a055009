interface RenderDynamicContentProps {
  isLoading?: boolean;
  loadingFallback?: React.ReactNode;
  hasError?: boolean;
  errorFallback?: React.ReactNode;
  isDataEmpty?: boolean;
  emptyDataFallback?: React.ReactNode;
  data?: React.ReactNode;
}

export const ConditionalRenderer: React.FC<RenderDynamicContentProps> = ({
  isLoading,
  loadingFallback,
  hasError,
  errorFallback,
  isDataEmpty,
  emptyDataFallback,
  data,
}) => {
  const message = 'provide fallback ui';

  if (isLoading) return loadingFallback || message;
  if (hasError) return errorFallback || message;
  if (isDataEmpty) return emptyDataFallback || message;
  return data || message;
};
