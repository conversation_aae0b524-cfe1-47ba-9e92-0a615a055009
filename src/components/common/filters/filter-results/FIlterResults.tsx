import type { ChipProps } from '@mui/material/Chip';

import {
  FiltersResultsText,
  FiltersResultsCount,
  FiltersResultsWrapper,
  FiltersResultsContent,
} from './FilterResults.style';

export const chipProps: ChipProps = {
  size: 'small',
  variant: 'soft',
};

type FiltersResultProps = {
  totalResults: number;
  children: React.ReactNode;
};

export function FiltersResult({ totalResults, children }: FiltersResultProps) {
  return (
    <FiltersResultsWrapper>
      <FiltersResultsContent>{children}</FiltersResultsContent>

      <FiltersResultsCount>
        <strong>{totalResults}</strong>
        <FiltersResultsText component="span">results found</FiltersResultsText>
      </FiltersResultsCount>
    </FiltersResultsWrapper>
  );
}
