import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

export const FiltersResultsWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
}));

export const FiltersResultsContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  gap: theme.spacing(1),
  display: 'flex',
  flexWrap: 'wrap',
  alignItems: 'center',
}));

export const FiltersResultsCount = styled(Box)(({ theme }) => ({
  marginLeft: theme.spacing(1.5),
  typography: 'body2',
}));

export const FiltersResultsText = styled(Box)(({ theme }) => ({
  color: 'text.secondary',
  marginLeft: theme.spacing(0.25),
}));
