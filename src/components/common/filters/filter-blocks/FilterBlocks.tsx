import {
  FiltersBlockLabel,
  FiltersBlockWrapper,
  FiltersBlockChildrenWrapper,
} from './FilterBlocks.style';

interface FilterBlockProps {
  label: string;
  isShow: boolean;
  children: React.ReactNode;
}

const FiltersBlock = ({ label, children, isShow }: FilterBlockProps) => {
  if (!isShow) {
    return null;
  }

  return (
    <FiltersBlockWrapper>
      <FiltersBlockLabel component="span">{label}</FiltersBlockLabel>
      <FiltersBlockChildrenWrapper>{children}</FiltersBlockChildrenWrapper>
    </FiltersBlockWrapper>
  );
};

export default FiltersBlock;
