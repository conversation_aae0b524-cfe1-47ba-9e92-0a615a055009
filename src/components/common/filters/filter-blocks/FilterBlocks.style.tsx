import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

export const FiltersBlockWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: 1,
  padding: theme.spacing(0.7),
  borderRadius: 5,
  overflow: 'hidden',
  border: `solid 1px ${theme.palette.divider}`,
}));

export const FiltersBlockLabel = styled(Box)(({ theme }) => ({
  height: 24,
  lineHeight: '24px',
  fontSize: theme.typography.h6.fontSize,
  fontWeight: theme.typography.subtitle2.fontWeight,
  paddingRight: '.5rem',
}));

export const FiltersBlockChildrenWrapper = styled(Box)(() => ({
  display: 'flex',
  gap: 1,
  flexWrap: 'wrap',
}));
