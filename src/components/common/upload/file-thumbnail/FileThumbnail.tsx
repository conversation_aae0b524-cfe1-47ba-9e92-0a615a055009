import Tooltip from '@mui/material/Tooltip';

import { fileData, fileThumb, fileFormat } from './utils';
import { RemoveButton, DownloadButton } from './action-button/ActionButton';
import { ThumbnailIcon, ThumbnailImage, ThumbnailContent } from './FileThumbnail.style';

import type { FileThumbnailProps } from './types';

export function FileThumbnail({
  sx,
  file,
  tooltip,
  onRemove,
  imageView,
  slotProps,
  onDownload,
  ...other
}: FileThumbnailProps) {
  const previewUrl = typeof file === 'string' ? file : URL.createObjectURL(file);
  const { name, path } = fileData(file);
  const format = fileFormat(path || previewUrl);

  const renderImg = (
    <ThumbnailImage
      src={previewUrl}
      sx={{
        ...slotProps?.img,
      }}
    />
  );

  const renderIcon = (
    <ThumbnailIcon
      src={fileThumb(format)}
      sx={{
        ...slotProps?.icon,
      }}
    />
  );

  const renderContent = (
    <ThumbnailContent
      component="span"
      sx={{
        ...sx,
      }}
      {...other}
    >
      {format === 'image' && imageView ? renderImg : renderIcon}

      {onRemove && <RemoveButton onClick={onRemove} sx={slotProps?.removeBtn} />}

      {onDownload && <DownloadButton onClick={onDownload} sx={slotProps?.downloadBtn} />}
    </ThumbnailContent>
  );

  if (tooltip) {
    return (
      <Tooltip
        arrow
        title={name}
        slotProps={{
          popper: {
            modifiers: [
              {
                name: 'offset',
                options: { offset: [0, -12] },
              },
            ],
          },
        }}
      >
        {renderContent}
      </Tooltip>
    );
  }

  return renderContent;
}
