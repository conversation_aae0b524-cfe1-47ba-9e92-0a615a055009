import type { ButtonBaseProps } from '@mui/material/ButtonBase';
import type { IconButtonProps } from '@mui/material/IconButton';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import { StyledButtonBase, StyledIconButton } from './ActionButton.style';

export function DownloadButton({ sx, ...other }: ButtonBaseProps) {
  return (
    <StyledButtonBase sx={sx} {...other}>
      <Iconify icon="eva:arrow-circle-down-fill" width={24} />
    </StyledButtonBase>
  );
}

export function RemoveButton({ sx, ...other }: IconButtonProps) {
  return (
    <StyledIconButton size="small" sx={sx} {...other}>
      <Iconify icon="mingcute:close-line" width={12} />
    </StyledIconButton>
  );
}
