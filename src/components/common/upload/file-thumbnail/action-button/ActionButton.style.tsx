import ButtonBase from '@mui/material/ButtonBase';
import IconButton from '@mui/material/IconButton';
import { alpha, styled } from '@mui/material/styles';

export const StyledButtonBase = styled(ButtonBase)(({ theme }) => ({
  padding: 0,
  top: 0,
  right: 0,
  width: 1,
  height: 1,
  zIndex: 9,
  opacity: 0,
  position: 'absolute',
  color: 'common.white',
  borderRadius: 'inherit',
  transition: theme.transitions.create(['opacity']),
  '&:hover': {
    opacity: 1,
    color: alpha(theme.palette.grey['900'], 0.64),
  },
}));

export const StyledIconButton = styled(IconButton)(({ theme }) => ({
  padding: theme.spacing(0.35),
  top: 4,
  right: 4,
  position: 'absolute',
  color: 'common.white',
  backgroundColor: alpha(theme.palette.grey['900'], 0.48),
  '&:hover': {
    backgroundColor: alpha(theme.palette.grey['900'], 0.72),
  },
}));
