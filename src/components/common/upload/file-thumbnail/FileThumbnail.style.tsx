import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';

export const ThumbnailImage = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
}));

export const ThumbnailIcon = styled('img')(() => ({
  width: 1,
  height: 1,
}));

export const ThumbnailContent = styled(Stack)(() => ({
  width: 36,
  height: 36,
  flexShrink: 0,
  borderRadius: 1.25,
  alignItems: 'center',
  position: 'relative',
  display: 'inline-flex',
  justifyContent: 'center',
}));
