import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import { alpha, styled } from '@mui/material/styles';
import FormHelperText from '@mui/material/FormHelperText';

export const UploadRoot = styled(Box)(() => ({
  width: '100%',
  position: 'relative',
}));

export const UploadDropZone = styled(Box, {
  shouldForwardProp: (prop) =>
    !['hasError', 'isDragActive', 'disabled', 'hasFile'].includes(prop as string),
})<{
  hasError?: boolean;
  isDragActive?: boolean;
  disabled?: boolean;
  hasFile?: boolean;
}>(({ theme, hasError, isDragActive, disabled, hasFile }) => ({
  padding: theme.spacing(5),
  outline: 'none',
  borderRadius: theme.shape.borderRadius,
  cursor: 'pointer',
  overflow: 'hidden',
  position: 'relative',
  backgroundColor: alpha(theme.palette.grey['500'], 0.08),
  border: `1px dashed ${alpha(theme.palette.grey['500'], 0.2)}`,
  transition: theme.transitions.create(['opacity', 'padding']),

  '&:hover': {
    opacity: 0.72,
  },

  ...(isDragActive && {
    opacity: 0.72,
  }),

  ...(disabled && {
    opacity: 0.48,
    pointerEvents: 'none',
  }),

  ...(hasError && {
    color: theme.palette.error.main,
    borderColor: theme.palette.error.main,
    backgroundColor: alpha(theme.palette.error.main, 0.08),
  }),

  ...(hasFile && {
    padding: '28% 0',
  }),
}));

export const UploadHelperText = styled(FormHelperText)(({ theme }) => ({
  paddingInline: theme.spacing(2),
}));

export const MultiPreviewContainer = styled(Stack)(({ theme }) => ({
  marginBlock: theme.spacing(3),
}));

export const MultiPreviewActions = styled(Stack)(() => ({
  flexDirection: 'row',
  justifyContent: 'flex-end',
}));

export const RemoveAllButton = styled(Button)(() => ({
  color: 'inherit',
}));

export const UploadButton = styled(Button)(() => ({}));
