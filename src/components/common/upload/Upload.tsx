import { useDropzone } from 'react-dropzone';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import { UploadPlaceholder } from './components/placeholder/Placeholder';
import { RejectionFiles } from './components/rejection-files/RejectionFiles';
import { MultiFilePreview } from './components/preview-multi-file/PreviewMultiFile';
import {
  DeleteButton,
  SingleFilePreview,
} from './components/preview-single-file/PreviewSingleFile';
import {
  UploadRoot,
  UploadButton,
  UploadDropZone,
  RemoveAllButton,
  UploadHelperText,
  MultiPreviewActions,
} from './Upload.style';

import type { UploadProps } from './types';

export function Upload({
  sx,
  value,
  error,
  disabled,
  onDelete,
  onUpload,
  onRemove,
  thumbnail,
  helperText,
  onRemoveAll,
  multiple = false,
  ...other
}: UploadProps) {
  const { getRootProps, getInputProps, isDragActive, isDragReject, fileRejections } = useDropzone({
    multiple,
    disabled,
    ...other,
  });

  const isArray = Array.isArray(value) && multiple;
  const hasFile = !isArray && !!value;
  const hasFiles = isArray && !!value.length;
  const hasError = isDragReject || !!error;

  const renderMultiPreview = hasFiles && (
    <>
      <MultiFilePreview files={value} thumbnail={thumbnail} onRemove={onRemove} />

      {(onRemoveAll || onUpload) && (
        <MultiPreviewActions spacing={1.5}>
          {onRemoveAll && (
            <RemoveAllButton variant="outlined" size="small" onClick={onRemoveAll}>
              Remove all
            </RemoveAllButton>
          )}

          {onUpload && (
            <UploadButton
              size="small"
              variant="contained"
              onClick={onUpload}
              startIcon={<Iconify icon="eva:cloud-upload-fill" />}
            >
              Upload
            </UploadButton>
          )}
        </MultiPreviewActions>
      )}
    </>
  );

  return (
    <UploadRoot sx={sx}>
      <UploadDropZone
        {...getRootProps()}
        hasError={hasError}
        isDragActive={isDragActive}
        disabled={disabled}
        hasFile={hasFile}
      >
        <input {...getInputProps()} />

        {hasFile ? <SingleFilePreview file={value as File | string} /> : <UploadPlaceholder />}
      </UploadDropZone>

      {hasFile && <DeleteButton onClick={onDelete} />}

      {helperText && <UploadHelperText error={!!error}>{helperText}</UploadHelperText>}

      <RejectionFiles files={fileRejections} />

      {renderMultiPreview}
    </UploadRoot>
  );
}
