import { styled } from '@mui/material/styles';
import { Box, alpha, IconButton } from '@mui/material';

export const PreviewContainer = styled(Box)(() => ({
  padding: 1,
  top: 0,
  left: 0,
  width: 1,
  height: 1,
  position: 'absolute',
}));

export const PreviewImage = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
}));

export const StyledDeleteButton = styled(IconButton)(({ theme }) => ({
  top: 16,
  right: 16,
  zIndex: 9,
  position: 'absolute',
  color: alpha(theme.palette.common.white, 0.8),
  backgroundColor: alpha(theme.palette.grey[900], 0.72),
  '&:hover': {
    backgroundColor: alpha(theme.palette.grey[900], 0.48),
  },
}));
