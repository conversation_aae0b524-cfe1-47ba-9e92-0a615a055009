import type { IconButtonProps } from '@mui/material/IconButton';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import { PreviewImage, PreviewContainer, StyledDeleteButton } from './PreviewSingleFile.style';

import type { SingleFilePreviewProps } from '../../types';

export function SingleFilePreview({ file }: SingleFilePreviewProps) {
  const fileName = typeof file === 'string' ? file : file.name;
  const previewUrl = typeof file === 'string' ? file : URL.createObjectURL(file);

  return (
    <PreviewContainer>
      <PreviewImage alt={fileName ?? 'preview'} src={previewUrl} />
    </PreviewContainer>
  );
}

export function DeleteButton({ sx, ...other }: IconButtonProps) {
  return (
    <StyledDeleteButton size="small" sx={sx} {...other}>
      <Iconify icon="mingcute:close-line" width={18} />
    </StyledDeleteButton>
  );
}
