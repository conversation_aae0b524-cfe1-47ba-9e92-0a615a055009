import type { BoxProps } from '@mui/material/Box';

import { IconButton } from '@mui/material';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import {
  TitleText,
  DragDropText,
  TextContainer,
  InstructionText,
  PlaceholderWrapper,
} from './Placeholder.style';

export function UploadPlaceholder({ ...other }: BoxProps) {
  return (
    <PlaceholderWrapper {...other}>
      <IconButton>
        <Iconify icon="eva:cloud-upload-fill" width={28} />
      </IconButton>

      <TextContainer>
        <TitleText variant="subtitle1">Drop or select file</TitleText>
        <InstructionText variant="body2">
          <DragDropText>Click to upload</DragDropText>
          or Drag and Drop through your machine.
        </InstructionText>
      </TextContainer>
    </PlaceholderWrapper>
  );
}
