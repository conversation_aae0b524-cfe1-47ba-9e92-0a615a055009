import { styled } from '@mui/material/styles';
import { Box, Stack, Typography } from '@mui/material';

export const PlaceholderWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  flexDirection: 'column',
  justifyContent: 'center',
}));

export const TextContainer = styled(Stack)(() => ({
  gap: 1,
  textAlign: 'center',
}));

export const DragDropText = styled('span')(({ theme }) => ({
  marginInline: theme.spacing(0.5),
  color: theme.palette.primary.main,
  textDecoration: 'underline',
}));

export const InstructionText = styled(Typography)(() => ({
  color: 'text.secondary',
}));

export const TitleText = styled(Typography)(() => ({
  fontWeight: 400,
}));
