import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import { alpha, styled } from '@mui/material/styles';

export const RejectionPaper = styled(Paper)(({ theme }) => ({
  paddingY: theme.spacing(1),
  paddingX: theme.spacing(2),
  marginTop: theme.spacing(3),
  textAlign: 'left',
  borderStyle: 'dashed',
  borderColor: theme.palette.error.main,
  backgroundColor: alpha(theme.palette.error.main, 0.08),
}));

export const RejectionItem = styled(Box)(({ theme }) => ({
  marginY: theme.spacing(1),
}));

export const RejectionError = styled(Box)(() => ({
  display: 'block',
}));
