import type { FileRejection } from 'react-dropzone';

import Typography from '@mui/material/Typography';

import { fData } from 'src/utils/format-number';

import { fileData } from 'src/components/common/upload/file-thumbnail';

import { RejectionItem, RejectionPaper, RejectionError } from './RejectionFiles.style';

type RejectionFilesProps = {
  files: readonly FileRejection[];
};

export function RejectionFiles({ files }: RejectionFilesProps) {
  if (!files.length) {
    return null;
  }

  return (
    <RejectionPaper variant="outlined">
      {files.map(({ file, errors }) => {
        const { path, size } = fileData(file);

        return (
          <RejectionItem key={path}>
            <Typography variant="subtitle2" noWrap>
              {path} - {size ? fData(size) : ''}
            </Typography>

            {errors.map((error) => (
              <RejectionError key={error.code} component="span" sx={{ typography: 'caption' }}>
                - {error.message}
              </RejectionError>
            ))}
          </RejectionItem>
        );
      })}
    </RejectionPaper>
  );
}
