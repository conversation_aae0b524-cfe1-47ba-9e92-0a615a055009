import { Box, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';

export const PreviewContainer = styled(Box)(({ theme }) => ({
  gap: theme.spacing(1),
  display: 'flex',
  flexDirection: 'column',
}));

export const PreviewItem = styled(Box)(({ theme }) => ({
  paddingY: theme.spacing(1),
  paddingRight: theme.spacing(1),
  paddingLeft: theme.spacing(1.5),
  gap: theme.spacing(1.5),
  display: 'flex',
  borderRadius: theme.shape.borderRadius,
  alignItems: 'center',
  border: `solid 1px ${alpha(theme.palette.grey[500], 0.16)}`,
}));

export const ThumbnailPreviewItem = styled(Box)(() => ({
  display: 'inline-flex',
}));

export const NodeWrapper = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'thumbnail',
})<{ thumbnail?: boolean }>(({ thumbnail }) => ({
  ...(thumbnail && {
    width: 'auto',
    display: 'inline-flex',
  }),
}));
