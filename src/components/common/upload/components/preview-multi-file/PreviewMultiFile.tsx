import { alpha } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';

import { fData } from 'src/utils/format-number';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';
import { fileData, FileThumbnail } from 'src/components/common/upload/file-thumbnail';

import {
  NodeWrapper,
  PreviewItem,
  PreviewContainer,
  ThumbnailPreviewItem,
} from './PreviewMultiFile.style';

import type { MultiFilePreviewProps } from '../../types';

export function MultiFilePreview({
  sx,
  files = [],
  onRemove,
  lastNode,
  thumbnail,
  slotProps,
  firstNode,
}: MultiFilePreviewProps) {
  const renderFirstNode = firstNode && (
    <NodeWrapper component="li" thumbnail={thumbnail}>
      {firstNode}
    </NodeWrapper>
  );

  const renderLastNode = lastNode && (
    <NodeWrapper component="li" thumbnail={thumbnail}>
      {lastNode}
    </NodeWrapper>
  );

  return (
    <PreviewContainer
      component="ul"
      sx={{
        ...(thumbnail && {
          flexWrap: 'wrap',
          flexDirection: 'row',
        }),
        ...sx,
      }}
    >
      {renderFirstNode}

      {files.map((file) => {
        const { name, size } = fileData(file);

        if (thumbnail) {
          return (
            <ThumbnailPreviewItem component="li" key={name}>
              <FileThumbnail
                tooltip
                imageView
                file={file}
                onRemove={() => onRemove?.(file)}
                sx={{
                  width: 80,
                  height: 80,
                  border: `solid 1px ${alpha('#919eab', 0.16)}`,
                }}
                slotProps={{ icon: { width: 36, height: 36 } }}
                {...slotProps?.thumbnail}
              />
            </ThumbnailPreviewItem>
          );
        }

        return (
          <PreviewItem component="li" key={name}>
            <FileThumbnail file={file} {...slotProps?.thumbnail} />

            <ListItemText
              primary={name}
              secondary={fData(size)}
              secondaryTypographyProps={{ component: 'span', typography: 'caption' }}
            />

            {onRemove && (
              <IconButton size="small" onClick={() => onRemove(file)}>
                <Iconify icon="mingcute:close-line" width={16} />
              </IconButton>
            )}
          </PreviewItem>
        );
      })}

      {renderLastNode}
    </PreviewContainer>
  );
}
