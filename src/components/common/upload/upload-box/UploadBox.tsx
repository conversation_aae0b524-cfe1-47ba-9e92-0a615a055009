import { useDropzone } from 'react-dropzone';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import { UploadBoxRoot } from './UploadBox.style';

import type { UploadProps } from '../types';

export function UploadBox({ placeholder, error, disabled, sx, ...other }: UploadProps) {
  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    disabled,
    ...other,
  });

  const hasError = isDragReject || error;

  return (
    <UploadBoxRoot
      {...getRootProps()}
      hasError={hasError}
      isDragActive={isDragActive}
      disabled={disabled}
      sx={sx}
    >
      <input {...getInputProps()} />
      {placeholder || <Iconify icon="eva:cloud-upload-fill" width={28} />}
    </UploadBoxRoot>
  );
}
