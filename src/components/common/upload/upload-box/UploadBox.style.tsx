import Box from '@mui/material/Box';
import { alpha, styled } from '@mui/material/styles';

export const UploadBoxRoot = styled(Box, {
  shouldForwardProp: (prop) => !['hasError', 'isDragActive', 'disabled'].includes(prop as string),
})<{
  hasError?: boolean;
  isDragActive?: boolean;
  disabled?: boolean;
}>(({ theme, hasError, isDragActive, disabled }) => ({
  width: 64,
  height: 64,
  flexShrink: 0,
  display: 'flex',
  borderRadius: 1,
  cursor: 'pointer',
  alignItems: 'center',
  color: 'text.disabled',
  justifyContent: 'center',
  bgcolor: alpha(theme.palette.grey['500'], 0.08),
  border: `dashed 1px ${alpha(theme.palette.grey['500'], 0.16)}`,
  transition: theme.transitions.create(['opacity', 'border-color', 'background-color']),

  '&:hover': {
    opacity: 0.72,
  },

  ...(isDragActive && {
    opacity: 0.72,
  }),

  ...(disabled && {
    opacity: 0.48,
    pointerEvents: 'none',
  }),

  ...(hasError && {
    color: 'error.main',
    borderColor: 'error.main',
    bgcolor: alpha(theme.palette.error.main, 0.08),
  }),
}));
