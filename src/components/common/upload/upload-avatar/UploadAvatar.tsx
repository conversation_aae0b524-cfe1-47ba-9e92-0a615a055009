import { useState, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';

import { alpha } from '@mui/material';
import Typography from '@mui/material/Typography';

import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import { RejectionFiles } from '../components/rejection-files/RejectionFiles';
import {
  PreviewImg,
  HelperText,
  PlaceholderBox,
  UploadAvatarRoot,
  UploadAvatarContent,
} from './UploadAvatar.style';

import type { UploadProps } from '../types';

export function UploadAvatar({ sx, error, value, disabled, helperText, ...other }: UploadProps) {
  const { getRootProps, getInputProps, isDragActive, isDragReject, fileRejections } = useDropzone({
    multiple: false,
    disabled,
    accept: { 'image/*': [] },
    ...other,
  });

  const hasFile = !!value;
  const hasError = isDragReject || !!error;
  const [preview, setPreview] = useState('');

  useEffect(() => {
    if (typeof value === 'string') {
      setPreview(value);
    } else if (value instanceof File) {
      setPreview(URL.createObjectURL(value));
    }
  }, [value]);

  const renderPreview = hasFile && <PreviewImg alt="avatar" src={preview} />;

  const renderPlaceholder = (
    <PlaceholderBox className="upload-placeholder" hasFile={hasFile} hasError={hasError}>
      <Iconify icon="solar:camera-add-bold" width={32} />
      <Typography variant="caption">{hasFile ? 'Update photo' : 'Upload photo'}</Typography>
    </PlaceholderBox>
  );

  const renderContent = (
    <UploadAvatarContent>
      {renderPreview}
      {renderPlaceholder}
    </UploadAvatarContent>
  );

  return (
    <>
      <UploadAvatarRoot
        {...getRootProps()}
        sx={{
          ...(isDragActive && { opacity: 0.72 }),
          ...(disabled && { opacity: 0.48, pointerEvents: 'none' }),
          ...(hasError && { borderColor: 'error.main' }),
          ...(hasFile && {
            ...(hasError && {
              bgcolor: (theme) => alpha(theme.palette.error.main, 0.08),
            }),
            '&:hover .upload-placeholder': { opacity: 1 },
          }),
          ...sx,
        }}
      >
        <input {...getInputProps()} />
        {renderContent}
      </UploadAvatarRoot>

      {helperText && <HelperText variant="caption">{helperText}</HelperText>}

      <RejectionFiles files={fileRejections} />
    </>
  );
}
