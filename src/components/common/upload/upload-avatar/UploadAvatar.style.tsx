import Box from '@mui/material/Box';
import { alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

export const UploadAvatarRoot = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1),
  margin: 'auto',
  width: 144,
  height: 144,
  cursor: 'pointer',
  overflow: 'hidden',
  borderRadius: '50%',
  border: `1px dashed ${alpha(theme.palette.grey['500'], 0.2)}`,
}));

export const UploadAvatarContent = styled(Box)(() => ({
  width: 1,
  height: 1,
  overflow: 'hidden',
  borderRadius: '50%',
  position: 'relative',
}));

export const PreviewImg = styled('img')(() => ({
  width: 1,
  height: 1,
  borderRadius: '50%',
}));

export const PlaceholderBox = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'hasFile' && prop !== 'hasError',
})<{ hasFile?: boolean; hasError?: boolean }>(({ theme, hasFile, hasError }) => ({
  top: 0,
  gap: 1,
  left: 0,
  width: 1,
  height: 1,
  zIndex: 9,
  display: 'flex',
  borderRadius: '50%',
  position: 'absolute',
  alignItems: 'center',
  color: 'text.disabled',
  flexDirection: 'column',
  justifyContent: 'center',
  bgcolor: alpha(theme.palette.grey['500'], 0.08),
  transition: theme.transitions.create(['opacity'], {
    duration: theme.transitions.duration.shorter,
  }),
  '&:hover': { opacity: 0.72 },
  ...(hasError && {
    color: 'error.main',
    bgcolor: alpha(theme.palette.error.main, 0.08),
  }),
  ...(hasFile && {
    zIndex: 9,
    opacity: 0,
    color: 'common.white',
    bgcolor: alpha(theme.palette.grey['900'], 0.64),
  }),
}));

export const HelperText = styled(Typography)(({ theme }) => ({
  marginTop: theme.spacing(3),
  marginInline: 'auto',
  display: 'block',
  textAlign: 'center',
  color: theme.palette.text.disabled,
}));
