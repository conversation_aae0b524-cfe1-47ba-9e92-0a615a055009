import { useState } from 'react';
import { useLocation } from 'react-router';

import { List, Collapse } from '@mui/material';

import { type Children } from 'src/shared/config-nav-data';

import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import {
  NavItemLink,
  NavItemButtonLabel,
  NavItemIconWrapper,
  CollapseIconWrapper,
} from './CollapsibleNavItem.style';

export interface CollapsibleNavItemProps {
  icon: JSX.Element;
  label: string;
  link: string;
  children: Children[];
  handleNavigation?: () => void;
}

const CollapsibleNavItem = ({
  icon,
  label,
  link,
  children,
  handleNavigation,
}: CollapsibleNavItemProps) => {
  const { pathname } = useLocation();
  const [open, setOpen] = useState(children.some((child) => pathname.startsWith(child.link)));

  const isActive =
    pathname.startsWith(link) || children.some((child) => pathname.startsWith(child.link));

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    setOpen(!open);
    if (handleNavigation) {
      handleNavigation();
    }
  };

  return (
    <>
      <NavItemLink to={link} isActive={isActive} onClick={handleToggle} className="parent-nav-item">
        <NavItemIconWrapper isActive={isActive}>{icon}</NavItemIconWrapper>
        <NavItemButtonLabel>{label}</NavItemButtonLabel>
        <CollapseIconWrapper isActive={isActive}>
          <Iconify
            icon={open ? Icons.up_chev_small : Icons.down_chev_small}
            width={16}
            height={16}
          />
        </CollapseIconWrapper>
      </NavItemLink>

      <Collapse in={open} timeout="auto" unmountOnExit>
        <List
          component="div"
          disablePadding
          sx={{
            py: 0.5,
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            backgroundColor: (theme) => theme.palette.grey[50],
            borderRadius: '8px',
            mx: 0.5,
          }}
        >
          {children.map((child, index) => {
            const childIsActive = pathname.startsWith(child.link);
            return (
              <NavItemLink
                key={index}
                to={child.link}
                isActive={childIsActive}
                sx={{
                  pl: 4,
                  borderRadius: '4px',
                }}
                className="child-nav-item"
              >
                <NavItemButtonLabel>{child.label}</NavItemButtonLabel>
              </NavItemLink>
            );
          })}
        </List>
      </Collapse>
    </>
  );
};

export default CollapsibleNavItem;
