import { Link } from 'react-router-dom';

import { styled } from '@mui/material/styles';

type SidebarNavigationLinkProps = {
  isActive?: boolean;
};

export const NavItemLink = styled(Link, {
  shouldForwardProp: (prop) => prop !== 'isActive',
})<SidebarNavigationLinkProps>(({ theme, isActive }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '.8rem',
  justifyContent: 'start',
  padding: '.76rem .7rem',
  borderRadius: '8px',
  fontWeight: theme.typography.fontWeightMedium,
  textDecoration: 'none',
  color: isActive ? theme.palette.primary.main : theme.palette.grey[500],
  backgroundColor: isActive ? theme.palette.primary.lighter : 'transparent',
  cursor: 'pointer',
  position: 'relative',
  transition: 'all 0.2s ease-in-out',

  '&:hover': {
    color: isActive ? theme.palette.primary.main : theme.palette.grey[500],
    backgroundColor: !isActive ? theme.palette.grey[200] : undefined,
  },

  '&.parent-nav-item': {
    marginBottom: theme.spacing(0.5),
    borderBottom: isActive ? `1px solid ${theme.palette.primary.lighter}` : 'none',
  },

  '&.child-nav-item': {
    backgroundColor: isActive ? theme.palette.primary.lighter : 'transparent',
    marginLeft: theme.spacing(1.5),
    marginRight: theme.spacing(1),
    width: 'auto',
    padding: '.6rem .7rem',
    borderLeft: `2px solid ${isActive ? theme.palette.primary.main : theme.palette.grey[300]}`,
  },
}));

export const NavItemButtonLabel = styled('span')(({ theme }) => ({
  textTransform: 'capitalize',
  fontSize: theme.typography.pxToRem(14),
  fontWeight: theme.typography.fontWeightMedium,
  flex: 1,
}));

export const NavItemIconWrapper = styled('div', {
  shouldForwardProp: (prop) => prop !== 'isActive',
})<SidebarNavigationLinkProps>(({ theme, isActive }) => ({
  display: 'grid',
  placeContent: 'center',
  flexShrink: 0,

  '& .MuiSvgIcon-root': {
    fill: isActive ? theme.palette.primary.main : theme.palette.grey[500],
  },
}));

export const CollapseIconWrapper = styled('div', {
  shouldForwardProp: (prop) => prop !== 'isActive',
})<SidebarNavigationLinkProps>(({ theme, isActive }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: isActive ? theme.palette.primary.main : theme.palette.grey[500],
}));
