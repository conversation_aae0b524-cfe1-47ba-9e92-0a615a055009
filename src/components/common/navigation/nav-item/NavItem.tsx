import { NavItemLink, NavItemButtonLabel, NavItemIconWrapper } from './NavItem.style';

export interface NavItemProps {
  icon: JSX.Element;
  label: string;
  active: boolean;
  link: string;
  handleNavigation?: () => void;
}

const NavItem = ({ icon, label, link, handleNavigation, active }: NavItemProps) => (
  <NavItemLink to={label === 'logout' ? '' : link} isActive={active} onClick={handleNavigation}>
    <NavItemIconWrapper isActive={active}>{icon}</NavItemIconWrapper>
    <NavItemButtonLabel>{label}</NavItemButtonLabel>
  </NavItemLink>
);

export default NavItem;
