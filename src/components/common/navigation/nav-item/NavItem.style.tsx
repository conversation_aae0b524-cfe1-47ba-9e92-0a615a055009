import { Link } from 'react-router-dom';

import { styled } from '@mui/material/styles';

type SidebarNavigationLinkProps = {
  isActive?: boolean;
};

export const NavItemLink = styled(Link, {
  shouldForwardProp: (prop) => prop !== 'isActive',
})<SidebarNavigationLinkProps>(({ theme, isActive }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '.8rem',
  justifyContent: 'start',
  padding: '.76rem .7rem',
  borderRadius: '8px',
  fontWeight: theme.typography.fontWeightMedium,
  textDecoration: 'none',
  color: isActive ? theme.palette.primary.main : theme.palette.grey[500],
  backgroundColor: isActive ? theme.palette.primary.lighter : 'transparent',
  cursor: 'pointer',

  '&:hover': {
    color: isActive ? theme.palette.primary.main : theme.palette.grey[500],
    backgroundColor: !isActive ? theme.palette.grey[200] : undefined,
  },
}));

export const NavItemButtonLabel = styled('span')(({ theme }) => ({
  textTransform: 'capitalize',
  fontSize: theme.typography.pxToRem(14),
  fontWeight: theme.typography.fontWeightMedium,
}));

export const NavItemIconWrapper = styled('div', {
  shouldForwardProp: (prop) => prop !== 'isActive',
})<SidebarNavigationLinkProps>(({ theme, isActive }) => ({
  display: 'grid',
  placeContent: 'center',
  flexShrink: 0,

  '& .MuiSvgIcon-root': {
    fill: isActive ? theme.palette.primary.main : theme.palette.grey[500],
  },
}));
