import { styled } from '@mui/material/styles';
import { Box, SvgIcon, IconButton } from '@mui/material';

export const SidebarToggleWrapper = styled(Box)(() => ({
  position: 'absolute',
  top: '20%',
  right: '-0.8rem',
}));

export const IconButtonWrapper = styled(IconButton)(({ theme }) => ({
  border: `1px solid ${theme.palette.grey[300]}`,
  width: 25,
  height: 25,
  padding: 0,
  backgroundColor: theme.palette.common.white,
}));

export const IconWrapper = styled(SvgIcon)(({ theme }) => ({
  color: theme.palette.grey[400],
}));
