import { Icons } from 'src/components/common/icons/iconify/Icons';
import { Iconify } from 'src/components/common/icons/iconify/Iconify';

import { IconWrapper, IconButtonWrapper, SidebarToggleWrapper } from './SidebarToggleButton.style';

export interface SidebarToggleButtonProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

const SidebarToggleButton = ({ isCollapsed, toggleSidebar }: SidebarToggleButtonProps) => (
  <SidebarToggleWrapper>
    <IconButtonWrapper onClick={toggleSidebar}>
      <IconWrapper sx={{ transform: isCollapsed ? 'scaleX(-1)' : 'unset' }}>
        <Iconify icon={Icons.left_chev_small} />
      </IconWrapper>
    </IconButtonWrapper>
  </SidebarToggleWrapper>
);

export default SidebarToggleButton;
