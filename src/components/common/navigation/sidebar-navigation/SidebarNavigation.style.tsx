import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

interface SidebarWrapperProps {
  isCollapsed?: boolean;
}

export const SidebarWrapper = styled(Box, {
  shouldForwardProp: (props) => props !== 'isCollapsed',
})<SidebarWrapperProps>(({ theme, isCollapsed }) => ({
  position: 'sticky',
  top: 0,
  display: 'flex',
  flexShrink: 0,
  gap: '1rem',
  width: isCollapsed ? '90px' : '280px',
  transition: 'width 0.3s ease-in-out',
  transitionDuration: '200ms',
  borderRight: `2px solid ${theme.palette.grey[200]}`,
  flexDirection: 'column',
  justifyContent: 'space-between',
  height: '100vh',
  maxHeight: '100vh',
  overflow: 'hidden',
  padding: `1.5rem ${isCollapsed ? '.5rem' : '1rem'}`,
  paddingBottom: '2rem',
}));

export const SidebarNavHeader = styled('div')(() => ({}));

export const SidebarNavLogoWrapper = styled('div')(() => ({}));

export const SidebarNavProfileWrapper = styled('div')(() => ({}));

export const SideBarNavListWrapper = styled('div')(({ theme }) => ({
  flex: 1,
  marginBlock: '2rem',
  overflowY: 'auto',
  overflowX: 'hidden',
  height: 'calc(100vh - 250px)',
  scrollbarWidth: 'thin',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: theme.palette.grey[100],
    borderRadius: '10px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.grey[300],
    borderRadius: '10px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: theme.palette.grey[400],
  },
}));

export const MiniSideBarNavWrapper = styled('div')(() => ({
  display: 'grid',
}));
