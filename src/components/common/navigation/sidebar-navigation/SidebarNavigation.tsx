import { useLocation } from 'react-router';

import { useBoolean } from 'src/hooks/use-boolean';
import { useResponsive } from 'src/hooks/use-responsive';

import { type NavLinks, getNavigationLinks } from 'src/shared/config-nav-data';

import Logo from '../../logo/Logo';
import NavList from '../nav-list/NavList';
import Profile from '../../profile/Profile';
import ScrollbarArea from '../../scrollbar/ScrollbarArea';
import SidebarFooter from './sidebar-footer/SidebarFooter';
import {
  SidebarWrapper,
  SidebarNavHeader,
  SideBarNavListWrapper,
  SidebarNavProfileWrapper,
} from './SidebarNavigation.style';

type SidebarProps = {
  navLinks: NavLinks[];
  userName: string;
  adminRole: string;
};

const SidebarNavigation = () => {
  const { pathname } = useLocation();

  const isSidebarCollapsed = useBoolean();
  const mdUp = useResponsive('up', 'md');

  const navigation = getNavigationLinks(pathname);

  return (
    <>
      {mdUp && (
        <SidebarWrapper isCollapsed={isSidebarCollapsed.value}>
          {!isSidebarCollapsed.value && (
            <RenderExpandedSidebar
              navLinks={navigation}
              userName="Seshan A"
              adminRole="Super Admin"
            />
          )}
        </SidebarWrapper>
      )}
    </>
  );
};

const RenderExpandedSidebar = ({ navLinks, adminRole, userName }: SidebarProps) => (
  <>
    <SidebarNavHeader>
      <Logo showLabel />

      <SidebarNavProfileWrapper>
        <Profile
          name={userName}
          adminRole={adminRole}
          profileSrc="/assets/placeholderProfile.png"
        />
      </SidebarNavProfileWrapper>
    </SidebarNavHeader>

    <SideBarNavListWrapper>
      <ScrollbarArea>
        <NavList navLinks={navLinks} subheader="primary" />
      </ScrollbarArea>
    </SideBarNavListWrapper>

    <SidebarFooter />
  </>
);

export default SidebarNavigation;
