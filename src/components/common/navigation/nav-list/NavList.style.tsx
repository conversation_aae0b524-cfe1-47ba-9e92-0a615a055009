import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

export const LinkViewWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

export const LinkViewSubHeader = styled(Typography)(({ theme }) => ({
  gap: 1,
  cursor: 'pointer',
  alignItems: 'center',
  position: 'relative',
  typography: 'overline',
  display: 'inline-flex',
  alignSelf: 'flex-start',
  color: theme.palette.grey[500],
  fontSize: theme.typography.pxToRem(11),
  fontWeight: theme.typography.fontWeightBold,
  textTransform: 'uppercase',
  marginBottom: theme.spacing(2),
}));
