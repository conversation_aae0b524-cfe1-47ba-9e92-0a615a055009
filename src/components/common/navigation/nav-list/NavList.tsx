import { useLocation } from 'react-router';

import { useAuth } from 'src/providers/AuthProvider';
import { type NavLinks } from 'src/shared/config-nav-data';
import { UserNavigations } from 'src/shared/enums/userNavigation';

import NavItem from '../nav-item/NavItem';
import { LinkViewWrapper, LinkViewSubHeader } from './NavList.style';
import CollapsibleNavItem from '../collapsible-nav-item/CollapsibleNavItem';

export interface NavListProps {
  navLinks: NavLinks[];
  subheader: string;
}

const NavList = ({ navLinks, subheader }: NavListProps) => {
  const { pathname } = useLocation();
  const { logout } = useAuth();

  const handleNavigations = (navLabel: string) => {
    switch (navLabel) {
      case UserNavigations.HOME:
      case UserNavigations.PROFILE:
        console.log(`${navLabel} Clicked`);
        break;
      case UserNavigations.LOGOUT:
        logout();
        break;
      default:
        break;
    }
  };
  return (
    <LinkViewWrapper>
      <LinkViewSubHeader variant="caption">{subheader}</LinkViewSubHeader>
      {navLinks?.map(({ icon, label, link, children }, i) =>
        children ? (
          <CollapsibleNavItem
            key={i}
            icon={icon}
            label={label}
            link={link as string}
            children={children}
            handleNavigation={() => handleNavigations(label)}
          />
        ) : (
          <NavItem
            key={i}
            icon={icon}
            label={label}
            link={link as string}
            handleNavigation={() => handleNavigations(label)}
            active={link === '/' ? pathname === '/' : pathname.startsWith(link as string)}
          />
        )
      )}
    </LinkViewWrapper>
  );
};

export default NavList;
