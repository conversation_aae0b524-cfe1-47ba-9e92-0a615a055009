import { Box, Stack, Avatar, IconButton } from '@mui/material';

import { useAuth } from 'src/providers/AuthProvider';
import { userLinks } from 'src/shared/config-nav-data';
import { UserNavigations } from 'src/shared/enums/userNavigation';

import Logo from '../../logo/Logo';
import {
  HomeLogoWrapper,
  HomeNavbarWrapper,
  HomeNavbarUserName,
  HomeNavbarUserRole,
  HomeNavbarContainer,
} from './HomeNavigation.style';

const HomeNavigation = () => {
  const { userName, role, logout } = useAuth();

  const handleNavigations = (navLabel: string) => {
    switch (navLabel) {
      case UserNavigations.HOME:
      case UserNavigations.PROFILE:
        console.log(`${navLabel} Clicked`);
        break;
      case UserNavigations.LOGOUT:
        logout();
        break;
      default:
        break;
    }
  };

  return (
    <HomeNavbarContainer>
      <HomeNavbarWrapper>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <HomeLogoWrapper>
            <Logo />
          </HomeLogoWrapper>

          <Stack direction="row" justifyItems="center" spacing={3}>
            <Stack direction="row" spacing={0.5} alignItems="center">
              {userLinks?.map(({ label, icon }, index) => (
                <IconButton key={index} onClick={() => handleNavigations(label)}>
                  {icon}
                </IconButton>
              ))}
            </Stack>

            <Stack direction="row" spacing={1.5} justifyItems="center">
              <Avatar alt="Avatar" src="/assets/placeholderProfile.png" />
              <Box>
                <HomeNavbarUserName>{userName}</HomeNavbarUserName>
                <HomeNavbarUserRole>{role}</HomeNavbarUserRole>
              </Box>
            </Stack>
          </Stack>
        </Stack>
      </HomeNavbarWrapper>
    </HomeNavbarContainer>
  );
};

export default HomeNavigation;
