import { styled } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';

export const HomeNavbarProfileWrapper = styled(Box)(({ theme }) => ({
  width: '40px',
  height: '40px',
  backgroundColor: theme.palette.grey['50'],
  borderRadius: '50%',
  overflow: 'hidden',
  outline: '2px solid lightgreen',
  outlineOffset: '2px',
}));

export const HomeLogoWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
}));

export const HomeNavbarProfile = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
}));

export const HomeNavbarUserName = styled(Typography)(({ theme }) => ({
  fontWeight: theme.typography.fontWeightBold,
  fontSize: theme.typography.body1.fontSize,
}));

export const HomeNavbarUserRole = styled(Typography)(({ theme }) => ({
  fontSize: theme.typography.body2.fontSize,
  color: theme.palette.grey[500],
  lineHeight: '12px',
}));

export const HomeNavbarContainer = styled(Box)(({ theme }) => ({
  position: 'sticky',
  top: 0,
  zIndex: 100,
  width: '100%',
  paddingTop: '3rem',
  background: theme.palette.common.white,
}));

export const HomeNavbarWrapper = styled(Box)(({ theme }) => ({
  padding: '1rem 2rem',
  width: '80%',
  marginInline: 'auto',
  borderRadius: '.7rem',
  boxShadow: `0px 0px 50px ${theme.palette.grey[300]}`,
}));
