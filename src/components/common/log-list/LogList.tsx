import type { AuditLogEntry } from 'src/shared/services/activity-logs/activity-logs.type';

import { Chip } from '@mui/material';

import { formatDateTime } from 'src/utils/date';

import ScrollbarArea from '../scrollbar/ScrollbarArea';
import {
  Root,
  LogTitle,
  LogItemUser,
  LogItemHeader,
  LogItemAction,
  LogItemWrapper,
  LogItemContent,
  LogItemMessage,
  LogItemTimestamp,
  EmptyLogsMessage,
  LogsContentWrapper,
} from './LogList.style';

interface LogItemProps {
  adminName: string;
  remarks: string;
  timestamp?: string;
  initialValue: string;
  finalValue: string;
}

const LogItem = ({ adminName, remarks, timestamp, initialValue, finalValue }: LogItemProps) => {
  const formattedTime = timestamp
    ? formatDateTime(new Date(timestamp))
    : formatDateTime(new Date());

  return (
    <LogItemWrapper>
      <LogItemHeader>
        <LogItemUser>
          <LogTitle variant="subtitle2">{adminName}</LogTitle> |
          <LogItemTimestamp>{formattedTime}</LogItemTimestamp>
        </LogItemUser>

        <LogItemAction>
          {initialValue === finalValue ? (
            <Chip label={finalValue} variant="outlined" size="small" color="success" />
          ) : (
            <>
              {initialValue && (
                <>
                  <Chip
                    label={<s>{initialValue}</s>}
                    variant="outlined"
                    size="small"
                    color="primary"
                    style={{ marginRight: '8px' }}
                  />
                  &rarr;
                </>
              )}
              <Chip label={finalValue} variant="outlined" size="small" color="success" />
            </>
          )}
        </LogItemAction>
      </LogItemHeader>
      <LogItemContent>
        <LogItemMessage variant="caption" color="text.disabled">
          {remarks}
        </LogItemMessage>
      </LogItemContent>
    </LogItemWrapper>
  );
};

interface LogsProps {
  title?: string;
  data: AuditLogEntry[];
}

const Logs = ({ title = 'Activity Logs', data }: LogsProps) => (
  <Root>
    <LogsContentWrapper>
      <ScrollbarArea>
        {data && data?.length > 0 ? (
          data?.map((log, index) => (
            <LogItem
              key={index}
              adminName={log.userName ?? ''}
              remarks={log.remark ?? ''}
              timestamp={log.lastModified ? new Date(log.lastModified).toISOString() : undefined}
              initialValue={log.initialValue}
              finalValue={log.finalValue}
            />
          ))
        ) : (
          <EmptyLogsMessage>No activity logs found</EmptyLogsMessage>
        )}
      </ScrollbarArea>
    </LogsContentWrapper>
  </Root>
);

export default Logs;
