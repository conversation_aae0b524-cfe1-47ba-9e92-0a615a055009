import { styled } from '@mui/material/styles';
import { Box, Paper, Avatar, Divider, Typography } from '@mui/material';

export const Root = styled('div')(({ theme }) => ({
  marginTop: theme.spacing(2),
  overflow: 'hidden',
  borderBlock: `1px solid ${theme.palette.grey[300]}`,
  marginBottom: theme.spacing(2),
}));

export const LogHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

export const LogTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
}));

export const LogCount = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  minWidth: 20,
  height: 20,
  padding: theme.spacing(0, 0.75),
  borderRadius: 10,
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  fontSize: '0.75rem',
  fontWeight: 600,
  marginLeft: theme.spacing(1),
}));

export const LogsContentWrapper = styled(Paper)(({ theme }) => ({
  height: '500px',
  maxHeight: '500px',
  borderRadius: 0,
  boxShadow: 'none',
  backgroundColor: theme.palette.background.paper,
  overflow: 'hidden',
}));

export const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(1.5),
  borderRadius: theme.shape.borderRadius,
  boxShadow: 'none',
  border: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

export const LogItemWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.grey[300]}`,
  '&:last-child': {
    borderBottom: 'none',
  },
}));

export const LogItemHeader = styled('div')({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
});

export const LogItemUser = styled('div')({
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
});

export const LogItemContent = styled('div')(({ theme }) => ({}));

export const LogItemTimestamp = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.75rem',
}));

export const LogItemDivider = styled(Divider)(({ theme }) => ({
  margin: theme.spacing(1, 0),
}));

export const StyledAvatar = styled(Avatar)(({ theme }) => ({
  width: 36,
  height: 36,
  backgroundColor: theme.palette.primary.main,
  fontSize: '1rem',
}));

export const LogItemMessage = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.primary,
  fontSize: '0.875rem',
  lineHeight: 1.5,
}));

export const LogItemAction = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
}));

export const EmptyLogsMessage = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(3),
  textAlign: 'center',
  color: theme.palette.text.secondary,
}));

export const SuccessText = styled(Typography)(({ theme }) => ({
  color: theme.palette.success.main,
}));

export const ErrorText = styled(Typography)(({ theme }) => ({
  color: theme.palette.error.main,
}));

export const StyledTypography = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
}));
