import type { ReactNode } from 'react';

import { useFormikContext } from 'formik';
import React, { useCallback } from 'react';

import { Step, StepLabel } from '@mui/material';

import { useToast } from 'src/providers/ToastProvider';

import { Icons, Iconify } from '../icons/iconify';
import {
  ActionButton,
  StepIconError,
  StyledStepper,
  StepIconActive,
  StepperActions,
  StepperContent,
  StepIconSuccess,
  CustomStepperWrapper,
} from './CustomStepper.style';

type CustomStepperProps = {
  steps: string[];
  children: ReactNode | ReactNode[];
  activeStep: number;
  setActiveStep: (step: number) => void;
  onValidateStep?: (step: number) => Promise<{ isValid: boolean; errors: Record<string, string> }>;
  onFinish?: () => void;
  onNext?: () => void;
  onBack?: () => void;
  showActions?: boolean;
  nextLabel?: string;
  backLabel?: string;
  finishLabel?: string;
  orientation?: 'horizontal' | 'vertical';
  title?: string;
  stepFields?: string[][];
};

const CustomStepper: React.FC<CustomStepperProps> = ({
  steps,
  children,
  activeStep,
  setActiveStep,
  onValidateStep,
  onFinish,
  onNext,
  onBack,
  showActions = true,
  nextLabel = 'Next',
  backLabel = 'Back',
  finishLabel = 'Submit Shipment',
  orientation = 'horizontal',
  title = 'Create New Shipment',
  stepFields,
}) => {
  const [stepErrors, setStepErrors] = React.useState<boolean[]>(Array(steps.length).fill(false));
  const [isLoading, setIsLoading] = React.useState(false);

  const { showToast } = useToast();
  const formik = useFormikContext<any>();

  const handleNext = useCallback(async () => {
    setIsLoading(true);

    if (stepFields && stepFields[activeStep]) {
      const currentStepFields = stepFields[activeStep];
      const touchedObj = currentStepFields.reduce(
        (acc, field) => {
          acc[field] = true;
          return acc;
        },
        {} as Record<string, boolean>
      );

      formik.setTouched({ ...formik.touched, ...touchedObj });

      await new Promise((resolve) => setTimeout(resolve, 50));
    }

    let valid = true;
    if (onValidateStep) {
      try {
        const { isValid, errors } = await onValidateStep(activeStep);
        valid = isValid;
        if (!isValid) {
          formik.setErrors(errors);
          console.log('Form errors:', errors);
        }
      } catch (error) {
        valid = false;
      }
    }

    if (!valid) {
      setStepErrors((prev) => {
        const updated = [...prev];
        updated[activeStep] = true;
        return updated;
      });
      showToast('Please fill the form in this step before continuing.', 'error');
      setIsLoading(false);
      return;
    }

    setStepErrors((prev) => {
      const updated = [...prev];
      updated[activeStep] = false;
      return updated;
    });

    if (activeStep === steps.length - 1) {
      if (onFinish) {
        onFinish();
      }
    } else {
      setActiveStep(activeStep + 1);
      if (onNext) {
        onNext();
      }
    }
    setIsLoading(false);
  }, [
    activeStep,
    onValidateStep,
    stepFields,
    formik,
    steps.length,
    onFinish,
    setActiveStep,
    onNext,
    showToast,
  ]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
      if (onBack) {
        onBack();
      }
    }
  }, [activeStep, onBack, setActiveStep]);

  const getIconForStep = (index: number) => {
    switch (index) {
      case 0:
        return Icons.tracking.customer;
      case 1:
        return Icons.tracking.pincode;
      case 2:
        return Icons.tracking.content;
      case 3:
        return Icons.tracking.service;
      case 4:
        return Icons.tracking.location;
      case 5:
        return Icons.tracking.pricing;
      default:
        return Icons.tracking.shipping;
    }
  };

  return (
    <CustomStepperWrapper>
      <StyledStepper
        activeStep={activeStep}
        alternativeLabel={orientation === 'horizontal'}
        orientation={orientation}
      >
        {steps.map((label, index) => (
          <Step key={label} completed={!stepErrors[index] && activeStep > index}>
            <StepLabel
              StepIconComponent={(props: { icon?: React.ReactNode }) => {
                if (stepErrors[index]) {
                  return (
                    <StepIconError>
                      <Iconify icon="mdi:alert" width={16} />
                    </StepIconError>
                  );
                }
                if (activeStep > index) {
                  return (
                    <StepIconSuccess>
                      <Iconify icon="mdi:check-bold" width={16} />
                    </StepIconSuccess>
                  );
                }
                if (activeStep === index) {
                  return (
                    <StepIconActive>
                      <Iconify icon={getIconForStep(index)} width={16} />
                    </StepIconActive>
                  );
                }
                return <span>{props.icon}</span>;
              }}
            >
              {label}
            </StepLabel>
          </Step>
        ))}
      </StyledStepper>

      <StepperContent>{Array.isArray(children) ? children[activeStep] : children}</StepperContent>

      {showActions && (
        <StepperActions>
          <ActionButton
            disabled={activeStep === 0}
            onClick={handleBack}
            variant="outlined"
            startIcon={<Iconify icon={Icons.left_chev_small} width={20} />}
          >
            {backLabel}
          </ActionButton>
          <ActionButton
            variant="contained"
            color="primary"
            type="submit"
            onClick={handleNext}
            disabled={isLoading}
            endIcon={
              activeStep === steps.length - 1 ? (
                <Iconify icon="mdi:check-all" width={20} />
              ) : (
                <Iconify icon={Icons.right_chev_small} width={20} />
              )
            }
          >
            {activeStep === steps.length - 1 ? finishLabel : nextLabel}
          </ActionButton>
        </StepperActions>
      )}
    </CustomStepperWrapper>
  );
};

export default CustomStepper;
