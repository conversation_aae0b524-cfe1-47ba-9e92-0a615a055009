import { styled } from '@mui/material/styles';
import { <PERSON>, Button, Stepper } from '@mui/material';

import { Iconify } from '../icons/iconify';

export const CustomStepperWrapper = styled('div')(({ theme }) => ({
  width: '100%',
  padding: theme.spacing(3),
  paddingBottom: theme.spacing(8),
  background: 'transparent',
  borderRadius: theme.shape.borderRadius,
  position: 'relative',
}));

export const StyledStepper = styled(Stepper)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  '& .MuiStepLabel-label': {
    fontWeight: 500,
    transition: 'all 0.2s ease',
    fontSize: '0.9rem',
  },
  '& .MuiStepLabel-label.Mui-active': {
    fontWeight: 700,
    color: theme.palette.primary.main,
  },
  '& .MuiStepConnector-line': {
    height: 3,
    borderRadius: 5,
  },
  '& .MuiStepConnector-root.Mui-active .MuiStepConnector-line': {
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
  },
  '& .MuiStepConnector-root.Mui-completed .MuiStepConnector-line': {
    background: theme.palette.primary.main,
  },
}));

export const StepperContent = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(4),
  marginBottom: theme.spacing(4),
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  transition: 'all 0.3s ease',
}));

export const StepperActions = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  position: 'sticky',
  bottom: 0,
  backgroundColor: theme.palette.background.default,
  padding: theme.spacing(2),
  marginTop: theme.spacing(3),
  zIndex: 10,
  backdropFilter: 'blur(8px)',
  boxShadow: '0 -4px 8px rgba(0, 0, 0, 0.05)',
  '& .MuiButton-root': {
    minWidth: 120,
    borderRadius: theme.shape.borderRadius,
    transition: 'all 0.2s',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[2],
    },
  },
}));

export const StepIconError = styled('span')(({ theme }) => ({
  background: theme.palette.error.main,
  borderRadius: '50%',
  color: '#fff',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 30,
  height: 30,
  fontWeight: 700,
  fontSize: 16,
  boxShadow: '0 2px 5px 0 rgba(244, 67, 54, 0.5)',
}));

export const StepIconSuccess = styled('span')(({ theme }) => ({
  background: theme.palette.success.main,
  borderRadius: '50%',
  color: '#fff',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 30,
  height: 30,
  fontWeight: 700,
  fontSize: 16,
  boxShadow: '0 2px 5px 0 rgba(76, 175, 80, 0.5)',
}));

export const StepIconActive = styled('span')(({ theme }) => ({
  background: theme.palette.primary.main,
  borderRadius: '50%',
  color: '#fff',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 30,
  height: 30,
  fontWeight: 700,
  fontSize: 16,
  boxShadow: `0 2px 5px 0 ${theme.palette.primary.main}80`,
}));

export const StepperHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
  paddingBottom: theme.spacing(2),
}));

export const ProgressIndicator = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(2),
  right: theme.spacing(3),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  color: theme.palette.primary.main,
}));

export const HeaderTitleContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

// Component to style the Iconify component when used in the header

export const StyledIconify = styled(Iconify)(({ theme }) => ({
  color: theme.palette.primary.main,
}));

export const ActionButton = styled(Button)(({ theme }) => ({
  flex: '0 0 auto',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[2],
  },
}));
