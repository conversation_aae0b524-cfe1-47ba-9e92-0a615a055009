import { StatusChipRoot } from './StatusChip.style';

export type StatusChipProps = {
  label: string;
  variant: 'soft' | 'filled' | 'outlined';
  color: string;
};

const StatusChip: React.FC<StatusChipProps> = ({ label, variant, color }: StatusChipProps) => (
  <StatusChipRoot
    variant={variant}
    label={label ?? '---'}
    size="small"
    colorCode={color ?? '#000000'}
  />
);

export default StatusChip;
