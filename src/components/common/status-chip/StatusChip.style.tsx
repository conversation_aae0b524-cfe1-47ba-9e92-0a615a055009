import { Chip } from '@mui/material';
import { alpha, styled } from '@mui/material/styles';

interface StatusChipProps {
  colorCode: string;
}

export const StatusChipRoot = styled(Chip, {
  shouldForwardProp: (prop) => prop !== 'colorCode',
})<StatusChipProps>(({ theme, colorCode }) => ({
  fontWeight: theme.typography.fontWeightMedium,
  fontSize: theme.typography.pxToRem(12),
  height: 24,
  paddingInline: theme.spacing(1),

  backgroundColor: alpha(colorCode, 0.1),
  color: colorCode,
}));
