import Button from '@mui/material/Button';

import {
  StyledDialog,
  StyledDivider,
  StyledDialogTitle,
  StyledDialogContent,
  StyledDialogActions,
} from './ConfirmDialog.style';

import type { ConfirmDialogProps } from './ConfirmDialog.types';

export function ConfirmDialog({
  title,
  content,
  action,
  open,
  onClose,
  maxWidth,
  ...other
}: ConfirmDialogProps) {
  return (
    <StyledDialog fullWidth maxWidth={maxWidth} open={open} onClose={onClose} {...other}>
      <StyledDialogTitle>{title}</StyledDialogTitle>

      {content && <StyledDialogContent>{content}</StyledDialogContent>}

      <StyledDivider />

      <StyledDialogActions>
        <Button variant="outlined" color="inherit" onClick={onClose}>
          Cancel
        </Button>
        {action}
      </StyledDialogActions>
    </StyledDialog>
  );
}
