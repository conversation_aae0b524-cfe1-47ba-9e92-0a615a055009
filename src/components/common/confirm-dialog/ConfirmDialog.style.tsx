import { styled } from '@mui/material/styles';
import { Dialog, Divider, DialogTitle, DialogContent, DialogActions } from '@mui/material';

export const StyledDialog = styled(Dialog)(() => ({}));

export const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  paddingBottom: theme.spacing(2),
  background: theme.palette.grey[200],
  marginBottom: '1rem',
}));

export const StyledDialogContent = styled(DialogContent)(() => ({
  typography: 'body1',
}));

export const StyledDivider = styled(Divider)(() => ({
  marginTop: '1rem',
}));

export const StyledDialogActions = styled(DialogActions)(() => ({
  padding: '1rem',
}));
