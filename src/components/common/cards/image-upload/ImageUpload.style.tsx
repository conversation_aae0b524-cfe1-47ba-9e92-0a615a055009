import { styled } from '@mui/material/styles';
import { Box, Card, Grid, Stack } from '@mui/material';

export const CardWrapper = styled(Card)(({ theme }) => ({
  marginTop: '1rem',
  boxShadow: theme.shadows[4],
}));

export const FormGrid = styled(Grid)(() => ({
  width: '100%',
  height: '100%',
}));

export const UploadBox = styled(Box)(() => ({
  marginBlock: '1rem',
  marginInline: '1.4rem',
}));

export const StackWrapper = styled(Stack)(() => ({
  marginInline: '1.4rem',
  marginBlock: '1rem',
}));
