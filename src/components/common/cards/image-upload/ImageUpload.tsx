import type { Accept } from 'react-dropzone/.';
import type { Gallery } from 'src/shared/services/gallery/gallery.type';

import { useState } from 'react';
import { useFormikContext } from 'formik';

import { Button, Switch, CardHeader, FormControlLabel } from '@mui/material';

import { Upload } from '../../upload';
import { UploadBox, CardWrapper, StackWrapper } from './ImageUpload.style';

export interface ImageUploadProps {
  title: string;
  readMode: boolean;
  accept: Accept;
}

const ImageUpload = ({ title, readMode, accept }: ImageUploadProps) => {
  const { values, errors, touched, setFieldValue } = useFormikContext<Gallery>();
  const [preview, setPreview] = useState(true);

  const handleDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setPreview(true);

      // Mocking the response for now

      const mockResponse = {
        id: `inv-${Date.now()}`,
        fileName: file.name,
        fileUrl: URL.createObjectURL(file),
        uploadedAt: new Date().toISOString(),
      };
      setFieldValue('asset', mockResponse);
    }
  };

  const handleRemove = () => {
    setFieldValue('asset', null);
  };

  return (
    <CardWrapper>
      <CardHeader title={title} />

      <UploadBox>
        <FormControlLabel
          control={<Switch checked={preview} onChange={(e) => setPreview(e.target.checked)} />}
          label="Show Thumbnail"
          sx={{ mb: 3, width: 1, justifyContent: 'flex-end' }}
        />

        <Upload
          multiple={false}
          thumbnail={preview}
          disabled={readMode}
          value={values?.assets?.fileUrl}
          error={Boolean(errors?.assets && touched?.assets)}
          helperText={touched.assets ? (errors?.assets as string) : ''}
          onDrop={handleDrop}
          onDelete={handleRemove}
          accept={accept}
        />
      </UploadBox>

      <StackWrapper direction="row" justifyContent="flex-end">
        <Button variant="outlined" color="inherit">
          Cancel
        </Button>
        <Button variant="contained" color="secondary">
          Save
        </Button>
      </StackWrapper>
    </CardWrapper>
  );
};

export default ImageUpload;
