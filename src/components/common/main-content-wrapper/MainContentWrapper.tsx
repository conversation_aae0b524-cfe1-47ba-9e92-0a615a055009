import React from 'react';

import PageTitle from '../page-title/PageTitle';
import {
  ActionButtons,
  ContentHeader,
  ContentWrapper,
  ContentChildWrapper,
} from './MainContentWrapper.style';

interface MainContentProps {
  children: React.ReactNode;
  pageTitle: string;
  buttons?: JSX.Element | null;
}

const MainContent = ({ children, pageTitle, buttons }: MainContentProps) => (
  <ContentWrapper>
    <ContentHeader>
      <PageTitle title={pageTitle} />
      <ActionButtons>{buttons}</ActionButtons>
    </ContentHeader>

    <ContentChildWrapper>{children}</ContentChildWrapper>
  </ContentWrapper>
);

export default MainContent;
