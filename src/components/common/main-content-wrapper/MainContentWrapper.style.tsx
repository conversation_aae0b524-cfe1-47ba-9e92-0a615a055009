import { Card } from '@mui/material';
import { styled } from '@mui/material/styles';

export const ContentWrapper = styled('div')(({ theme }) => ({
  display: 'flex',
  flex: '1 auto',
  flexDirection: 'column',
  position: 'relative',

  [theme.breakpoints.down('md')]: {
    marginInline: '2rem',
  },
}));

export const ContentHeader = styled(Card)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  position: 'sticky',
  top: 0,
  zIndex: 100,
  boxShadow: 'none',
  paddingBlock: theme.spacing(2),
  backgroundColor: theme.palette.grey[100],
  width: '100%',
  paddingInline: '2rem',
  borderRadius: 0,
}));

export const ActionButtons = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
}));

export const ContentChildWrapper = styled('div')(() => ({
  position: 'relative',
}));
