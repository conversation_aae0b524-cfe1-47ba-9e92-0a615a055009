import { IconButton } from '@mui/material';
import { useTheme } from '@mui/material/styles';

import { useRouter } from 'src/routes/hooks';

import { IconChevronLeft } from 'src/components/common/icons';

import { PageTitleWrapper, StyledTypography } from './PageTitle.style';

interface PageTitleProps {
  title: string;
}

const PageTitle = ({ title }: PageTitleProps) => {
  const theme = useTheme();
  const router = useRouter();

  return (
    <PageTitleWrapper onClick={router.back}>
      <IconButton>
        <IconChevronLeft style={{ width: 24 }} />
      </IconButton>
      <StyledTypography variant="h6" color={theme.palette.grey[600]}>
        {title}
      </StyledTypography>
    </PageTitleWrapper>
  );
};

export default PageTitle;
