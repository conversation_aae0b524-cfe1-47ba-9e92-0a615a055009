import { LinearProgress, CircularProgress } from '@mui/material';

import { LinearProgressWrapper } from './LinearProgressLoader.style';

interface ProgressLoaderProps {
  variant?: 'linear' | 'circular';
  size?: number;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
}

const ProgressLoader = ({
  variant = 'linear',
  size = 40,
  color = 'primary',
}: ProgressLoaderProps) => {
  if (variant === 'circular') {
    return (
      <LinearProgressWrapper>
        <CircularProgress size={size} color={color} />
      </LinearProgressWrapper>
    );
  }

  return (
    <LinearProgressWrapper>
      <LinearProgress color={color} />
    </LinearProgressWrapper>
  );
};

export default ProgressLoader;
