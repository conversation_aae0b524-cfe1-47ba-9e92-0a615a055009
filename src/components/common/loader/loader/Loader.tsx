import type { ColorType } from 'src/theme/core/palette';

import { CircularProgress } from '@mui/material';

import { StyledBackdrop } from './Loader.style';

interface LoaderProps {
  loading: boolean;
  color?: ColorType;
}

const Loader = ({ loading, color = 'primary' }: LoaderProps) => (
  <>
    {loading && (
      <StyledBackdrop open={loading}>
        <CircularProgress color={color} />
      </StyledBackdrop>
    )}
  </>
);

export default Loader;
