interface SvgIconProps extends React.SVGProps<SVGSVGElement> {}

const IconCheck = (props: SvgIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} viewBox="0 0 24 24" {...props}>
    <path
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="m2 12l5.25 5l2.625-3M8 12l5.25 5L22 7m-6 0l-3.5 4"
    />
  </svg>
);

export default IconCheck;
