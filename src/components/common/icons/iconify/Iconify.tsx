import type { IconProps } from '@iconify/react';
import type { BoxProps } from '@mui/material/Box';

import { forwardRef } from 'react';
import { Icon, disableCache } from '@iconify/react';

import { iconifyClasses } from './classes';
import { IconifyWrapper } from './Iconify.style';

export type IconifyProps = BoxProps & IconProps;

export const Iconify = forwardRef<SVGElement, IconifyProps>(
  ({ className, width = 24, sx, ...other }, ref) => (
    <IconifyWrapper
      ssr
      ref={ref}
      width={width}
      component={Icon}
      className={iconifyClasses.root.concat(className ? ` ${className}` : '')}
      {...other}
    />
  )
);

disableCache('local');
