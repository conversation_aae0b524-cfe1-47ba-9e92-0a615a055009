export const Icons = {
  navigation: {
    home: 'ic:round-home',
    profile: 'fa6-solid:user-group',
    logout: 'solar:logout-2-bold-duotone',
  },

  nexMov: {
    contactRequestIcon: 'hugeicons:contact-01',
    quoteRequestIcon: 'stash:invoice',
    instantQuoteIcon: 'mdi:invoice-text-fast-outline',
    careersIcon: 'hugeicons:work',
    paymentRequestIcon: 'fluent:payment-32-regular',
    trackingIcon: 'mdi:package-variant-closed',
    cms: 'solar:gallery-send-bold',
    gallery: 'solar:gallery-send-bold',
    markePlaceIcon: 'mdi:store-24-hour',
  },

  files: {
    pdf: 'eva:file-text-fill',
    image: 'eva:image-fill',
    default: 'eva:file-fill',
    download: 'eva:download-fill',
    view: 'eva:eye-fill',
  },

  password: {
    eyeClose: 'solar:eye-closed-bold',
    eyeOpen: 'solar:eye-bold',
  },

  tracking: {
    customer: 'mdi:account',
    pincode: 'mdi:map-marker-radius',
    content: 'mdi:package-variant',
    service: 'mdi:package-variant-closed-check',
    location: 'mdi:map-marker',
    pricing: 'mdi:currency-usd',
    email: 'mdi:email',
    phone: 'mdi:phone',
    address: 'mdi:home',
    city: 'mdi:city',
    state: 'mdi:map',
    country: 'mdi:earth',
    zipcode: 'mdi:mailbox',
    dimensions: 'mdi:ruler-square',
    weight: 'mdi:weight',
    description: 'mdi:text-box',
    dropoff: 'mdi:store-marker',
    shipping: 'mdi:truck-delivery',
  },

  left_chev_small: 'heroicons-solid:chevron-left',
  right_chev_small: 'heroicons-solid:chevron-right',
  up_chev_small: 'heroicons-solid:chevron-up',
  down_chev_small: 'heroicons-solid:chevron-down',
  copyIcon: 'stash:copy',
};
