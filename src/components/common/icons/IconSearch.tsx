import * as React from 'react';

interface SvgIconProps extends React.SVGProps<SVGSVGElement> {}

const IconSearch = (props: SvgIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="m21 21-3.5-3.5m2.5-6a8.5 8.5 0 1 1-17 0 8.5 8.5 0 0 1 17 0"
    />
  </svg>
);

export default IconSearch;
