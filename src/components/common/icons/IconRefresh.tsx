import * as React from 'react';

interface SvgIconProps extends React.SVGProps<SVGSVGElement> {}

const IconRefresh = (props: SvgIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M22 10s-2.005-2.732-3.634-4.362a9 9 0 1 0 2.282 8.862M22 10V4m0 6h-6"
    />
  </svg>
);

export default IconRefresh;
