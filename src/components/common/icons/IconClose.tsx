import * as React from 'react';

interface SvgIconProps extends React.SVGProps<SVGSVGElement> {}

const IconX = (props: SvgIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M18 6 6 18M6 6l12 12"
    />
  </svg>
);

export default IconX;
