import type { SearchResponse } from 'src/shared/types/searchResponse';

import React, { useMemo, useState, useEffect, useCallback } from 'react';

import { <PERSON><PERSON>, debounce, TextField, Autocomplete } from '@mui/material';

export interface IAutoCompleteSearchProps<T> {
  relationshipTypeId: number;
  onChange: (value: T) => void;
  searchFunction: (searchKey: string, relationshipTypeId: number) => Promise<SearchResponse<T>>;
  getOptionLabel: (option: T) => string;
  renderOption: (props: React.HTMLAttributes<HTMLLIElement>, option: T) => React.ReactNode;
}

const AutoCompleteSearch = <T,>({
  onChange,
  relationshipTypeId,
  searchFunction,
  getOptionLabel,
  renderOption,
}: IAutoCompleteSearchProps<T>) => {
  const [value, setValue] = useState<T | null>(null);
  const [inputValue, setInputValue] = useState<string>('');
  const [options, setOptions] = useState<readonly T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const loadOptions = useCallback(
    (searchKey: string, callback: (results?: readonly T[]) => void) => {
      setLoading(true);
      searchFunction(searchKey, relationshipTypeId)
        .then((response) => callback(response.searchResult))
        .catch((error) => console.error(error))
        .finally(() => setLoading(false));
    },
    [relationshipTypeId, searchFunction]
  );

  const fetch = useMemo(
    () =>
      debounce((input: string, callback: (results?: readonly T[]) => void) => {
        loadOptions(input, callback);
      }, 400),
    [loadOptions]
  );

  useEffect(() => {
    let active = true;

    if (inputValue === '') {
      setOptions(value ? [value] : []);
      return undefined;
    }

    fetch(inputValue, (results) => {
      if (active) {
        setOptions(results ? [...(value ? [value] : []), ...results] : []);
      }
    });

    return () => {
      active = false;
    };
  }, [value, inputValue, fetch]);

  return (
    <>
      {!value && (
        <Alert sx={{ mb: 2 }} variant="outlined" severity="error">
          Please select an option!
        </Alert>
      )}
      <Autocomplete
        fullWidth
        size="small"
        getOptionLabel={getOptionLabel}
        filterOptions={(x) => x}
        options={options}
        includeInputInList
        filterSelectedOptions
        value={value || null}
        noOptionsText="No options"
        onChange={(event: React.SyntheticEvent, newValue: T | null) => {
          setValue(newValue || null);
          onChange(newValue as T);
        }}
        loading={loading}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField {...params} label="Search" placeholder="Enter details" fullWidth />
        )}
        renderOption={renderOption}
      />
    </>
  );
};

export default AutoCompleteSearch;
