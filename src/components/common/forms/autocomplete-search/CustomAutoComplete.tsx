/* eslint-disable @typescript-eslint/no-shadow */
import React, { useState, useEffect } from 'react';

import { TextField, FormControl, Autocomplete, CircularProgress } from '@mui/material';

import { useDebounce } from 'src/hooks/use-debounce';

import { SelectLabel } from '../select/Select.style';

interface CustomAutoCompleteProps<T> {
  label?: string;
  coreLabel?: string;
  placeholder?: string;
  onSearch: (query: string) => Promise<T[]>;
  value: T | null;
  onChange: (event: React.ChangeEvent<{}>, value: T | null) => void;
  getOptionLabel: (option: T) => string;
  isOptionEqualToValue?: (option: T, value: T) => boolean;
  error?: boolean;
  disabled: boolean;
  helperText?: string;
  id: string;
  name: string;
  required?: boolean;
  getOptionKey?: (option: T) => string | number;
}

export default function CustomAutoComplete<T>({
  id,
  name,
  label,
  coreLabel,
  placeholder,
  onSearch,
  value,
  disabled,
  onChange,
  isOptionEqualToValue = (option, value) => option === value,
  getOptionLabel,
  getOptionKey,
  error,
  helperText,
  required = true,
}: CustomAutoCompleteProps<T>) {
  const [inputValue, setInputValue] = useState('');
  const [options, setOptions] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);

  const debouncedValue = useDebounce(inputValue, 500);

  useEffect(() => {
    const fetchOptions = async () => {
      if (debouncedValue.trim() === '' && !value) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const searchQuery = debouncedValue || (value ? getOptionLabel(value) : '');
        const results = await onSearch(searchQuery);
        setOptions(results);
      } catch (error) {
        console.error('Failed to fetch options:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOptions();
  }, [debouncedValue, onSearch, value, getOptionLabel]);

  return (
    <FormControl fullWidth error={error}>
      {label && (
        <SelectLabel htmlFor={id} data-required={required ? ' *' : ''}>
          {label}
        </SelectLabel>
      )}
      <Autocomplete
        value={value}
        options={options}
        getOptionLabel={getOptionLabel}
        getOptionKey={getOptionKey}
        noOptionsText="Type to search..."
        onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
        onChange={onChange}
        loading={loading}
        isOptionEqualToValue={isOptionEqualToValue}
        disabled={disabled}
        renderInput={(params) => (
          <TextField
            {...params}
            size="small"
            name={name}
            id={id}
            label={coreLabel}
            placeholder={placeholder}
            variant="outlined"
            error={error}
            helperText={helperText}
            required={required}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {loading ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
      />
    </FormControl>
  );
}
