import { Box, styled, FormLabel, FormControl } from '@mui/material';

export const StyledFormControl = styled(FormControl)(({ theme }) => ({
  width: '100%',
}));

export const InputLabel = styled(FormLabel)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  display: 'block',
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightMedium,
  fontSize: '0.75rem',
  '&::after': {
    content: 'attr(data-required)',
    color: theme.palette.error.main,
  },
}));

export const EditorContainer = styled(Box)<{ error?: boolean; disabled?: boolean }>(
  ({ theme, error, disabled }) => ({
    border: `1px solid ${error ? theme.palette.error.main : theme.palette.grey[300]}`,
    borderRadius: theme.shape.borderRadius,
    backgroundColor: disabled
      ? theme.palette.action.disabledBackground
      : theme.palette.background.paper,
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    '&:hover': {
      borderColor: disabled ? theme.palette.grey[300] : theme.palette.grey[400],
    },
    '&:focus-within': {
      borderColor: error ? theme.palette.error.main : theme.palette.primary.main,
      boxShadow: `0 0 0 2px ${error ? theme.palette.error.main : theme.palette.primary.main}20`,
    },
    '& .ql-container': {
      fontSize: '0.875rem',
    },
    '& .ql-editor': {
      minHeight: '120px',
      padding: theme.spacing(1.5),
    },
    '& .ql-toolbar': {
      borderBottom: `1px solid ${theme.palette.grey[200]}`,
      padding: theme.spacing(1),
      backgroundColor: theme.palette.grey[50],
      borderTopLeftRadius: theme.shape.borderRadius,
      borderTopRightRadius: theme.shape.borderRadius,
    },
  })
);

export const HelperText = styled('div')<{ error?: boolean }>(({ theme, error }) => ({
  marginTop: theme.spacing(0.5),
  fontSize: theme.typography.pxToRem(12),
  color: error ? theme.palette.error.main : theme.palette.text.secondary,
}));
