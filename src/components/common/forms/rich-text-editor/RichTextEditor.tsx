import 'react-quill/dist/quill.snow.css';

import ReactQuill from 'react-quill';
import { useRef, forwardRef, useImperativeHandle } from 'react';

import { InputLabel, HelperText, EditorContainer, StyledFormControl } from './RichTextEditor.style';

import type { RichTextEditorRef, RichTextEditorProps } from './RichTextEditor.types';

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(
  (
    {
      id,
      name,
      label,
      placeholder = 'Start typing...',
      error = false,
      disabled = false,
      helperText,
      value,
      required = false,
      minHeight = 120,
      maxHeight = 400,
      handleChange,
      handleBlur,
    },
    ref
  ) => {
    const quillRef = useRef<ReactQuill>(null);

    const modules = {
      toolbar: [
        [{ header: [1, 2, 3, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['blockquote', 'code-block'],
        ['clean'],
      ],
    };

    const formats = [
      'header',
      'bold',
      'italic',
      'underline',
      'strike',
      'list',
      'bullet',
      'blockquote',
      'code-block',
    ];

    useImperativeHandle(ref, () => ({
      focus: () => quillRef.current?.focus(),
      blur: () => {
        if (quillRef.current) {
          const quill = quillRef.current.getEditor();
          quill.blur();
        }
      },
      getHTML: () => {
        if (quillRef.current) {
          const quill = quillRef.current.getEditor();
          return quill.root.innerHTML;
        }
        return '';
      },
      getText: () => {
        if (quillRef.current) {
          const quill = quillRef.current.getEditor();
          return quill.getText();
        }
        return '';
      },
      isEmpty: () => {
        if (quillRef.current) {
          const quill = quillRef.current.getEditor();
          return quill.getLength() === 1;
        }
        return true;
      },
    }));

    return (
      <StyledFormControl error={error}>
        {label && (
          <InputLabel htmlFor={id} data-required={required ? ' *' : ''}>
            {label}
          </InputLabel>
        )}
        <EditorContainer error={error} disabled={disabled}>
          <ReactQuill
            ref={quillRef}
            theme="snow"
            value={value}
            onChange={handleChange}
            onBlur={
              handleBlur ? (_prevSel, _source, editor) => handleBlur(editor.getHTML()) : undefined
            }
            placeholder={placeholder}
            modules={modules}
            formats={formats}
            style={{ minHeight, maxHeight }}
            readOnly={disabled}
          />
        </EditorContainer>
        {helperText && <HelperText error={error}>{helperText}</HelperText>}
      </StyledFormControl>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
