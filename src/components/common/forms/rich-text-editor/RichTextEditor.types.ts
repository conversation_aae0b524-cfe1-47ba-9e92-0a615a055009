export interface RichTextEditorProps {
  id: string;
  name: string;
  label?: string;
  placeholder?: string;
  error?: boolean;
  disabled?: boolean;
  helperText?: string;
  value: string;
  required?: boolean;
  minHeight?: number;
  maxHeight?: number;
  handleChange: (value: string) => void;
  handleBlur?: (value: string) => void;
}

export interface RichTextEditorRef {
  focus: () => void;
  blur: () => void;
  getHTML: () => string;
  getText: () => string;
  isEmpty: () => boolean;
}
