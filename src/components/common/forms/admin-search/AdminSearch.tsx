import type { AdminUser } from 'src/shared/services/admin/users/users.types';

import { memo } from 'react';

import CustomAutoComplete from '../autocomplete-search/CustomAutoComplete';

interface AdminSearchProps {
  readMode: boolean;
  selectedAdmin: AdminUser | null;
  onAdminChange: (admin: AdminUser | null) => void;
  onSearch: (query: string) => Promise<AdminUser[]>;
  error?: boolean;
  helperText?: string;
}

const AdminSearch = ({
  readMode,
  selectedAdmin,
  onAdminChange,
  onSearch,
  error,
  helperText,
}: AdminSearchProps) => (
  <CustomAutoComplete
    id="assignedAdmin"
    name="assignedAdmin"
    label="Assign Admin"
    coreLabel=""
    placeholder="Search admin by name or email"
    onSearch={onSearch}
    value={selectedAdmin}
    onChange={(_, value) => onAdminChange(value)}
    getOptionLabel={(option) => `${option?.fullName}`}
    isOptionEqualToValue={(option, value) => option?.id === value?.id}
    error={error}
    helperText={helperText}
    disabled={readMode}
  />
);

export default memo(AdminSearch);
