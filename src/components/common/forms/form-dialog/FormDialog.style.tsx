import { Form } from 'formik';

import { LoadingButton } from '@mui/lab';
import { Dialog, styled, DialogTitle, DialogActions, DialogContent } from '@mui/material';

export const RootDialog = styled(Dialog)(() => ({}));

export const FormDialogTitle = styled(DialogTitle)(({ theme }) => ({
  background: theme.palette.grey[50],
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingBlock: theme.spacing(2.5),
  paddingInline: theme.spacing(4),
  marginBottom: theme.spacing(1),
  fontWeight: theme.typography.fontWeightSemiBold,
}));

export const FormDialogContent = styled(DialogContent)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  width: '100%',
  fontSize: theme.typography.body2.fontSize,
  fontWeight: theme.typography.fontWeightSemiBold,
  marginInline: 'auto',
  paddingInline: theme.spacing(4),
  marginBottom: theme.spacing(2),
}));

export const StyledForm = styled(Form)(() => ({}));

export const FormDialogActions = styled(DialogActions)(({ theme }) => ({
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  borderTop: `dashed 1px ${theme.palette.grey[300]}`,
}));

export const FormLoadingButton = styled(LoadingButton)(({ theme }) => ({
  ':disabled': {
    backgroundColor: theme.palette.grey[200],
    color: theme.palette.grey[500],
  },
}));

export const NewEditActionWrapper = styled('div')(() => ({
  display: 'flex',
  columnGap: 4,
}));

export const HeaderActions = styled('div')(() => ({}));
