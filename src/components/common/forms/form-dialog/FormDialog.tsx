import type { Breakpoint } from '@mui/material';

import { useFormikContext } from 'formik';

import { Button, IconButton } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { IconClose } from 'src/components/common/icons';

import { ConfirmDialog } from '../../confirm-dialog/ConfirmDialog';
import {
  RootDialog,
  HeaderActions,
  FormDialogTitle,
  FormDialogActions,
  FormDialogContent,
  FormLoadingButton,
  NewEditActionWrapper,
} from './FormDialog.style';

interface FormDialogProps {
  title: React.ReactNode;
  content: React.ReactNode;
  maxWidth?: Breakpoint;
  open: boolean;
  readMode: boolean;
  isNew: boolean;
  formLoading: boolean;
  name: string;
  disabledDelete?: boolean;
  actionText?: string;
  onClose: () => void;
  handleCancel: () => void;
  handleEditAction: () => void;
  handleDeleteAction?: () => void;
}

const DELETE_DIALOG_TITLE = 'Delete';
const DELETE_DIALOG_CONTENT = (dname: string) =>
  `Are you sure you want to delete the entry "${dname}"? This cannot be undone.`;

const FormDialog = ({
  title,
  content,
  open,
  maxWidth = 'sm',
  readMode,
  isNew,
  formLoading,
  name,
  disabledDelete = false,
  onClose,
  handleCancel,
  handleEditAction,
  handleDeleteAction,
  actionText,
  ...other
}: FormDialogProps) => {
  const confirmDel = useBoolean();
  const { resetForm, dirty, handleSubmit } = useFormikContext<any>();

  const handleDelete = () => {
    if (handleDeleteAction) {
      handleDeleteAction();
    }
    confirmDel.onFalse();
    onClose();
  };

  const handleCancelAction = () => {
    resetForm();
    onClose();
    if (isNew) {
      handleCancel();
    }
  };

  const handleFormSubmit = () => {
    handleSubmit();
  };

  return (
    <>
      <RootDialog fullWidth maxWidth={maxWidth} open={open} onClose={onClose} {...other}>
        {/* Header */}
        <FormDialogTitle>
          {title}
          <HeaderActions>
            <IconButton onClick={onClose} aria-label="Close dialog">
              <IconClose />
            </IconButton>
          </HeaderActions>
        </FormDialogTitle>

        {/* Form Content */}
        <FormDialogContent>{content}</FormDialogContent>

        {/* Actions */}
        <FormDialogActions>
          <NewEditActionWrapper>
            {!isNew && !disabledDelete && handleDeleteAction && (
              <FormLoadingButton
                variant="text"
                color="error"
                onClick={confirmDel.onTrue}
                disabled={formLoading}
              >
                Delete
              </FormLoadingButton>
            )}
          </NewEditActionWrapper>

          <NewEditActionWrapper>
            {readMode ? (
              <Button variant="contained" onClick={handleEditAction}>
                Edit
              </Button>
            ) : (
              <>
                <Button variant="outlined" color="inherit" onClick={handleCancelAction}>
                  Cancel
                </Button>
                <FormLoadingButton
                  type="submit"
                  variant="contained"
                  color="primary"
                  loading={formLoading}
                  disabled={formLoading || !dirty}
                  role="progressbar"
                  onClick={handleFormSubmit}
                >
                  {actionText || 'Save'}
                </FormLoadingButton>
              </>
            )}
          </NewEditActionWrapper>
        </FormDialogActions>
      </RootDialog>

      {/* Delete Confirmation Dialog (Only shows if delete is enabled) */}
      {!disabledDelete && handleDeleteAction && (
        <ConfirmDialog
          open={confirmDel.value}
          onClose={confirmDel.onFalse}
          title={DELETE_DIALOG_TITLE}
          content={DELETE_DIALOG_CONTENT(name)}
          action={
            <Button variant="contained" color="error" onClick={handleDelete} disabled={formLoading}>
              Delete
            </Button>
          }
        />
      )}
    </>
  );
};

export default FormDialog;
