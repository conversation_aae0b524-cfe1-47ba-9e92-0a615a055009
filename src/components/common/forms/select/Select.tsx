import type { TextFieldVariants } from '@mui/material';
import type { ddValue } from 'src/shared/types/ddValue';

import { FormControl } from '@mui/material';

import { ArrowDownIcon } from 'src/theme/core/components/select';

import { SelectLabel, StyledSelectItem, StyledSelectField } from './Select.style';

import type { TInputChangeEvent } from '../types/event.types';

interface CustomSelectProps {
  label?: string;
  coreLabel?: string;
  placeholder?: string;
  variant?: TextFieldVariants;
  options: ddValue[];
  name: string;
  id: string;
  value: string;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium';
  handleChange: (event: TInputChangeEvent) => void;
  handleBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  IconComponent?: React.ElementType;
}

const CustomSelect = ({
  label,
  coreLabel,
  options,
  variant,
  name,
  id,
  value,
  error,
  helperText,
  disabled,
  placeholder,
  size = 'small',
  required = true,
  handleChange,
  handleBlur,
  IconComponent = ArrowDownIcon,
}: CustomSelectProps) => (
  <FormControl fullWidth error={error}>
    {label && (
      <SelectLabel htmlFor={id} data-required={required ? ' *' : ''}>
        {label}
      </SelectLabel>
    )}
    <StyledSelectField
      variant={variant}
      id={id}
      name={name}
      size={size}
      label={coreLabel}
      helperText={helperText}
      error={error}
      disabled={disabled}
      aria-readonly={disabled}
      aria-required={required}
      SelectProps={{
        IconComponent,
      }}
      value={value ?? ''}
      onChange={handleChange}
      onBlur={handleBlur}
      required={required}
      placeholder={placeholder}
      fullWidth
      select
    >
      {options?.length === 0 ? (
        <StyledSelectItem disabled value="" aria-valuetext="No options available">
          No options available
        </StyledSelectItem>
      ) : (
        options?.map((item) => (
          <StyledSelectItem key={item?.id} value={item?.id} aria-valuetext={item?.name}>
            {item?.name}
          </StyledSelectItem>
        ))
      )}
    </StyledSelectField>
  </FormControl>
);

export default CustomSelect;
