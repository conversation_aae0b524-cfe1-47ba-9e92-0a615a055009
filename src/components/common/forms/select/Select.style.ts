import { styled, MenuItem, TextField, FormLabel } from '@mui/material';

export const StyledSelectField = styled(TextField)(() => ({
  width: '100%',
  '& input[type=number]': {
    MozAppearance: 'textfield',
  },
  '& input[type=number]::-webkit-outer-spin-button': {
    WebkitAppearance: 'none',
    margin: 0,
  },
  '& input[type=number]::-webkit-inner-spin-button': {
    WebkitAppearance: 'none',
    margin: 0,
  },
}));

export const StyledSelectItem = styled(MenuItem)(() => ({
  textTransform: 'capitalize',
}));

export const SelectLabel = styled(FormLabel)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  display: 'block',
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightMedium,
  fontSize: '0.75rem',
  '&::after': {
    content: 'attr(data-required)',
    color: theme.palette.error.main,
  },
}));
