import type { TextFieldProps } from '@mui/material';

import { InputAdornment } from '@mui/material';

import { IconSearch } from 'src/components/common/icons';

import { StyledSearchInput } from './SearchBar.style';

interface SearchbarProps {}

type TInputProps = TextFieldProps & SearchbarProps;

const SearchBar = ({ ...props }: TInputProps) => (
  <StyledSearchInput
    {...props}
    size="small"
    InputProps={{
      startAdornment: (
        <InputAdornment position="start">
          <IconSearch style={{ width: 18 }} />
        </InputAdornment>
      ),
    }}
  />
);

export default SearchBar;
