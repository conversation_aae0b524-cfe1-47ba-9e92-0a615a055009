import type { TextFieldVariants } from '@mui/material';

import { FormControl, InputAdornment } from '@mui/material';

import { InputLabel, StyledTextInput } from './CustomTextField.style';

interface CustomTextFieldProps {
  id: string;
  name: string;
  label?: string;
  placeholder?: string;
  error?: boolean;
  disabled?: boolean;
  helperText?: string;
  variant: TextFieldVariants;
  type: 'text' | 'password' | 'email' | 'number' | 'date';
  size?: 'small' | 'medium';
  endAdornment?: JSX.Element;
  startAdornment?: JSX.Element;
  value: string | number | undefined;
  maxLength?: number;
  isUppercase?: boolean;
  multiline?: boolean;
  minRows?: number;
  maxRows?: number;
  required?: boolean;
  handleChange: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleBlur: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const CustomTextField = ({
  name,
  id,
  label,
  placeholder,
  value,
  type,
  variant,
  disabled,
  error,
  helperText,
  endAdornment,
  startAdornment,
  maxLength,
  size = 'small',
  isUppercase = false,
  multiline = false,
  minRows = 3,
  maxRows = 6,
  required = false,
  handleChange,
  handleBlur,
}: CustomTextFieldProps) => {
  const inputChangeHandler = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (isUppercase) e.target.value = e.target.value.toUpperCase();
    if (type === 'number' && maxLength && maxLength > 0) {
      if (e.target.value.length <= maxLength) handleChange(e);
    } else {
      handleChange(e);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (type === 'number') {
      if (e.key === 'e' || e.key === '+' || e.key === '-' || e.key === '.') {
        e.preventDefault();
      }
    }
  };

  return (
    <FormControl fullWidth error={error}>
      {label && (
        <InputLabel htmlFor={id} data-required={required ? ' *' : ''}>
          {label}
        </InputLabel>
      )}
      <StyledTextInput
        variant={variant}
        size={size}
        name={name}
        id={id}
        placeholder={placeholder}
        value={isUppercase ? value?.toString().toUpperCase() : value}
        type={type}
        onChange={inputChangeHandler}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        error={error}
        helperText={helperText}
        disabled={disabled}
        multiline={multiline}
        minRows={multiline ? minRows : undefined}
        maxRows={multiline ? maxRows : undefined}
        InputLabelProps={{ shrink: true }}
        InputProps={{
          endAdornment: endAdornment && (
            <InputAdornment position="end">{endAdornment}</InputAdornment>
          ),
          startAdornment: startAdornment && (
            <InputAdornment position="start">{startAdornment}</InputAdornment>
          ),
        }}
        inputProps={{
          maxLength,
        }}
        fullWidth
      />
    </FormControl>
  );
};

export default CustomTextField;
