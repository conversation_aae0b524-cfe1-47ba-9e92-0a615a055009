import { styled, TextField, FormLabel } from '@mui/material';

export const StyledTextInput = styled(TextField)(({ theme }) => ({
  '& .MuiInputBase-input::placeholder': {
    color: theme.palette.text.disabled,
  },
  '& .MuiInputLabel-root': {
    display: 'none',
  },
}));

export const InputLabel = styled(FormLabel)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  display: 'block',
  color: theme.palette.grey[800],
  fontWeight: theme.typography.fontWeightMedium,
  fontSize: '0.75rem',
  '&::after': {
    content: 'attr(data-required)',
    color: theme.palette.error.main,
  },
}));
