import { useFormikContext } from 'formik';
import React, { useRef, useState, useEffect, useCallback } from 'react';

import { <PERSON><PERSON>, Typo<PERSON>, FormControl, FormHelperText, ClickAwayListener } from '@mui/material';

import { Iconify } from '../../icons/iconify';
import {
  NoResultsItem,
  PredictionIcon,
  PredictionItem,
  StyledTextField,
  PredictionsPaper,
  AutocompleteWrapper,
} from './GoogleAddressAutocomplete.style';

declare global {
  interface Window {
    google: any;
  }
}

interface Place {
  id: string;
  name: string;
  fullAddress?: string;
  placeId?: string;
  location?: {
    lat?: number;
    lng?: number;
  };
}

interface GoogleAddressAutocompleteProps {
  name: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  helperText?: string;
}

const GoogleAddressAutocomplete: React.FC<GoogleAddressAutocompleteProps> = ({
  name,
  placeholder = 'Start typing an address...',
  required = false,
  disabled = false,
  helperText,
}) => {
  const { values, setFieldValue, errors, touched, handleBlur } = useFormikContext<any>();
  const [inputValue, setInputValue] = useState('');
  const [predictions, setPredictions] = useState<Place[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [autocompleteService, setAutocompleteService] = useState<any>(null);
  const [placesService, setPlacesService] = useState<any>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const mapRef = useRef<HTMLDivElement | null>(null);

  const fieldValue = values[name];

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY || ''}&libraries=places`;
    script.async = true;
    script.defer = true;
    script.onload = () => {
      setIsLoaded(true);
    };
    document.head.appendChild(script);

    // eslint-disable-next-line consistent-return
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  useEffect(() => {
    if (isLoaded && window.google && window.google.maps) {
      setAutocompleteService(new window.google.maps.places.AutocompleteService());

      if (!mapRef.current) {
        const mapDiv = document.createElement('div');
        mapDiv.style.display = 'none';
        document.body.appendChild(mapDiv);
        mapRef.current = mapDiv;
      }

      setPlacesService(new window.google.maps.places.PlacesService(mapRef.current));
    }
  }, [isLoaded]);

  useEffect(() => {
    if (fieldValue) {
      if (typeof fieldValue === 'string') {
        setInputValue(fieldValue);
      } else if (typeof fieldValue === 'object' && fieldValue.name) {
        setInputValue(fieldValue.name);
      }
    } else {
      setInputValue('');
    }
  }, [fieldValue]);

  const fetchPredictions = useCallback(
    (searchValue: string) => {
      if (!searchValue || !autocompleteService) {
        setPredictions([]);
        return;
      }

      autocompleteService.getPlacePredictions(
        {
          input: searchValue,
          componentRestrictions: { country: 'in' },
        },
        (results: any, status: any) => {
          if (status === window.google.maps.places.PlacesServiceStatus.OK && results) {
            const formattedResults: Place[] = results.map((prediction: any) => ({
              id: prediction.place_id,
              name: prediction.description,
              placeId: prediction.place_id,
              fullAddress: prediction.structured_formatting?.main_text,
            }));
            setPredictions(formattedResults);
          } else {
            setPredictions([]);
          }
        }
      );
    },
    [autocompleteService]
  );

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setInputValue(newValue);
      setIsOpen(true);

      if (newValue === '') {
        setFieldValue(name, '', true);
      } else {
        fetchPredictions(newValue);
      }
    },
    [fetchPredictions, name, setFieldValue]
  );

  const handleFocus = useCallback(() => {
    setIsOpen(true);
    if (inputValue) {
      fetchPredictions(inputValue);
    }
  }, [inputValue, fetchPredictions]);

  const handleSelect = useCallback(
    (place: Place) => {
      setInputValue(place.name);

      if (placesService && place.placeId) {
        placesService.getDetails(
          {
            placeId: place.placeId,
            fields: ['address_component', 'formatted_address', 'geometry', 'name', 'place_id'],
          },
          (placeResult: any, status: any) => {
            if (status === window.google.maps.places.PlacesServiceStatus.OK && placeResult) {
              setFieldValue(
                name,
                {
                  ...placeResult,
                  id: place.id,
                  name: place.name,
                  fullAddress: placeResult.formatted_address,
                  placeId: place.placeId,
                  location: {
                    lat: placeResult.geometry?.location?.lat(),
                    lng: placeResult.geometry?.location?.lng(),
                  },
                },
                true
              );
            } else {
              setFieldValue(name, place, true);
            }
          }
        );
      } else {
        setFieldValue(name, place, true);
      }

      setIsOpen(false);
    },
    [name, placesService, setFieldValue]
  );

  const handleClickAway = useCallback(() => {
    setIsOpen(false);
  }, []);

  const hasError = Boolean(errors[name] && touched[name]);

  const errorMessage =
    typeof errors[name] === 'string'
      ? errors[name]
      : Array.isArray(errors[name]) &&
          (errors[name] as unknown[]).every((e) => typeof e === 'string')
        ? (errors[name] as string[]).join(', ')
        : typeof helperText === 'string'
          ? helperText
          : '';

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <AutocompleteWrapper>
        <FormControl fullWidth error={hasError}>
          {/* {label && (
            <InputLabel htmlFor={`search-${name}`} data-required={required ? ' *' : ''}>
              {label}
            </InputLabel>
          )} */}
          <StyledTextField
            id={name}
            name={name}
            placeholder={placeholder}
            value={inputValue}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            inputRef={inputRef}
            disabled={disabled || !isLoaded}
            error={hasError}
            required={required}
            variant="outlined"
            fullWidth
            autoComplete="off"
            size="small"
          />
          {hasError && <FormHelperText>{String(errorMessage || '')}</FormHelperText>}
        </FormControl>

        {isOpen && (
          <Popper
            open
            anchorEl={inputRef.current}
            placement="bottom-start"
            style={{ width: inputRef.current?.clientWidth, zIndex: 1300 }}
          >
            <PredictionsPaper elevation={3}>
              {predictions.length > 0 ? (
                predictions.map((place) => (
                  <PredictionItem key={place.id} onClick={() => handleSelect(place)}>
                    <PredictionIcon>
                      <Iconify icon="mdi:map-marker" width={20} height={20} />
                    </PredictionIcon>
                    <Typography variant="body2">{place.name}</Typography>
                  </PredictionItem>
                ))
              ) : (
                <NoResultsItem>
                  <Typography variant="body2">No results found</Typography>
                </NoResultsItem>
              )}
            </PredictionsPaper>
          </Popper>
        )}
      </AutocompleteWrapper>
    </ClickAwayListener>
  );
};

export default GoogleAddressAutocomplete;
