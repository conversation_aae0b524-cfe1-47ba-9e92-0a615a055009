import { styled } from '@mui/material/styles';
import { Box, Paper, TextField } from '@mui/material';

export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.primary.light,
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.primary.main,
      borderWidth: 1,
      boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
    },
  },
  '& .MuiInputLabel-root': {
    '&.Mui-focused': {
      color: theme.palette.primary.main,
    },
  },
}));

export const AutocompleteWrapper = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
}));

export const PredictionsPaper = styled(Paper)(({ theme }) => ({
  maxHeight: 300,
  overflow: 'auto',
  marginTop: theme.spacing(0.5),
  zIndex: 1300,
  boxShadow: theme.shadows[3],
  border: `1px solid ${theme.palette.divider}`,
}));

export const PredictionItem = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.5, 2),
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  borderBottom: `1px solid ${theme.palette.divider}`,
  transition: theme.transitions.create(['background-color']),
  '&:hover': {
    backgroundColor: `${theme.palette.primary.main}10`,
  },
  '&:last-child': {
    borderBottom: 'none',
  },
}));

export const PredictionIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(1.5),
  color: theme.palette.primary.main,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 32,
  height: 32,
  borderRadius: '50%',
  backgroundColor: `${theme.palette.primary.main}10`,
}));

export const NoResultsItem = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  color: theme.palette.text.secondary,
  textAlign: 'center',
  fontStyle: 'italic',
}));
