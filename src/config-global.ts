import { paths } from './routes/paths';
import packageJson from '../package.json';

export type ConfigValue = {
  site: {
    name: string;
    serverUrl: string;
    assetsDir: string;
    basePath: string;
    version: string;
  };
  auth: {
    method: 'jwt' | 'amplify' | 'firebase' | 'supabase' | 'auth0';
    skip: boolean;
    redirectPath: string;
  };
};

export const CONFIG: ConfigValue = {
  site: {
    name: 'Halef International',
    serverUrl: import.meta.env.VITE_SERVER_URL ?? '',
    assetsDir: import.meta.env.VITE_ASSET_URL ?? '',
    basePath: import.meta.env.VITE_BASE_PATH ?? '',
    version: packageJson.version,
  },

  auth: {
    method: 'jwt',
    skip: false,
    redirectPath: paths.home,
  },
};
