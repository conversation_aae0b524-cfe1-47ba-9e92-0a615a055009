# MUI Button Component Documentation

This document provides an overview of the custom MUI Button component, including its styling, variants, color usage, and other properties.

## Overview

The `MuiButton` component is a customized Material-UI button that aligns with the provided design system. It supports multiple variants (`contained`, `outlined`, `text`, `soft`), sizes (`small`, `medium`, `large`), and colors (`primary`, `secondary`, `info`, `success`, `warning`, `error`, `inherit`). The component is designed to have a consistent look and feel, with pill-shaped buttons, smooth transitions, and accessible states.

## Features

- **Variants**: `contained`, `outlined`, `text`, and a custom `soft` variant.
- **Sizes**: `small` (30px height), `medium` (36px height), `large` (48px height).
- **Colors**: Supports `primary`, `secondary`, `info`, `success`, `warning`, `error`, and `inherit`.
- **Border Radius**: All buttons have a `50px` border radius for a pill-shaped appearance.
- **Typography**: Text is not uppercase (`textTransform: 'none'`), with a `fontWeight` of 600 for a medium-bold look.
- **Transitions**: Smooth transitions for background color, box-shadow, and border color changes.

## Color Usage

The component uses the following color palette from the design system:

- **Primary**:
  - `main`: `#6941C6` (default background for `contained`, border/text for `outlined` and `text`)
  - `lighter`: `#EDE2FE` (background for `soft` variant)
  - `dark`: `#7E22CE` (hover background for `contained`)
  - `contrastText`: `#FFFFFF` (text color for `contained`)
- **Secondary**:
  - `main`: `#647ACF`
  - `lighter`: `#EDE9FE`
  - `dark`: `#4361A0`
  - `contrastText`: `#FFFFFF`
- **Info**:
  - `main`: `#36BDF1`
  - `lighter`: `#E0F2FE`
  - `dark`: `#0284C7`
  - `contrastText`: `#FFFFFF`
- **Success**:
  - `main`: `#22C55E`
  - `lighter`: `#DCFCE7`
  - `dark`: `#166534`
  - `contrastText`: `#FFFFFF`
- **Warning**:
  - `main`: `#F59E0B`
  - `lighter`: `#FEF3C7`
  - `dark`: `#92400E`
  - `contrastText`: `#1C252E`
- **Error**:
  - `main`: `#EF4444`
  - `lighter`: `#FEE4E2`
  - `dark`: `#991B1B`
  - `contrastText`: `#FFFFFF`
- **Grey** (used for disabled states and `inherit` color):
  - `100`: `#F9FAFB` (default `soft` background)
  - `200`: `#F4F6F8` (hover background for `soft`, disabled background for `soft`)
  - `300`: `#DFE3E8` (disabled background for `contained` and `outlined`)
  - `500`: `#919EAB` (disabled text color)
  - `800`: `#1C252E` (default text for `inherit` color)
  - `900`: `#141A21` (hover background for `inherit` in `contained`)

### Default Color

The default color for the button is set to `primary` in the `defaultProps`. This means that if no `color` prop is specified, the button will use the `primary` color palette, with the `main` color (`#6941C6`) applied as the background for `contained` buttons, or as the border/text color for `outlined` and `text` buttons.

**Previous Issue with Black Color**: In the earlier version, the default color was set to `inherit`, which used `grey[800]` (`#1C252E`) for the text and background in some cases. This might have appeared as black or a very dark shade. This has been updated to use `primary` as the default color, ensuring the `main` color (`#6941C6`) is used instead.

### Disabled State

- **Background**: `grey[300]` (`#DFE3E8`) for `contained` and `outlined` variants, `grey[200]` (`#F4F6F8`) for `soft`.
- **Text/Border**: `grey[500]` (`#919EAB`) for all variants.

## Variants

### Contained

- **Background**: Uses the `main` color of the specified color (e.g., `primary.main`).
- **Text**: Uses the `contrastText` color (e.g., `primary.contrastText`).
- **Hover**: Changes the background to the `dark` shade (e.g., `primary.dark`).
- **Disabled**: Background is `grey[300]`, text is `grey[500]`.

### Outlined

- **Border**: Uses the `main` color with a 1px width.
- **Text**: Matches the `main` color.
- **Background**: Transparent.
- **Hover**: Adds a background of `alpha(lighter, 0.5)` (e.g., `alpha(primary.lighter, 0.5)`), border changes to the `dark` shade.
- **Disabled**: Border and text use `grey[500]`, background

# MUI Chip Component Documentation

This document provides an overview of the custom MUI Chip component, including its styling, variants, color usage, and other properties.

## Overview

The `MuiChip` component is a customized Material-UI chip that aligns with the provided design system. It supports multiple variants (`filled`, `outlined`, `soft`), sizes (`small`, `medium`), and colors (`primary`, `secondary`, `info`, `success`, `warning`, `error`, `default`). The component is designed to have a consistent look and feel, with rounded chips, smooth transitions, and accessible states.

## Features

- **Variants**: `filled`, `outlined`, and a custom `soft` variant.
- **Sizes**: `small` (24px height), `medium` (32px height).
- **Colors**: Supports `primary`, `secondary`, `info`, `success`, `warning`, `error`, and `default`.
- **Border Radius**: Chips have a `8px` border radius for medium size and `6px` for small size, providing a rounded appearance.
- **Typography**: Text has a `fontWeight` of 600 for a medium-bold look.
- **Transitions**: Smooth transitions for background color and border color changes.

## Color Usage

The component uses the following color palette from the design system:

- **Primary**:
  - `main`: `#6941C6` (default background for `filled`, border/text for `outlined`)
  - `lighter`: `#EDE2FE` (background for `soft` variant)
  - `dark`: `#7E22CE` (hover background for `filled`, avatar background)
  - `contrastText`: `#FFFFFF` (text color for `filled`, avatar color)
- **Secondary**:
  - `main`: `#647ACF`
  - `lighter`: `#EDE9FE`
  - `dark`: `#4361A0`
  - `contrastText`: `#FFFFFF`
- **Info**:
  - `main`: `#36BDF1`
  - `lighter`: `#E0F2FE`
  - `dark`: `#0284C7`
  - `contrastText`: `#FFFFFF`
- **Success**:
  - `main`: `#22C55E`
  - `lighter`: `#DCFCE7`
  - `dark`: `#166534`
  - `contrastText`: `#FFFFFF`
- **Warning**:
  - `main`: `#F59E0B`
  - `lighter`: `#FEF3C7`
  - `dark`: `#92400E`
  - `contrastText`: `#1C252E`
- **Error**:
  - `main`: `#EF4444`
  - `lighter`: `#FEE4E2`
  - `dark`: `#991B1B`
  - `contrastText`: `#FFFFFF`
- **Grey** (used for disabled states and `default` color):
  - `100`: `#F9FAFB` (default `soft` background)
  - `200`: `#F4F6F8` (hover background for `soft`, disabled background)
  - `300`: `#DFE3E8` (disabled border)
  - `500`: `#919EAB` (disabled text color)
  - `800`: `#1C252E` (default text for `default` color)
  - `900`: `#141A21` (hover background for `default` in `filled`)

### Default Color

The default color for the chip is set to `primary` in the `defaultProps`. This means that if no `color` prop is specified, the chip will use the `primary` color palette, with the `main` color (`#6941C6`) applied as the background for `filled` chips, or as the border/text color for `outlined` chips.

### Disabled State

- **Background**: `grey[200]` (`#F4F6F8`) for all variants.
- **Text**: `grey[500]` (`#919EAB`) for all variants.
- **Border**: `grey[300]` (`#DFE3E8`) for `outlined` variant.
- **Avatar**: Background is `grey[300]`, color is `grey[500]`.

## Variants

### Filled

- **Background**: Uses the `main` color of the specified color (e.g., `primary.main`).
- **Text**: Uses the `contrastText` color (e.g., `primary.contrastText`).
- **Hover**: Changes the background to the `dark` shade (e.g., `primary.dark`).
- **Avatar**: Background is `dark`, color is `contrastText`.
- **Disabled**: Background is `grey[200]`, text is `grey[500]`.

### Outlined

- **Border**: Uses the `main` color with a 1px width.
- **Text**: Matches the `main` color.
- **Background**: Transparent.
- **Hover**: Adds a background of `alpha(lighter, 0.5)` (e.g., `alpha(primary.lighter, 0.5)`), border changes to the `dark` shade.
- **Avatar**: Background is `dark`, color is `contrastText`.
- **Disabled**: Border and text use `grey[500]`, background is `grey[200]`.

### Soft

- **Background**: Uses the `lighter` shade of the specified color (e.g., `primary.lighter`).
- **Text**: Uses the `main` color (e.g., `primary.main`).
- **Hover**: Changes the background to `alpha(main, 0.16)`.
- **Avatar**: Background is `dark`, color is `contrastText`.
- **Disabled**: Background is `grey[200]`, text is `grey[500]`.

## Usage Example

```jsx
import { Chip } from '@mui/material';

<Chip label="Primary Filled" variant="filled" color="primary" />
<Chip label="Secondary Outlined" variant="outlined" color="secondary" />
<Chip label="Success Soft" variant="soft" color="success" />
<Chip label="Default" color="default" />
<Chip label="Disabled" disabled />
<Chip label="Small" size="small" />
```
