import path from 'path';
import checker from 'vite-plugin-checker';
import { loadEnv, defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';

const PORT = 8081;

const env = loadEnv('all', process.cwd());

export default defineConfig({
  base: env.VITE_BASE_PATH,
  plugins: [
    react(),
    checker({
      typescript: true,
      eslint: {
        lintCommand: 'eslint "./src/**/*.{js,jsx,ts,tsx}"',
      },
      overlay: {
        position: 'tl',
        initialIsOpen: false,
      },
    }),
  ],
  resolve: {
    alias: [
      {
        find: /^~(.+)/,
        replacement: path.join(process.cwd(), 'node_modules/$1'),
      },
      {
        find: /^src(.+)/,
        replacement: path.join(process.cwd(), 'src/$1'),
      },
    ],
  },
  server: { port: PORT, host: true },
  preview: { port: PORT, host: true },
  build: {
    chunkSizeWarningLimit: 2000, // Optional: Adjust the limit as per your project's requirements
  },
  optimizeDeps: {
    exclude: [
      // Add dependencies that are incompatible with the Vite optimizer
      '@mui/material/styles/createTypography',
      '@mui/system',
      '@emotion/react/jsx-runtime',
    ],
  },
});
