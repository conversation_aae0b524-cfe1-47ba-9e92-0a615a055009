export default {
  testEnvironment: 'jsdom', // Use jsdom for browser-like environment
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  transform: {
    '^.+\\.(t|j)sx?$': '@swc/jest', // Use SWC for transforming JS/TS files
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1', // Adjust if you use path aliases
    '^src/(.*)$': '<rootDir>/src/$1', // add this line for Jest to resolve 'src/' imports
  },
  testMatch: ['**/?(*.)+(spec|test).[jt]s?(x)'],
};
