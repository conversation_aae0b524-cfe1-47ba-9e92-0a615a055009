{"name": "nex-mov", "author": "Nex<PERSON>ov", "version": "0.0.1", "description": "Vite Starter & TypeScript", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "tsc && vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:host": "vite --host", "test": "jest", "prepare": "husky", "lint-staged": "lint-staged", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/barlow": "^5.0.13", "@fontsource/dm-sans": "^5.0.21", "@fontsource/inter": "^5.0.18", "@fontsource/nunito-sans": "^5.0.13", "@fontsource/public-sans": "^5.0.18", "@hookform/resolvers": "^3.6.0", "@iconify/react": "^5.0.1", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.20", "@mui/x-data-grid": "^7.7.0", "@mui/x-date-pickers": "^7.7.0", "@mui/x-tree-view": "^7.7.0", "@radix-ui/react-scroll-area": "^1.2.3", "axios": "^1.7.2", "dayjs": "^1.11.11", "formik": "^2.4.6", "notistack": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-quill": "^2.0.0", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@swc/jest": "^0.2.37", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-perfectionist": "^2.11.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.0", "prettier": "^3.3.2", "ts-jest": "^29.2.6", "typescript": "^5.4.5", "vite": "^5.3.0", "vite-plugin-checker": "^0.6.4"}}