## Prerequisites

- Node.js 20.x (Recommended)

## Installation

**Using Yarn (Recommended)**

```sh
yarn install
yarn dev
```

**Using Npm**

```sh
npm i
npm run dev
```

## Build

```sh
yarn build
# or
npm run build
```

## Mock server

By default we provide demo data from : `https://api-dev-minimal-[version].vercel.app`

To set up your local server:

- **Guide:** [https://docs.minimals.cc/mock-server](https://docs.minimals.cc/mock-server).

- **Resource:** [Download](https://www.dropbox.com/sh/6ojn099upi105tf/AACpmlqrNUacwbBfVdtt2t6va?dl=0).

## Full version

- Create React App ([migrate to CRA](https://docs.minimals.cc/migrate-to-cra/)).
- Next.js
- Vite.js

## Starter version

- To remove unnecessary components. This is a simplified version ([https://starter.minimals.cc/](https://starter.minimals.cc/))
- Good to start a new project. You can copy components from the full version.
- Make sure to install the dependencies exactly as compared to the full version.

---

**NOTE:**
_When copying folders remember to also copy hidden files like .env. This is important because .env files often contain environment variables that are crucial for the application to run correctly._

## Theme Settings

### Colors.json

```
no bro just change these json to a readme me docs {
  "primary": {
    "25": "#F5F0FF",    // Lighter shade from Brand palette
    "50": "#EDE2FE",    // Light shade from Brand palette
    "100": "#DDC3FD",   // Adjusted for progression
    "200": "#C68EFF",   // Light from your current secondary (adjusted)
    "300": "#A855F7",   // Main progression
    "400": "#9333EA",   // Main from Brand palette
    "500": "#7E22CE",   // Main (darkened slightly)
    "600": "#6B21A8",   // Dark
    "700": "#581C87",   // Darker
    "800": "#3B0764",   // Darker shade
    "900": "#2E065F",   // Darkest
    "950": "#1E034E",   // Deepest shade
    "contrastText": "#FFFFFF"
  },
  "secondary": {
    "25": "#F5F3FF",    // Lighter from Gray neutral (adjusted)
    "50": "#EDE9FE",    // Light from Gray neutral
    "100": "#DDDCF7",   // Adjusted
    "200": "#C7D2FE",   // Light progression
    "300": "#A3BFFA",   // Info light influence
    "400": "#7C91D1",   // Main progression
    "500": "#647ACF",   // Main (adjusted from info)
    "600": "#4361A0",   // Dark
    "700": "#2E4074",   // Darker
    "800": "#1F2A44",   // Darkest
    "900": "#151A30",   // Deepest
    "950": "#0F1322",   // Deepest shade
    "contrastText": "#FFFFFF"
  },
  "info": {
    "25": "#F0F9FF",    // Lighter from Gray cool
    "50": "#E0F2FE",    // Light from Gray cool
    "100": "#B9E2FC",   // Adjusted
    "200": "#7FD0F7",   // Light progression
    "300": "#36BDF1",   // Main progression
    "400": "#0EA5E9",   // Main from design system
    "500": "#0284C7",   // Main (darkened)
    "600": "#075985",   // Dark
    "700": "#0E4A6E",   // Darker
    "800": "#0F3753",   // Darkest
    "900": "#0A253F",   // Deepest
    "950": "#071D32",   // Deepest shade
    "contrastText": "#FFFFFF"
  },
  "success": {
    "25": "#F0FDF4",    // Lighter from Success
    "50": "#DCFCE7",    // Light from Success
    "100": "#BBF7D0",   // Adjusted
    "200": "#86EFAC",   // Light progression
    "300": "#4ADE80",   // Main progression
    "400": "#22C55E",   // Main from design system
    "500": "#16A34A",   // Main (darkened)
    "600": "#166534",   // Dark
    "700": "#14532D",   // Darker
    "800": "#0F4223",   // Darkest
    "900": "#0A2E1B",   // Deepest
    "950": "#052E16",   // Deepest shade
    "contrastText": "#FFFFFF"
  },
  "warning": {
    "25": "#FFFBEB",    // Lighter from Warning
    "50": "#FEF3C7",    // Light from Warning
    "100": "#FDE68A",   // Adjusted
    "200": "#FCD34D",   // Light progression
    "300": "#FBBF24",   // Main progression
    "400": "#F59E0B",   // Main from design system
    "500": "#D97706",   // Main (darkened)
    "600": "#92400E",   // Dark
    "700": "#78350F",   // Darker
    "800": "#451A03",   // Darkest
    "900": "#3B1A03",   // Deepest
    "950": "#2C1302",   // Deepest shade
    "contrastText": "#1C252E"
  },
  "error": {
    "25": "#FEF2F2",    // Lighter from Error
    "50": "#FEE4E2",    // Light from Error
    "100": "#FECDD3",   // Adjusted
    "200": "#FDA4AF",   // Light progression
    "300": "#F87171",   // Main progression
    "400": "#EF4444",   // Main from design system
    "500": "#DC2626",   // Main (darkened)
    "600": "#991B1B",   // Dark
    "700": "#7F1D1D",   // Darker
    "800": "#4A0F0F",   // Darkest
    "900": "#3F0D0D",   // Deepest
    "950": "#2F0A0A",   // Deepest shade
    "contrastText": "#FFFFFF"
  },
  "grey": {
    "50": "#FCFDFD",    // From design system Gray (light mode)
    "100": "#F9FAFB",
    "200": "#F4F6F8",
    "300": "#DFE3E8",
    "400": "#C4CDD5",
    "500": "#919EAB",
    "600": "#637381",
    "700": "#454F5B",
    "800": "#1C252E",
    "900": "#141A21"
  },
  "common": {
    "black": "#000000",
    "white": "#FFFFFF"
  }
}

```
